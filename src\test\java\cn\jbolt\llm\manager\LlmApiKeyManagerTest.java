package cn.jbolt.llm.manager;

import cn.jbolt.llm.service.LlmService;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * LLM API密钥管理器测试类
 * 演示多API密钥轮询和负载均衡功能
 */
public class LlmApiKeyManagerTest {
    
    private LlmApiKeyManager keyManager;
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
        keyManager = LlmApiKeyManager.me();
        // 重新加载配置
        keyManager.reload();
    }
    
    /**
     * 测试1：基本功能测试
     */
    @Test
    public void testBasicFunctionality() {
        System.out.println("=== 测试1：基本功能测试 ===");
        
        try {
            // 测试获取可用API密钥
            System.out.println("步骤1: 获取Gemini提供商的可用API密钥");
            LlmApiKeyManager.ApiKeyInfo keyInfo = keyManager.getAvailableApiKey("gemini");
            
            if (keyInfo != null) {
                System.out.println("✓ 成功获取API密钥");
                System.out.println("  提供商: " + keyInfo.getProviderName());
                System.out.println("  密钥: " + maskApiKey(keyInfo.getApiKey()));
                System.out.println("  频率限制: " + keyInfo.getRateLimitPerMinute() + "/分钟");
                System.out.println("  优先级: " + keyInfo.getPriority());
            } else {
                System.out.println("✗ 未找到可用的API密钥");
            }
            
            // 测试获取提供商的所有密钥
            System.out.println("\n步骤2: 获取Gemini提供商的所有API密钥");
            List<LlmApiKeyManager.ApiKeyInfo> allKeys = keyManager.getProviderKeys("gemini");
            System.out.println("总共有 " + allKeys.size() + " 个API密钥:");
            for (int i = 0; i < allKeys.size(); i++) {
                LlmApiKeyManager.ApiKeyInfo key = allKeys.get(i);
                System.out.println("  密钥" + (i+1) + ": " + maskApiKey(key.getApiKey()) + 
                                 " (限制: " + key.getRateLimitPerMinute() + "/分钟, 优先级: " + key.getPriority() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("基本功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("基本功能测试完成\n");
    }
    
    /**
     * 测试2：轮询机制测试
     */
    @Test
    public void testRoundRobinMechanism() {
        System.out.println("=== 测试2：轮询机制测试 ===");
        
        try {
            System.out.println("连续获取10次API密钥，观察轮询效果:");
            
            for (int i = 1; i <= 10; i++) {
                LlmApiKeyManager.ApiKeyInfo keyInfo = keyManager.getAvailableApiKey("gemini");
                if (keyInfo != null) {
                    System.out.println("第" + i + "次: " + maskApiKey(keyInfo.getApiKey()));
                } else {
                    System.out.println("第" + i + "次: 无可用密钥");
                }
                
                // 短暂等待
                Thread.sleep(100);
            }
            
        } catch (Exception e) {
            System.err.println("轮询机制测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("轮询机制测试完成\n");
    }
    
    /**
     * 测试3：频率限制测试
     */
    @Test
    public void testRateLimiting() {
        System.out.println("=== 测试3：频率限制测试 ===");
        
        try {
            System.out.println("快速连续请求，测试频率限制:");
            
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 1; i <= 20; i++) {
                LlmApiKeyManager.ApiKeyInfo keyInfo = keyManager.getAvailableApiKey("gemini");
                if (keyInfo != null) {
                    successCount++;
                    System.out.println("第" + i + "次: ✓ " + maskApiKey(keyInfo.getApiKey()));
                } else {
                    failCount++;
                    System.out.println("第" + i + "次: ✗ 无可用密钥（可能达到频率限制）");
                }
                
                // 很短的等待时间，模拟快速请求
                Thread.sleep(50);
            }
            
            System.out.println("测试结果: 成功 " + successCount + " 次, 失败 " + failCount + " 次");
            
        } catch (Exception e) {
            System.err.println("频率限制测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("频率限制测试完成\n");
    }
    
    /**
     * 测试4：并发请求测试
     */
    @Test
    public void testConcurrentRequests() {
        System.out.println("=== 测试4：并发请求测试 ===");
        
        try {
            int threadCount = 5;
            int requestsPerThread = 10;
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            CountDownLatch latch = new CountDownLatch(threadCount);
            
            System.out.println("启动 " + threadCount + " 个线程，每个线程发起 " + requestsPerThread + " 个请求");
            
            for (int t = 0; t < threadCount; t++) {
                final int threadId = t + 1;
                executor.submit(() -> {
                    try {
                        int success = 0;
                        int fail = 0;
                        
                        for (int i = 1; i <= requestsPerThread; i++) {
                            LlmApiKeyManager.ApiKeyInfo keyInfo = keyManager.getAvailableApiKey("gemini");
                            if (keyInfo != null) {
                                success++;
                                System.out.println("线程" + threadId + "-请求" + i + ": ✓ " + maskApiKey(keyInfo.getApiKey()));
                            } else {
                                fail++;
                                System.out.println("线程" + threadId + "-请求" + i + ": ✗ 无可用密钥");
                            }
                            
                            Thread.sleep(100); // 模拟处理时间
                        }
                        
                        System.out.println("线程" + threadId + " 完成: 成功 " + success + ", 失败 " + fail);
                        
                    } catch (Exception e) {
                        System.err.println("线程" + threadId + " 异常: " + e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            // 等待所有线程完成
            latch.await(30, TimeUnit.SECONDS);
            executor.shutdown();
            
        } catch (Exception e) {
            System.err.println("并发请求测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("并发请求测试完成\n");
    }
    
    /**
     * 测试5：使用统计查看
     */
    @Test
    public void testUsageStatistics() {
        System.out.println("=== 测试5：使用统计查看 ===");
        
        try {
            // 先发起一些请求
            System.out.println("发起一些请求以生成统计数据...");
            for (int i = 0; i < 5; i++) {
                keyManager.getAvailableApiKey("gemini");
                Thread.sleep(200);
            }
            
            // 查看统计信息
            System.out.println("\nAPI密钥使用统计:");
            Map<String, LlmApiKeyManager.ApiKeyUsage> stats = keyManager.getUsageStatistics();
            
            for (Map.Entry<String, LlmApiKeyManager.ApiKeyUsage> entry : stats.entrySet()) {
                LlmApiKeyManager.ApiKeyUsage usage = entry.getValue();
                System.out.println("密钥: " + maskApiKey(usage.getApiKey()));
                System.out.println("  当前分钟请求数: " + usage.getCurrentMinuteRequests());
                System.out.println("  总请求数: " + usage.getTotalRequests());
                System.out.println("  最后请求时间: " + (usage.getLastRequestTime() > 0 ? 
                    new java.util.Date(usage.getLastRequestTime()) : "无"));
                System.out.println("  是否被阻塞: " + usage.isBlocked());
                System.out.println();
            }
            
        } catch (Exception e) {
            System.err.println("使用统计测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("使用统计测试完成\n");
    }
    
    /**
     * 测试6：实际LLM调用测试
     */
    @Test
    public void testActualLlmCall() {
        System.out.println("=== 测试6：实际LLM调用测试 ===");
        
        try {
            System.out.println("使用负载均衡的API密钥调用LLM服务...");
            
            String response = LlmService.me().callLlm(
                "gemini", 
                "gemini-2.5-pro", 
                "请简单回答：你好，这是一个API密钥负载均衡测试。"
            );
            
            if (response != null && !response.isEmpty()) {
                System.out.println("✓ LLM调用成功");
                System.out.println("响应长度: " + response.length() + " 字符");
                System.out.println("响应预览: " + response.substring(0, Math.min(100, response.length())) + "...");
            } else {
                System.out.println("✗ LLM调用失败或返回空响应");
            }
            
        } catch (Exception e) {
            System.err.println("实际LLM调用测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("实际LLM调用测试完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("LLM API密钥管理器功能测试");
        System.out.println("========================================");
        
        testBasicFunctionality();
        testRoundRobinMechanism();
        testRateLimiting();
        testConcurrentRequests();
        testUsageStatistics();
        testActualLlmCall();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
    }
    
    /**
     * 掩码显示API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
