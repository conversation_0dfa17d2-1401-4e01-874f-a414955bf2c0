#database config
db_name = stonecrm
db_schema = stonecrm
jdbc_url = **********************************************************************************************************************************************************************************************************************************************************************************************************************************
user = stonecrm
password = Stonecrm@2025
#是否为加密账号和密码
is_encrypted = false
id_gen_mode = snowflake
#force_cast_all_id_gen_mode = snowflake
model_package = cn.jbolt.core.model,cn.jbolt.common.model