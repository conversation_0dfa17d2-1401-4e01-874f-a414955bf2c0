package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 大模型提供商表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseLlmProvider<M extends BaseLlmProvider<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**主键ID*/
    public static final String ID = "id";
    /**厂商名称*/
    public static final String NAME = "name";
    /**API基础URL*/
    public static final String API_BASE_URL = "api_base_url";
    /**API密钥*/
    public static final String API_KEY = "api_key";
    /**API密钥(可选)*/
    public static final String API_SECRET = "api_secret";
    /**API类型(openai/custom)*/
    public static final String API_TYPE = "api_type";
    /**适配器类名(非OpenAI兼容时需要)*/
    public static final String ADAPTER_CLASS = "adapter_class";
    /**请求超时时间(秒)*/
    public static final String REQUEST_TIMEOUT = "request_timeout";
    /**默认最大token数*/
    public static final String MAX_TOKENS = "max_tokens";
    /**默认模型标识*/
    public static final String DEFAULT_MODEL = "default_model";
    /**状态(0-禁用,1-启用)*/
    public static final String STATUS = "status";
    /**优先级(数字越小优先级越高)*/
    public static final String PRIORITY = "priority";
    /**备注*/
    public static final String REMARK = "remark";
    /**每分钟请求限制数量*/
    public static final String RATE_LIMIT_PER_MINUTE = "rate_limit_per_minute";
    /**创建时间*/
    public static final String CREATE_TIME = "create_time";
    /**更新时间*/
    public static final String UPDATE_TIME = "update_time";
	/**
	 * 主键ID
	 */
	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	/**
	 * 主键ID
	 */
	@JBoltField(name="id" ,columnName="id",type="Long", remark="主键ID", required=true, maxLength=19, fixed=0, order=1)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getId() {
		return getLong("id");
	}

	/**
	 * 厂商名称
	 */
	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	/**
	 * 厂商名称
	 */
	@JBoltField(name="name" ,columnName="name",type="String", remark="厂商名称", required=true, maxLength=100, fixed=0, order=2)
	public java.lang.String getName() {
		return getStr("name");
	}

	/**
	 * API基础URL
	 */
	public M setApiBaseUrl(java.lang.String apiBaseUrl) {
		set("api_base_url", apiBaseUrl);
		return (M)this;
	}
	
	/**
	 * API基础URL
	 */
	@JBoltField(name="apiBaseUrl" ,columnName="api_base_url",type="String", remark="API基础URL", required=true, maxLength=255, fixed=0, order=3)
	public java.lang.String getApiBaseUrl() {
		return getStr("api_base_url");
	}

	/**
	 * API密钥
	 */
	public M setApiKey(java.lang.String apiKey) {
		set("api_key", apiKey);
		return (M)this;
	}
	
	/**
	 * API密钥
	 */
	@JBoltField(name="apiKey" ,columnName="api_key",type="String", remark="API密钥", required=true, maxLength=255, fixed=0, order=4)
	public java.lang.String getApiKey() {
		return getStr("api_key");
	}

	/**
	 * API密钥(可选)
	 */
	public M setApiSecret(java.lang.String apiSecret) {
		set("api_secret", apiSecret);
		return (M)this;
	}
	
	/**
	 * API密钥(可选)
	 */
	@JBoltField(name="apiSecret" ,columnName="api_secret",type="String", remark="API密钥(可选)", required=false, maxLength=255, fixed=0, order=5)
	public java.lang.String getApiSecret() {
		return getStr("api_secret");
	}

	/**
	 * API类型(openai/custom)
	 */
	public M setApiType(java.lang.String apiType) {
		set("api_type", apiType);
		return (M)this;
	}
	
	/**
	 * API类型(openai/custom)
	 */
	@JBoltField(name="apiType" ,columnName="api_type",type="String", remark="API类型(openai/custom)", required=true, maxLength=50, fixed=0, order=6)
	public java.lang.String getApiType() {
		return getStr("api_type");
	}

	/**
	 * 适配器类名(非OpenAI兼容时需要)
	 */
	public M setAdapterClass(java.lang.String adapterClass) {
		set("adapter_class", adapterClass);
		return (M)this;
	}
	
	/**
	 * 适配器类名(非OpenAI兼容时需要)
	 */
	@JBoltField(name="adapterClass" ,columnName="adapter_class",type="String", remark="适配器类名(非OpenAI兼容时需要)", required=false, maxLength=255, fixed=0, order=7)
	public java.lang.String getAdapterClass() {
		return getStr("adapter_class");
	}

	/**
	 * 请求超时时间(秒)
	 */
	public M setRequestTimeout(java.lang.Integer requestTimeout) {
		set("request_timeout", requestTimeout);
		return (M)this;
	}
	
	/**
	 * 请求超时时间(秒)
	 */
	@JBoltField(name="requestTimeout" ,columnName="request_timeout",type="Integer", remark="请求超时时间(秒)", required=false, maxLength=10, fixed=0, order=8)
	public java.lang.Integer getRequestTimeout() {
		return getInt("request_timeout");
	}

	/**
	 * 默认最大token数
	 */
	public M setMaxTokens(java.lang.Integer maxTokens) {
		set("max_tokens", maxTokens);
		return (M)this;
	}
	
	/**
	 * 默认最大token数
	 */
	@JBoltField(name="maxTokens" ,columnName="max_tokens",type="Integer", remark="默认最大token数", required=false, maxLength=10, fixed=0, order=9)
	public java.lang.Integer getMaxTokens() {
		return getInt("max_tokens");
	}

	/**
	 * 默认模型标识
	 */
	public M setDefaultModel(java.lang.String defaultModel) {
		set("default_model", defaultModel);
		return (M)this;
	}
	
	/**
	 * 默认模型标识
	 */
	@JBoltField(name="defaultModel" ,columnName="default_model",type="String", remark="默认模型标识", required=false, maxLength=100, fixed=0, order=10)
	public java.lang.String getDefaultModel() {
		return getStr("default_model");
	}

	/**
	 * 状态(0-禁用,1-启用)
	 */
	public M setStatus(java.lang.Boolean status) {
		set("status", status);
		return (M)this;
	}
	
	/**
	 * 状态(0-禁用,1-启用)
	 */
	@JBoltField(name="status" ,columnName="status",type="Boolean", remark="状态(0-禁用,1-启用)", required=false, maxLength=1, fixed=0, order=11)
	public java.lang.Boolean getStatus() {
		return getBoolean("status");
	}

	/**
	 * 优先级(数字越小优先级越高)
	 */
	public M setPriority(java.lang.Integer priority) {
		set("priority", priority);
		return (M)this;
	}
	
	/**
	 * 优先级(数字越小优先级越高)
	 */
	@JBoltField(name="priority" ,columnName="priority",type="Integer", remark="优先级(数字越小优先级越高)", required=false, maxLength=10, fixed=0, order=12)
	public java.lang.Integer getPriority() {
		return getInt("priority");
	}

	/**
	 * 备注
	 */
	public M setRemark(java.lang.String remark) {
		set("remark", remark);
		return (M)this;
	}
	
	/**
	 * 备注
	 */
	@JBoltField(name="remark" ,columnName="remark",type="String", remark="备注", required=false, maxLength=500, fixed=0, order=13)
	public java.lang.String getRemark() {
		return getStr("remark");
	}

	/**
	 * 每分钟请求限制数量
	 */
	public M setRateLimitPerMinute(java.lang.Integer rateLimitPerMinute) {
		set("rate_limit_per_minute", rateLimitPerMinute);
		return (M)this;
	}

	/**
	 * 每分钟请求限制数量
	 */
	@JBoltField(name="rateLimitPerMinute" ,columnName="rate_limit_per_minute",type="Integer", remark="每分钟请求限制数量", required=false, maxLength=10, fixed=0, order=14)
	public java.lang.Integer getRateLimitPerMinute() {
		return getInt("rate_limit_per_minute");
	}

	/**
	 * 创建时间
	 */
	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	/**
	 * 创建时间
	 */
	@JBoltField(name="createTime" ,columnName="create_time",type="Date", remark="创建时间", required=true, maxLength=19, fixed=0, order=15)
	public java.util.Date getCreateTime() {
		return getDate("create_time");
	}

	/**
	 * 更新时间
	 */
	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}

	/**
	 * 更新时间
	 */
	@JBoltField(name="updateTime" ,columnName="update_time",type="Date", remark="更新时间", required=true, maxLength=19, fixed=0, order=16)
	public java.util.Date getUpdateTime() {
		return getDate("update_time");
	}

}

