package cn.jbolt.llm.test;

import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.llm.service.LlmService;
import cn.jbolt.llm.util.MultimodalLlmUtil;
import cn.jbolt.llm.adapter.MultimodalLlmAdapter;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.tx.Tx;
import cn.jbolt.core.permission.UnCheck;

import java.util.Arrays;
import java.util.List;

/**
 * 多模态LLM测试控制器
 * 用于测试各种大模型的多模态功能
 * 
 * @author: 系统管理员
 * @date: 2025-01-15
 */
@UnCheck
@Path(value = "/test/multimodal", viewPath = "/_view/test/multimodal")
public class MultimodalTestController extends JBoltBaseController {

    /**
     * 测试页面
     */
    public void index() {
        render("index.html");
    }

    /**
     * 测试文本对话
     */
    public void testText() {
        String provider = get("provider", "openai");
        String model = get("model", "gpt-3.5-turbo");
        String prompt = get("prompt", "你好，请介绍一下自己");

        try {
            String response = LlmService.me().callLlm(provider, model, prompt);
            renderJson(Ret.ok("msg", "测试成功").set("response", response));
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "测试失败：" + e.getMessage()));
        }
    }

    /**
     * 测试图片理解
     */
    public void testImage() {
        String provider = get("provider", "claude");
        String model = get("model", "claude-3-haiku-20240307");
        String prompt = get("prompt", "请描述这张图片的内容");
        String imagePath = get("imagePath");

        if (imagePath == null || imagePath.isEmpty()) {
            renderJson(Ret.fail("msg", "请提供图片路径"));
            return;
        }

        try {
            List<String> imagePaths = Arrays.asList(imagePath);
            String response = LlmService.me().callLlmWithImages(provider, model, prompt, imagePaths);
            renderJson(Ret.ok("msg", "测试成功").set("response", response));
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "测试失败：" + e.getMessage()));
        }
    }

    /**
     * 测试视频理解（仅Gemini支持）
     */
    public void testVideo() {
        String provider = get("provider", "gemini");
        String model = get("model", "gemini-1.5-pro");
        String prompt = get("prompt", "请分析这个视频的内容");
        String videoPath = get("videoPath");

        if (videoPath == null || videoPath.isEmpty()) {
            renderJson(Ret.fail("msg", "请提供视频路径"));
            return;
        }

        try {
            List<String> videoPaths = Arrays.asList(videoPath);
            String response = LlmService.me().callLlmWithImages(provider, model, prompt, videoPaths);
            renderJson(Ret.ok("msg", "测试成功").set("response", response));
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "测试失败：" + e.getMessage()));
        }
    }

    /**
     * 测试音频理解（仅Gemini支持）
     */
    public void testAudio() {
        String provider = get("provider", "gemini");
        String model = get("model", "gemini-1.5-pro");
        String prompt = get("prompt", "请转录这段音频的内容");
        String audioPath = get("audioPath");

        if (audioPath == null || audioPath.isEmpty()) {
            renderJson(Ret.fail("msg", "请提供音频路径"));
            return;
        }

        try {
            List<String> audioPaths = Arrays.asList(audioPath);
            String response = LlmService.me().callLlmWithImages(provider, model, prompt, audioPaths);
            renderJson(Ret.ok("msg", "测试成功").set("response", response));
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "测试失败：" + e.getMessage()));
        }
    }

    /**
     * 获取支持的模态类型
     */
    public void getSupportedModalities() {
        String adapterClass = get("adapterClass");
        
        if (adapterClass == null || adapterClass.isEmpty()) {
            renderJson(Ret.fail("msg", "请提供适配器类名"));
            return;
        }

        try {
            List<MultimodalLlmAdapter.ModalityType> modalities = 
                MultimodalLlmUtil.getSupportedModalities(adapterClass);
            renderJson(Ret.ok("msg", "获取成功").set("modalities", modalities));
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "获取失败：" + e.getMessage()));
        }
    }

    /**
     * 批量测试所有提供商
     */
    public void batchTest() {
        String testPrompt = get("prompt", "Hello, this is a batch test.");
        
        // 定义要测试的提供商和模型
        String[][] testCases = {
            {"openai", "gpt-3.5-turbo"},
            {"claude", "claude-3-haiku-20240307"},
            {"gemini", "gemini-2.5-pro"},
            {"qwen", "qwen-turbo"},
            {"zhipu", "glm-4"},
            {"kimi", "moonshot-v1-8k"},
            {"doubao", "doubao-pro-4k"},
            {"deepseek", "deepseek-chat"},
            {"tencent", "hunyuan-lite"},
            {"baidu", "ernie-bot-turbo"}
        };

        List<Kv> results = new java.util.ArrayList<>();

        for (String[] testCase : testCases) {
            String provider = testCase[0];
            String model = testCase[1];
            
            try {
                long startTime = System.currentTimeMillis();
                String response = LlmService.me().callLlm(provider, model, testPrompt);
                long endTime = System.currentTimeMillis();
                
                results.add(Kv.by("provider", provider)
                    .set("model", model)
                    .set("status", "success")
                    .set("response", response)
                    .set("responseTime", endTime - startTime));
                    
            } catch (Exception e) {
                results.add(Kv.by("provider", provider)
                    .set("model", model)
                    .set("status", "error")
                    .set("error", e.getMessage())
                    .set("responseTime", -1));
            }
        }

        renderJson(Ret.ok("msg", "批量测试完成").set("results", results));
    }

    /**
     * 性能基准测试
     */
    public void benchmark() {
        String provider = get("provider", "openai");
        String model = get("model", "gpt-3.5-turbo");
        String prompt = get("prompt", "请用一句话介绍人工智能");
        int iterations = getInt("iterations", 5);

        List<Kv> results = new java.util.ArrayList<>();
        long totalTime = 0;
        int successCount = 0;

        for (int i = 0; i < iterations; i++) {
            try {
                long startTime = System.currentTimeMillis();
                String response = LlmService.me().callLlm(provider, model, prompt);
                long endTime = System.currentTimeMillis();
                long responseTime = endTime - startTime;
                
                totalTime += responseTime;
                successCount++;
                
                results.add(Kv.by("iteration", i + 1)
                    .set("status", "success")
                    .set("responseTime", responseTime)
                    .set("responseLength", response != null ? response.length() : 0));
                    
            } catch (Exception e) {
                results.add(Kv.by("iteration", i + 1)
                    .set("status", "error")
                    .set("error", e.getMessage())
                    .set("responseTime", -1));
            }
        }

        Kv summary = Kv.by("totalIterations", iterations)
            .set("successCount", successCount)
            .set("failureCount", iterations - successCount)
            .set("averageResponseTime", successCount > 0 ? totalTime / successCount : -1)
            .set("totalTime", totalTime);

        renderJson(Ret.ok("msg", "基准测试完成")
            .set("summary", summary)
            .set("details", results));
    }

    /**
     * 多模态能力检测
     */
    public void detectCapabilities() {
        String provider = get("provider");
        
        if (provider == null || provider.isEmpty()) {
            renderJson(Ret.fail("msg", "请提供提供商名称"));
            return;
        }

        try {
            // 根据提供商名称获取适配器类名
            String adapterClass = getAdapterClassByProvider(provider);
            
            if (adapterClass != null) {
                List<MultimodalLlmAdapter.ModalityType> modalities = 
                    MultimodalLlmUtil.getSupportedModalities(adapterClass);
                
                Kv capabilities = Kv.by("provider", provider)
                    .set("adapterClass", adapterClass)
                    .set("supportedModalities", modalities)
                    .set("supportsText", modalities.contains(MultimodalLlmAdapter.ModalityType.TEXT))
                    .set("supportsImage", modalities.contains(MultimodalLlmAdapter.ModalityType.IMAGE))
                    .set("supportsVideo", modalities.contains(MultimodalLlmAdapter.ModalityType.VIDEO))
                    .set("supportsAudio", modalities.contains(MultimodalLlmAdapter.ModalityType.AUDIO))
                    .set("supportsDocument", modalities.contains(MultimodalLlmAdapter.ModalityType.DOCUMENT));
                
                renderJson(Ret.ok("msg", "检测成功").set("capabilities", capabilities));
            } else {
                renderJson(Ret.fail("msg", "未找到对应的适配器"));
            }
            
        } catch (Exception e) {
            renderJson(Ret.fail("msg", "检测失败：" + e.getMessage()));
        }
    }

    /**
     * 根据提供商名称获取适配器类名
     */
    private String getAdapterClassByProvider(String provider) {
        switch (provider.toLowerCase()) {
            case "claude":
                return "cn.jbolt.llm.adapter.ClaudeAdapter";
            case "gemini":
                return "cn.jbolt.llm.adapter.GeminiAdapter";
            case "qwen":
                return "cn.jbolt.llm.adapter.QwenAdapter";
            case "zhipu":
                return "cn.jbolt.llm.adapter.ZhipuAdapter";
            case "kimi":
                return "cn.jbolt.llm.adapter.KimiAdapter";
            case "doubao":
                return "cn.jbolt.llm.adapter.DoubaoAdapter";
            case "deepseek":
                return "cn.jbolt.llm.adapter.DeepseekAdapter";
            case "tencent":
                return "cn.jbolt.llm.adapter.TencentAdapter";
            case "baidu":
                return "cn.jbolt.llm.adapter.BaiduAdapter";
            default:
                return null;
        }
    }
}