package cn.jbolt.llm.util;

import cn.jbolt.common.model.EmailTranslationConfig;
import okhttp3.OkHttpClient;
import com.jfinal.kit.LogKit;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.TimeUnit;

/**
 * 代理配置工具类
 */
public class ProxyConfigUtil {
    
    /**
     * 创建带代理配置的OkHttpClient
     */
    public static OkHttpClient createHttpClientWithProxy() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS);
        
        try {
            // 获取代理配置
            String proxyHost = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_HOST);
            String proxyPortStr = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_PORT);
            
            if (proxyHost != null && !proxyHost.trim().isEmpty() && 
                proxyPortStr != null && !proxyPortStr.trim().isEmpty()) {
                
                try {
                    int proxyPort = Integer.parseInt(proxyPortStr.trim());
                    
                    // 创建代理
                    Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost.trim(), proxyPort));
                    builder.proxy(proxy);
                    
                    LogKit.info("使用代理配置: " + proxyHost + ":" + proxyPort);
                    
                } catch (NumberFormatException e) {
                    LogKit.warn("代理端口配置无效: " + proxyPortStr + ", 将不使用代理");
                }
            } else {
                LogKit.debug("未配置代理，使用直连");
            }
            
        } catch (Exception e) {
            LogKit.warn("获取代理配置失败，使用直连: " + e.getMessage());
        }
        
        return builder.build();
    }
    
    /**
     * 检查代理配置是否有效
     */
    public static boolean isProxyConfigured() {
        try {
            String proxyHost = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_HOST);
            String proxyPortStr = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_PORT);
            
            if (proxyHost != null && !proxyHost.trim().isEmpty() && 
                proxyPortStr != null && !proxyPortStr.trim().isEmpty()) {
                
                try {
                    Integer.parseInt(proxyPortStr.trim());
                    return true;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取代理配置信息
     */
    public static String getProxyInfo() {
        try {
            String proxyHost = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_HOST);
            String proxyPortStr = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PROXY_PORT);
            
            if (proxyHost != null && !proxyHost.trim().isEmpty() && 
                proxyPortStr != null && !proxyPortStr.trim().isEmpty()) {
                
                try {
                    int proxyPort = Integer.parseInt(proxyPortStr.trim());
                    return proxyHost.trim() + ":" + proxyPort;
                } catch (NumberFormatException e) {
                    return "代理配置无效";
                }
            }
            
            return "未配置代理";
            
        } catch (Exception e) {
            return "获取代理配置失败";
        }
    }
    
    /**
     * 测试代理连接
     */
    public static boolean testProxyConnection() {
        if (!isProxyConfigured()) {
            return false;
        }
        
        try {
            OkHttpClient client = createHttpClientWithProxy();
            
            // 测试连接到Google（通过代理）
            okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://www.google.com")
                .head()
                .build();
            
            try (okhttp3.Response response = client.newCall(request).execute()) {
                return response.isSuccessful();
            }
            
        } catch (Exception e) {
            LogKit.warn("代理连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置代理配置
     */
    public static boolean setProxyConfig(String host, String port) {
        try {
            boolean success = true;
            
            if (host != null) {
                success &= EmailTranslationConfig.setConfigValue(
                    EmailTranslationConfig.PROXY_HOST, host.trim(), "设置代理主机");
            }
            
            if (port != null) {
                success &= EmailTranslationConfig.setConfigValue(
                    EmailTranslationConfig.PROXY_PORT, port.trim(), "设置代理端口");
            }
            
            return success;
            
        } catch (Exception e) {
            LogKit.error("设置代理配置失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清除代理配置
     */
    public static boolean clearProxyConfig() {
        try {
            boolean success = true;
            
            success &= EmailTranslationConfig.setConfigValue(
                EmailTranslationConfig.PROXY_HOST, "", "清除代理配置");
            success &= EmailTranslationConfig.setConfigValue(
                EmailTranslationConfig.PROXY_PORT, "", "清除代理配置");
            
            return success;
            
        } catch (Exception e) {
            LogKit.error("清除代理配置失败: " + e.getMessage());
            return false;
        }
    }
}
