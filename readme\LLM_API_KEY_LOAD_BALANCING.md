# LLM API密钥负载均衡和轮询系统

## 概述

本系统实现了智能的API密钥负载均衡和轮询机制，解决了单个API密钥频率限制的问题。支持：

- 🔄 **多API密钥轮询** - 自动在多个密钥间轮换
- ⚖️ **负载均衡** - 智能分配请求到可用密钥
- 🚦 **频率限制管理** - 自动跟踪和管理每个密钥的使用频率
- 🔒 **故障隔离** - 自动阻塞出错的密钥
- 📊 **使用统计** - 详细的密钥使用情况监控

## 快速开始

### 1. 数据库配置

执行数据库迁移脚本：

```sql
-- 执行 src/main/resources/sql/llm_api_key_enhancement.sql
source src/main/resources/sql/llm_api_key_enhancement.sql;
```

### 2. 配置多个API密钥

在 `llm_provider` 表中配置多个API密钥（用逗号分隔）：

```sql
-- 配置多个Gemini API密钥
UPDATE llm_provider SET 
  api_key = 'AIzaXXXXXXXXXXXXXXXX1,AIzaXXXXXXXXXXXXXXXX2,AIzaXXXXXXXXXXXXXXXX3',
  rate_limit_per_minute = 15
WHERE name = 'gemini';

-- 配置多个OpenAI API密钥
UPDATE llm_provider SET 
  api_key = 'sk-XXXXXXXXXXXXXXXX1,sk-XXXXXXXXXXXXXXXX2,sk-XXXXXXXXXXXXXXXX3',
  rate_limit_per_minute = 20
WHERE name = 'openai';
```

### 3. 使用方式

系统会自动使用负载均衡，无需修改现有代码：

```java
// 原有代码保持不变，系统自动使用负载均衡
String response = LlmService.me().callLlm(
    "gemini", 
    "gemini-2.5-pro", 
    "你好，这是一个测试请求"
);
```

## 详细配置

### 频率限制配置

每个提供商可以设置不同的频率限制：

| 提供商 | 默认限制 | 建议配置 |
|--------|----------|----------|
| Gemini | 15/分钟 | 15/分钟 |
| OpenAI | 20/分钟 | 20/分钟 |
| Claude | 10/分钟 | 10/分钟 |
| 通义千问 | 30/分钟 | 30/分钟 |
| 智谱AI | 20/分钟 | 20/分钟 |

### API密钥优先级

系统支持API密钥优先级设置：

```sql
-- 设置不同优先级的密钥（数字越小优先级越高）
UPDATE llm_provider SET 
  api_key = 'high_priority_key,medium_priority_key,low_priority_key',
  priority = '1,2,3'  -- 对应每个密钥的优先级
WHERE name = 'gemini';
```

## 核心功能

### 1. 智能轮询

```java
// 获取可用的API密钥（自动轮询）
LlmApiKeyManager.ApiKeyInfo keyInfo = LlmApiKeyManager.me().getAvailableApiKey("gemini");
```

### 2. 频率限制检查

系统自动检查每个密钥的使用频率：

- 维护每个密钥的请求时间队列
- 自动清理过期的请求记录
- 当达到频率限制时自动跳过该密钥

### 3. 故障处理

```java
// 标记密钥出错（自动阻塞一段时间）
LlmApiKeyManager.me().markApiKeyError(apiKey, 60 * 1000); // 阻塞1分钟
```

### 4. 使用统计

```java
// 获取所有密钥的使用统计
Map<String, LlmApiKeyManager.ApiKeyUsage> stats = LlmApiKeyManager.me().getUsageStatistics();

for (LlmApiKeyManager.ApiKeyUsage usage : stats.values()) {
    System.out.println("密钥: " + usage.getApiKey());
    System.out.println("当前分钟请求数: " + usage.getCurrentMinuteRequests());
    System.out.println("总请求数: " + usage.getTotalRequests());
    System.out.println("是否被阻塞: " + usage.isBlocked());
}
```

## 监控和管理

### 1. 查看密钥状态

```sql
-- 查看所有密钥的使用情况
SELECT * FROM v_llm_api_key_stats;
```

### 2. 重置被阻塞的密钥

```sql
-- 手动重置所有被阻塞的密钥
CALL ResetBlockedApiKeys();
```

### 3. 清理过期日志

```sql
-- 清理30天前的请求日志
CALL CleanupApiRequestLogs(30);
```

## 最佳实践

### 1. 密钥配置建议

- **多样化**: 使用不同账户的API密钥以提高可用性
- **备份**: 至少配置3个以上的密钥作为备份
- **监控**: 定期检查密钥的使用情况和有效性

### 2. 频率限制设置

- **保守设置**: 设置比官方限制稍低的值，留出缓冲空间
- **分级设置**: 重要业务使用高优先级密钥
- **动态调整**: 根据实际使用情况调整频率限制

### 3. 错误处理

```java
try {
    String response = LlmService.me().callLlm("gemini", "gemini-2.5-pro", prompt);
    // 处理成功响应
} catch (Exception e) {
    if (e.getMessage().contains("rate limit")) {
        // 所有密钥都达到频率限制，等待后重试
        Thread.sleep(60000);
        // 重试逻辑
    } else {
        // 其他错误处理
    }
}
```

## 性能优化

### 1. 连接池配置

```java
// 在应用启动时预加载密钥配置
LlmApiKeyManager.me().reload();
```

### 2. 缓存策略

- 密钥配置缓存在内存中，避免频繁数据库查询
- 使用统计信息缓存，减少计算开销

### 3. 并发处理

- 使用线程安全的数据结构
- 原子操作保证轮询索引的正确性

## 故障排除

### 常见问题

1. **所有密钥都不可用**
   - 检查密钥是否正确配置
   - 确认频率限制设置是否合理
   - 查看是否有密钥被错误阻塞

2. **轮询不均匀**
   - 检查密钥优先级设置
   - 确认所有密钥都是有效的

3. **频率限制不准确**
   - 检查系统时间是否正确
   - 确认频率限制配置是否符合API提供商要求

### 调试方法

```java
// 开启详细日志
LogKit.setLevel(Level.DEBUG);

// 查看密钥轮询情况
List<LlmApiKeyManager.ApiKeyInfo> keys = LlmApiKeyManager.me().getProviderKeys("gemini");
for (LlmApiKeyManager.ApiKeyInfo key : keys) {
    System.out.println("密钥: " + key.getApiKey() + ", 优先级: " + key.getPriority());
}
```

## 扩展功能

### 1. 自定义负载均衡策略

可以扩展 `LlmApiKeyManager` 实现自定义的负载均衡算法：

- 加权轮询
- 最少连接数
- 响应时间优先

### 2. 动态配置更新

支持运行时动态更新密钥配置：

```java
// 重新加载配置
LlmApiKeyManager.me().reload();
```

### 3. 监控集成

可以集成到监控系统中：

- Prometheus指标导出
- 告警规则配置
- 仪表板展示

## 总结

通过这个API密钥负载均衡系统，你可以：

- ✅ 突破单个密钥的频率限制
- ✅ 提高系统的可用性和稳定性
- ✅ 实现智能的请求分发
- ✅ 获得详细的使用统计和监控

系统设计为零侵入式，现有代码无需修改即可享受负载均衡的好处。
