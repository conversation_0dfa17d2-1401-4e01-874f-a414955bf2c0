# EmailTranslationPlugin Gemini修复说明

## 问题描述

在实施多API密钥轮询系统后，EmailTranslationPlugin的翻译功能失效，保存到数据库中的翻译字段全部为空。

## 问题原因

EmailTranslationPlugin中硬编码使用了"Kimi Moonshot"提供商和相关模型：
- `translateSubject()` 方法使用 `"Kimi Moonshot"` + `"kimi-latest"`
- `translateContent()` 方法使用 `"Kimi Moonshot"` + `"kimi-latest"`
- `translateSingleImage()` 方法使用 `"Kimi Moonshot"` + `"moonshot-v1-128k-vision-preview"`
- 批量处理方法也使用相同的提供商

在多密钥轮询系统中，如果"Kimi Moonshot"提供商不存在或配置有问题，就会导致翻译失败。

## 解决方案

将所有翻译方法改为使用Gemini 2.0 Flash Exp模型：

### 修改内容

1. **文本翻译方法**：
   ```java
   // 修改前
   String result = LlmService.me().callLlm("Kimi Moonshot", "kimi-latest", prompt);
   
   // 修改后
   String result = LlmService.me().callLlm("gemini", "gemini-2.0-flash-exp", prompt);
   ```

2. **图片翻译方法**：
   ```java
   // 修改前
   String result = LlmService.me().callLlmWithImages("Kimi Moonshot", "moonshot-v1-128k-vision-preview", prompt, imageSet);
   
   // 修改后
   String result = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", prompt, imageList);
   ```

3. **数据类型调整**：
   - 将图片路径参数从 `Set<String>` 改为 `List<String>`
   - 确保与LlmService接口兼容

### 修改的方法列表

1. `translateSubject()` - 翻译邮件标题
2. `translateContent()` - 翻译邮件内容  
3. `translateSingleImage()` - 翻译单张图片
4. `translateBatchImages()` - 批量翻译图片（两处调用）

## 为什么选择Gemini 2.0 Flash Exp

1. **多模态支持**：支持文本、图片、视频、音频等多种模态
2. **性能优秀**：响应速度快，翻译质量高
3. **成本效益**：相比其他模型更具成本优势
4. **系统兼容**：与现有的多密钥轮询系统完全兼容
5. **稳定可靠**：Google提供的稳定服务

## 测试验证

### 1. 运行测试类

```bash
# 运行完整测试
mvn test -Dtest=EmailTranslationPluginTest#testGeminiTranslation

# 或者运行交互式测试
java -cp ... cn.jbolt.mail.gpt.fetch.plugin.EmailTranslationPluginTest
```

### 2. 测试步骤

1. **验证LLM服务**：确认Gemini提供商配置正确
2. **测试文本翻译**：验证标题和内容翻译功能
3. **测试图片翻译**：验证图片内容识别和翻译
4. **检查数据库**：确认翻译结果正确保存

### 3. 预期结果

- 翻译功能正常工作
- 数据库中的翻译字段不再为空
- 支持文本和图片的混合翻译
- 翻译质量满足业务需求

## 配置要求

### 1. 确保Gemini提供商配置

```sql
-- 检查Gemini提供商配置
SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1;

-- 如果没有，需要添加配置
INSERT INTO llm_provider (name, api_base_url, api_key, api_type, adapter_class, default_model, rate_limit_per_minute, status, priority) 
VALUES ('gemini', 'https://generativelanguage.googleapis.com/v1beta/models/', 'your-gemini-api-key', 'custom', 'cn.jbolt.llm.adapter.GeminiAdapter', 'gemini-2.0-flash-exp', 15, 1, 1);
```

### 2. 配置多个API密钥（可选）

```sql
-- 配置多个Gemini API密钥以提高并发能力
UPDATE llm_provider SET 
  api_key = 'AIzaSyABC123...,AIzaSyDEF456...,AIzaSyGHI789...',
  rate_limit_per_minute = 15
WHERE name = 'gemini';
```

## 使用方法

### 1. 自动翻译

EmailTranslationPlugin会自动处理新收到的邮件：

```java
// 插件会自动调用以下方法
plugin.translateEmail(emailId, true); // 包含图片翻译
plugin.translateEmail(emailId, false); // 仅文本翻译
```

### 2. 手动翻译

可以手动触发特定邮件的翻译：

```java
EmailTranslationPlugin plugin = new EmailTranslationPlugin();
plugin.start();

// 翻译指定邮件
String result = plugin.translateEmail(emailId, true);

plugin.stop();
```

### 3. 单独翻译功能

```java
// 翻译标题
String titleResult = plugin.translateSingle(emailId, "subject", null, null, 
    "请翻译以下邮件标题", null, null);

// 翻译内容
String contentResult = plugin.translateSingle(emailId, "content", content, null,
    "请翻译以下邮件内容", null, null);
```

## 监控和维护

### 1. 检查翻译状态

```sql
-- 查看最近的翻译记录
SELECT et.*, em.subject as original_subject 
FROM email_translation et 
LEFT JOIN email_messages em ON et.email_id = em.id 
ORDER BY et.create_time DESC LIMIT 10;

-- 检查翻译失败的邮件
SELECT em.* FROM email_messages em 
LEFT JOIN email_translation et ON em.id = et.email_id 
WHERE et.id IS NULL AND em.sent_date > DATE_SUB(NOW(), INTERVAL 1 DAY);
```

### 2. 性能监控

通过API密钥统计页面监控Gemini的使用情况：
- 访问：`/admin/llm/apikey/stats`
- 查看Gemini提供商的请求统计
- 监控频率限制和成功率

## 故障排除

### 1. 翻译结果为空

- 检查Gemini API密钥是否有效
- 确认网络连接正常
- 查看日志中的错误信息

### 2. 频率限制问题

- 配置多个API密钥
- 调整频率限制设置
- 使用API密钥统计页面监控

### 3. 图片翻译失败

- 确认图片文件存在且可访问
- 检查图片格式是否支持
- 验证Gemini模型是否支持图片处理

## 总结

通过将EmailTranslationPlugin从Kimi Moonshot切换到Gemini 2.0 Flash Exp，解决了多密钥轮询系统下的翻译失效问题。新的配置具有更好的稳定性、性能和成本效益，同时完全兼容现有的系统架构。
