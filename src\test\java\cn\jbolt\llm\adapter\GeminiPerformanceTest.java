package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Gemini适配器性能测试
 * 测试转换性能、并发处理能力和内存使用情况
 */
public class GeminiPerformanceTest {
    
    private final GeminiAdapter adapter;
    
    public GeminiPerformanceTest() {
        this.adapter = new GeminiAdapter();
    }
    
    public static void main(String[] args) {
        GeminiPerformanceTest test = new GeminiPerformanceTest();
        
        System.out.println("Gemini适配器性能测试");
        System.out.println("==================\n");
        
        // 1. 请求转换性能测试
        test.testRequestConversionPerformance();
        
        // 2. 响应转换性能测试
        test.testResponseConversionPerformance();
        
        // 3. 大文本处理性能测试
        test.testLargeTextPerformance();
        
        // 4. 并发处理测试
        test.testConcurrentProcessing();
        
        // 5. 内存使用测试
        test.testMemoryUsage();
        
        // 6. 多模态处理性能测试
        test.testMultimodalPerformance();
        
        System.out.println("性能测试完成！");
    }
    
    /**
     * 测试1: 请求转换性能
     */
    public void testRequestConversionPerformance() {
        System.out.println("=== 测试1: 请求转换性能 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", "请翻译这段文本：Hello, how are you today?"));
        
        int iterations = 10000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            adapter.convertRequest("gemini-pro", messages);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        System.out.println("请求转换性能:");
        System.out.println("  总次数: " + iterations);
        System.out.println("  总时间: " + totalTime + "ms");
        System.out.println("  平均时间: " + String.format("%.2f", avgTime) + "ms/次");
        System.out.println("  吞吐量: " + String.format("%.0f", 1000.0 / avgTime) + "次/秒");
        System.out.println();
    }
    
    /**
     * 测试2: 响应转换性能
     */
    public void testResponseConversionPerformance() {
        System.out.println("=== 测试2: 响应转换性能 ===");
        
        String sampleResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"你好！今天天气怎么样？\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 15,\n" +
            "    \"candidatesTokenCount\": 10,\n" +
            "    \"totalTokenCount\": 25\n" +
            "  },\n" +
            "  \"responseId\": \"perf-test-123\"\n" +
            "}";
        
        int iterations = 10000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            adapter.convertResponse(sampleResponse);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        System.out.println("响应转换性能:");
        System.out.println("  总次数: " + iterations);
        System.out.println("  总时间: " + totalTime + "ms");
        System.out.println("  平均时间: " + String.format("%.2f", avgTime) + "ms/次");
        System.out.println("  吞吐量: " + String.format("%.0f", 1000.0 / avgTime) + "次/秒");
        System.out.println();
    }
    
    /**
     * 测试3: 大文本处理性能
     */
    public void testLargeTextPerformance() {
        System.out.println("=== 测试3: 大文本处理性能 ===");
        
        // 生成大文本
        StringBuilder largeText = new StringBuilder();
        String baseText = "这是一段用于性能测试的文本。";
        for (int i = 0; i < 1000; i++) {
            largeText.append(baseText).append("第").append(i).append("次重复。");
        }
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", "请翻译以下大段文本：" + largeText.toString()));
        
        int iterations = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            String request = adapter.convertRequest("gemini-pro", messages);
            // 模拟处理大响应
            String largeResponse = createLargeResponse(largeText.toString());
            adapter.convertResponse(largeResponse);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        System.out.println("大文本处理性能:");
        System.out.println("  文本长度: " + largeText.length() + "字符");
        System.out.println("  处理次数: " + iterations);
        System.out.println("  总时间: " + totalTime + "ms");
        System.out.println("  平均时间: " + String.format("%.2f", avgTime) + "ms/次");
        System.out.println();
    }
    
    /**
     * 测试4: 并发处理测试
     */
    public void testConcurrentProcessing() {
        System.out.println("=== 测试4: 并发处理测试 ===");
        
        int threadCount = 10;
        int tasksPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        List<Future<Long>> futures = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            Future<Long> future = executor.submit(() -> {
                long threadStartTime = System.currentTimeMillis();
                
                for (int j = 0; j < tasksPerThread; j++) {
                    List<Kv> messages = new ArrayList<>();
                    messages.add(Kv.by("role", "user").set("content", "线程" + threadId + "任务" + j + ": 请翻译Hello"));
                    
                    String request = adapter.convertRequest("gemini-pro", messages);
                    String response = createSampleResponse("你好");
                    adapter.convertResponse(response);
                }
                
                return System.currentTimeMillis() - threadStartTime;
            });
            futures.add(future);
        }
        
        try {
            long maxThreadTime = 0;
            for (Future<Long> future : futures) {
                long threadTime = future.get();
                maxThreadTime = Math.max(maxThreadTime, threadTime);
            }
            
            long totalTime = System.currentTimeMillis() - startTime;
            int totalTasks = threadCount * tasksPerThread;
            
            System.out.println("并发处理性能:");
            System.out.println("  线程数: " + threadCount);
            System.out.println("  每线程任务数: " + tasksPerThread);
            System.out.println("  总任务数: " + totalTasks);
            System.out.println("  总时间: " + totalTime + "ms");
            System.out.println("  最慢线程时间: " + maxThreadTime + "ms");
            System.out.println("  平均吞吐量: " + String.format("%.0f", (double) totalTasks * 1000 / totalTime) + "任务/秒");
            
        } catch (Exception e) {
            System.err.println("并发测试失败: " + e.getMessage());
        } finally {
            executor.shutdown();
            try {
                executor.awaitTermination(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println();
    }
    
    /**
     * 测试5: 内存使用测试
     */
    public void testMemoryUsage() {
        System.out.println("=== 测试5: 内存使用测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建大量对象进行转换
        List<String> results = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", "测试消息" + i));
            
            String request = adapter.convertRequest("gemini-pro", messages);
            String response = createSampleResponse("响应" + i);
            String converted = adapter.convertResponse(response);
            
            results.add(converted);
        }
        
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        System.out.println("内存使用情况:");
        System.out.println("  处理前内存: " + formatBytes(beforeMemory));
        System.out.println("  处理后内存: " + formatBytes(afterMemory));
        System.out.println("  内存增长: " + formatBytes(memoryUsed));
        System.out.println("  平均每次转换: " + formatBytes(memoryUsed / 1000));
        
        // 清理引用，测试内存释放
        results.clear();
        System.gc();
        long afterGcMemory = runtime.totalMemory() - runtime.freeMemory();
        
        System.out.println("  GC后内存: " + formatBytes(afterGcMemory));
        System.out.println("  内存释放: " + formatBytes(afterMemory - afterGcMemory));
        System.out.println();
    }
    
    /**
     * 测试6: 多模态处理性能
     */
    public void testMultimodalPerformance() {
        System.out.println("=== 测试6: 多模态处理性能 ===");
        
        List<String> imagePaths = Arrays.asList("image1.jpg", "image2.jpg", "image3.jpg");
        String prompt = "请分析这些图片";
        
        int iterations = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            Object parts = adapter.processImages(imagePaths, prompt);
            
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", parts));
            
            adapter.convertRequest("gemini-pro-vision", messages);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        System.out.println("多模态处理性能:");
        System.out.println("  图片数量: " + imagePaths.size());
        System.out.println("  处理次数: " + iterations);
        System.out.println("  总时间: " + totalTime + "ms");
        System.out.println("  平均时间: " + String.format("%.2f", avgTime) + "ms/次");
        System.out.println();
    }
    
    /**
     * 创建示例响应
     */
    private String createSampleResponse(String text) {
        return "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"" + text + "\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 10,\n" +
            "    \"candidatesTokenCount\": 5,\n" +
            "    \"totalTokenCount\": 15\n" +
            "  }\n" +
            "}";
    }
    
    /**
     * 创建大响应
     */
    private String createLargeResponse(String text) {
        return "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"" + text.replace("\"", "\\\"") + "\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 1000,\n" +
            "    \"candidatesTokenCount\": 2000,\n" +
            "    \"totalTokenCount\": 3000\n" +
            "  }\n" +
            "}";
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
}