package cn.jbolt.mail.gpt.fetch.plugin;

import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.common.model.AiPrompt;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.common.model.EmailTranslation;
import cn.jbolt.common.model.EmailTranslationConfig;
import cn.jbolt.common.util.FileKit;
import cn.jbolt.llm.service.LlmService;
import cn.jbolt.mail.gpt.service.ConfigurableTranslationService;
import cn.jbolt.llm.util.ExcelToImageUtil;
import cn.jbolt.llm.util.PdfToImageUtil;
import cn.jbolt.llm.util.TextToImageUtil;
import cn.jbolt.llm.util.WordToImageUtil;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import cn.jbolt.mail.gpt.parser.EmailParsingService;
import cn.jbolt.mail.gpt.util.HtmlScreenshotUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jfinal.aop.Aop;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.IPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static cn.jbolt.common.util.FileKit.*;

/**
 * 分离翻译结果类
 */
class SeparateTranslationResult {
    private String translatedSubject;
    private String translatedContent;
    private List<EmailTranslation.ImageTranslationResult> imageTranslations;

    public SeparateTranslationResult() {
        this.imageTranslations = new ArrayList<>();
    }

    public String getTranslatedSubject() {
        return translatedSubject;
    }

    public void setTranslatedSubject(String translatedSubject) {
        this.translatedSubject = translatedSubject;
    }

    public String getTranslatedContent() {
        return translatedContent;
    }

    public void setTranslatedContent(String translatedContent) {
        this.translatedContent = translatedContent;
    }

    public List<EmailTranslation.ImageTranslationResult> getImageTranslations() {
        return imageTranslations;
    }

    public void setImageTranslations(List<EmailTranslation.ImageTranslationResult> imageTranslations) {
        this.imageTranslations = imageTranslations;
    }

    public void addImageTranslation(String imagePath, String translatedContent, int order) {
        EmailTranslation.ImageTranslationResult result = new EmailTranslation.ImageTranslationResult();
        result.setImagePath(imagePath);
        result.setTranslatedContent(translatedContent);
        result.setOrder(order);
        this.imageTranslations.add(result);
    }
}

/**
 * 邮件翻译插件
 * 从数据库读取最近两天的客户邮件，且没在邮件翻译表里面的邮件
 * 1. 如果fetch_status是0的，就启动完整邮件接收，把邮件全部接收下来
 * 2. 读取邮件正文，用html的方式显示，截图
 * 3. 把截图丢给kimi的api，给定预置的翻译文字，把返回来的内容写到邮件翻译表
 */
public class EmailTranslationPlugin implements IPlugin {
    private static final Logger LOG = LoggerFactory.getLogger(EmailTranslationPlugin.class);

    // 运行状态
    private final AtomicBoolean running = new AtomicBoolean(false);

    // 邮件客户端池
    private final EmailClientPool clientPool = EmailClientPool.getInstance();

    // 轮询间隔（秒）
    private final int pollingInterval;

    // 最大并发任务数
    private final int maxConcurrentTasks;

    // 获取最近几天的邮件
    private final int recentDays;
    // 执行器服务
    private ScheduledExecutorService scheduledExecutor;
    private ExecutorService processExecutor;

    private EmailNameCache emailNameCache = Aop.get(EmailNameCache.class);

    /**
     * 构造函数
     */
    public EmailTranslationPlugin() {
        this(
                PropKit.getInt("email_translation_polling_interval", 300), // 默认每5分钟执行一次
                PropKit.getInt("email_translation_max_concurrent_tasks", 2), // 降低默认并发任务数从3到2
                PropKit.getInt("email_translation_recent_days", 1) // 默认处理最近1天的邮件
        );
    }

    /**
     * 构造函数
     *
     * @param pollingInterval    轮询间隔（秒）
     * @param maxConcurrentTasks 最大并发任务数
     * @param recentDays         获取最近几天的邮件
     */
    public EmailTranslationPlugin(int pollingInterval, int maxConcurrentTasks, int recentDays) {
        this.pollingInterval = pollingInterval;
        this.maxConcurrentTasks = maxConcurrentTasks;
        this.recentDays = recentDays;
    }

    @Override
    public boolean start() {
        if (running.compareAndSet(false, true)) {
            try {
                LOG.info("启动邮件翻译插件");

                // 显示当前翻译配置
                ConfigurableTranslationService translationService = ConfigurableTranslationService.getInstance();
                LOG.info("当前翻译配置: {}", translationService.getConfigSummary());

                // 创建线程池
                scheduledExecutor = Executors.newScheduledThreadPool(1);
                processExecutor = Executors.newFixedThreadPool(maxConcurrentTasks);

                // 启动定时任务
                scheduledExecutor.scheduleWithFixedDelay(
                        this::processRecentEmails,
                        60, // 启动后延迟60秒开始执行
                        pollingInterval,
                        TimeUnit.SECONDS
                );

                return true;
            } catch (Exception e) {
                LOG.error("启动邮件翻译插件失败: {}", e.getMessage(), e);
                running.set(false);
                return false;
            }
        } else {
            LOG.warn("邮件翻译插件已在运行中");
            return true;
        }
    }

    @Override
    public boolean stop() {
        if (running.compareAndSet(true, false)) {
            try {
                LOG.info("停止邮件翻译插件");

                // 关闭线程池
                if (scheduledExecutor != null) {
                    scheduledExecutor.shutdown();
                    scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS);
                }

                if (processExecutor != null) {
                    processExecutor.shutdown();
                    processExecutor.awaitTermination(30, TimeUnit.SECONDS);
                }

                return true;
            } catch (Exception e) {
                LOG.error("停止邮件翻译插件失败: " + e.getMessage(), e);
                return false;
            }
        } else {
            LOG.warn("邮件翻译插件未运行");
            return true;
        }
    }

    /**
     * 处理最近的邮件
     */
    private void processRecentEmails() {
        if (!running.get()) {
            return;
        }

        try {
            LOG.info("开始处理最近{}天的客户邮件进行翻译", recentDays);

            // 计算最近几天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -recentDays);
            Date startDate = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 查询最近几天的客户邮件，且不在翻译表中的
            String sql = "SELECT m.* FROM email_messages m " +
                    "LEFT JOIN email_translation t ON m.id = t.email_id " +
                    "WHERE m.sent_date >= ? " +
                    "AND t.id IS NULL " +
                    "AND m.from_address in (select email from client group by email) " +
                    "AND m.from_address not in (select username from email_account group by username) " +
                    "ORDER BY m.sent_date DESC " +
                    "LIMIT 50";

            List<Record> emails = Db.find(sql, sdf.format(startDate));

            if (emails.isEmpty()) {
                LOG.info("没有需要翻译的新邮件");
                return;
            }

            LOG.info("找到 {} 封需要翻译的邮件", emails.size());

            // 处理每封邮件
            for (Record emailRecord : emails) {
                if (!running.get()) break;

                processExecutor.submit(() -> {
                    try {
                        processEmail(new EmailMessages().put(emailRecord));
                    } catch (Exception e) {
                        LOG.error("处理邮件翻译失败: {}", e.getMessage(), e);
                    }
                });
            }
        } catch (Exception e) {
            LOG.error("处理最近邮件翻译失败: " + e.getMessage(), e);
        }
    }


    /**
     * 翻译邮件
     *
     * @param emailId 邮件ID
     */
    public String translateEmail(Long emailId) {
        return translateEmail(emailId, true); // 默认启用图片翻译
    }

    /**
     * 翻译邮件
     *
     * @param emailId 邮件ID
     * @param translateImages 是否翻译图片
     */
    public String translateEmail(Long emailId, boolean translateImages) {
        EmailMessages email = new EmailMessages().dao().findById(emailId);
        if (email == null) {
            LOG.warn("邮件 {} 不存在", emailId);
            return null;
        }

        return processEmail(email, translateImages);
    }

    /**
     * 单独翻译功能
     *
     * @param emailId 邮件ID
     * @param type 翻译类型 (subject/content/images)
     * @param originalContent 原始内容
     * @param promptId 提示词ID
     * @param customPrompt 自定义提示词
     * @param providerId AI提供商ID
     * @param modelId AI模型ID
     * @return 翻译结果JSON字符串
     */
    public String translateSingle(Long emailId, String type, String originalContent,
                                 Integer promptId, String customPrompt, Long providerId, Long modelId) {
        try {
            EmailMessages email = new EmailMessages().dao().findById(emailId);
            if (email == null) {
                LOG.warn("邮件 {} 不存在", emailId);
                return null;
            }

            // 获取或创建翻译记录
            EmailTranslation translation = getOrCreateTranslation(emailId);

            // 根据类型执行不同的翻译逻辑
            switch (type) {
                case "subject":
                    return translateSubjectSingle(email, translation, promptId, customPrompt, providerId, modelId);
                case "content":
                    return translateContentSingle(email, translation, originalContent, promptId, customPrompt, providerId, modelId);
                case "images":
                    return translateImagesSingle(email, translation, promptId, customPrompt, providerId, modelId);
                default:
                    LOG.warn("未知的翻译类型: {}", type);
                    return null;
            }
        } catch (Exception e) {
            LOG.error("单独翻译失败", e);
            return null;
        }
    }

    /**
     * 获取或创建翻译记录
     */
    private EmailTranslation getOrCreateTranslation(Long emailId) {
        EmailTranslation translation = new EmailTranslation().dao().findFirst(
                "SELECT * FROM email_translation WHERE email_id = ?", emailId);

        if (translation == null) {
            // 创建新的翻译记录
            EmailMessages email = new EmailMessages().dao().findById(emailId);
            if (email != null) {
                translation = new EmailTranslation();
                translation.setEmailId(emailId);
                translation.setOriginalContent(email.getContent());
                translation.setSubjectOriginal(email.getSubject());
                translation.setContentOriginal(email.getContent());
                translation.setCreateTime(new Date());
                translation.save();
            }
        }

        return translation;
    }

    /**
     * 单独翻译标题
     */
    private String translateSubjectSingle(EmailMessages email, EmailTranslation translation,
                                         Integer promptId, String customPrompt, Long providerId, Long modelId) {
        try {
            String subject = email.getSubject();
            if (subject == null || subject.trim().isEmpty()) {
                return null;
            }

            // 获取提示词
            AiPrompt aiPrompt = getAiPrompt(promptId, customPrompt);
            if (aiPrompt == null) {
                LOG.error("获取提示词失败");
                return null;
            }

            // 执行翻译
            String translatedSubject = translateTextWithModel(subject, aiPrompt, providerId, modelId);

            if (translatedSubject != null && !translatedSubject.trim().isEmpty()) {
                // 保存翻译结果
                translation.setSubjectTranslated(translatedSubject);
                translation.updateTimestamp();
                translation.update();

                // 返回结果
                JSONObject result = new JSONObject();
                result.put("subjectOriginal", subject);
                result.put("subjectTranslated", translatedSubject);
                return result.toJSONString();
            }

            return null;
        } catch (Exception e) {
            LOG.error("翻译标题失败", e);
            return null;
        }
    }

    /**
     * 获取AI提示词
     */
    private AiPrompt getAiPrompt(Integer promptId, String customPrompt) {
        if (promptId != null) {
            return new AiPrompt().dao().findById(promptId);
        } else if (customPrompt != null && !customPrompt.trim().isEmpty()) {
            // 创建临时提示词对象
            AiPrompt aiPrompt = new AiPrompt();
            aiPrompt.setUserContent(customPrompt);
            return aiPrompt;
        }
        return null;
    }

    /**
     * 使用指定模型翻译文本
     */
    private String translateTextWithModel(String text, AiPrompt aiPrompt, Long providerId, Long modelId) {
        try {
            // 这里需要调用LLM服务进行翻译
            // 暂时返回简单的翻译结果，实际应该调用对应的AI模型
            LlmService llmService = Aop.get(LlmService.class);
            if (llmService != null) {
                return llmService.translateWithModel(text, aiPrompt, providerId, modelId);
            }

            // 如果LLM服务不可用，返回null
            LOG.warn("LLM服务不可用");
            return null;
        } catch (Exception e) {
            LOG.error("调用AI模型翻译失败", e);
            return null;
        }
    }

    /**
     * 单独翻译内容
     */
    private String translateContentSingle(EmailMessages email, EmailTranslation translation, String originalContent,
                                         Integer promptId, String customPrompt, Long providerId, Long modelId) {
        try {
            if (originalContent == null || originalContent.trim().isEmpty()) {
                return null;
            }

            // 获取提示词
            AiPrompt aiPrompt = getAiPrompt(promptId, customPrompt);
            if (aiPrompt == null) {
                LOG.error("获取提示词失败");
                return null;
            }

            // 执行翻译
            String translatedContent = translateTextWithModel(originalContent, aiPrompt, providerId, modelId);

            if (translatedContent != null && !translatedContent.trim().isEmpty()) {
                // 保存翻译结果
                translation.setContentTranslated(translatedContent);
                translation.updateTimestamp();
                translation.update();

                // 返回结果
                JSONObject result = new JSONObject();
                result.put("contentOriginal", originalContent);
                result.put("contentTranslated", translatedContent);
                return result.toJSONString();
            }

            return null;
        } catch (Exception e) {
            LOG.error("翻译内容失败", e);
            return null;
        }
    }

    /**
     * 单独翻译图片
     */
    private String translateImagesSingle(EmailMessages email, EmailTranslation translation,
                                        Integer promptId, String customPrompt, Long providerId, Long modelId) {
        try {
            // 获取邮件的图片附件
            // 获取邮件中的图片文件路径集合
            Set<String> imageFiles = new HashSet<>();
            List<File> attachments = FileKit.listEmailAttachments(email);
            for (File file : attachments) {
                String fileName = file.getName().toLowerCase();
                if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") 
                    || fileName.endsWith(".png") || fileName.endsWith(".gif")
                    || fileName.endsWith(".bmp") || fileName.endsWith(".webp")
                    || fileName.endsWith(".tiff") || fileName.endsWith(".tif")) {
                    imageFiles.add(file.getAbsolutePath());
                }
            }
            if (imageFiles.isEmpty()) {
                LOG.info("邮件 {} 没有图片附件", email.getId());
                return null;
            }

            // 获取提示词
            AiPrompt aiPrompt = getAiPrompt(promptId, customPrompt);
            if (aiPrompt == null) {
                LOG.error("获取提示词失败");
                return null;
            }

            // 翻译所有图片
            List<EmailTranslation.ImageTranslationResult> imageTranslations = new ArrayList<>();
            List<String> fileList = new ArrayList<>(imageFiles);

            for (int i = 0; i < fileList.size(); i++) {
                String imagePath = fileList.get(i);
                String imageTranslation = translateSingleImageWithModel(aiPrompt, imagePath, i + 1, providerId, modelId);
                
                // 创建翻译结果对象，无论是否成功都要记录
                EmailTranslation.ImageTranslationResult result = new EmailTranslation.ImageTranslationResult();
                result.setImagePath(imagePath);
                result.setOrder(i + 1);
                
                if (imageTranslation != null && !imageTranslation.trim().isEmpty()) {
                    result.setTranslatedContent(imageTranslation);
                    LOG.info("图片翻译成功: {} -> {}", imagePath, imageTranslation.length() + "字符");
                } else {
                    result.setTranslatedContent("图片翻译失败：AI模型无法处理此图片或网络连接异常");
                    LOG.warn("图片翻译失败: {}", imagePath);
                }
                
                imageTranslations.add(result);
            }

            // 无论是否有成功的翻译，都保存结果（包括失败信息）
            translation.setImageTranslationResults(imageTranslations);
            translation.updateTimestamp();
            translation.update();

            // 返回结果
            JSONObject result = new JSONObject();
            result.put("imageTranslations", imageTranslations);
            return result.toJSONString();
        } catch (Exception e) {
            LOG.error("翻译图片失败", e);
            return null;
        }
    }

    /**
     * 使用指定模型翻译单张图片
     */
    private String translateSingleImageWithModel(AiPrompt aiPrompt, String imagePath, int order, Long providerId, Long modelId) {
        try {
            // 检查图片文件是否存在
            File imageFile = new File(imagePath);
            if (!imageFile.exists()) {
                LOG.error("图片文件不存在: {}", imagePath);
                return null;
            }

            // 调用LLM服务进行图片翻译
            LlmService llmService = Aop.get(LlmService.class);
            if (llmService != null) {
                LOG.debug("开始使用AI模型翻译图片: {} (providerId={}, modelId={})", imagePath, providerId, modelId);
                String result = llmService.translateImageWithModel(imagePath, aiPrompt, providerId, modelId);
                if (result != null && !result.trim().isEmpty()) {
                    LOG.debug("AI模型翻译图片成功: {} -> {}字符", imagePath, result.length());
                    return result;
                } else {
                    LOG.warn("AI模型翻译图片返回空结果: {}", imagePath);
                }
            } else {
                LOG.warn("LLM服务不可用，尝试使用原有翻译方法");
            }

            // 如果LLM服务不可用或翻译失败，使用原有的翻译方法
            LOG.debug("尝试使用原有方法翻译图片: {}", imagePath);
            return translateSingleImage(aiPrompt, imagePath, order);
        } catch (Exception e) {
            LOG.error("调用AI模型翻译图片失败: {}", imagePath, e);
            return null;
        }
    }

    /**
     * 处理单封邮件
     *
     * @param email 邮件对象
     */
    private String processEmail(EmailMessages email) {
        return processEmail(email, true); // 默认启用图片翻译
    }

    /**
     * 处理单封邮件
     *
     * @param email 邮件对象
     * @param translateImages 是否翻译图片
     */
    private String processEmail(EmailMessages email, boolean translateImages) {
        try {
            // 检查系统内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long freeMemory = runtime.freeMemory();
            long totalMemory = runtime.totalMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            // 如果内存使用率超过60%，跳过处理
            if (usedMemory > maxMemory * 0.6) {
                LOG.warn("系统内存使用率过高 ({}%), 跳过邮件 {} 的处理", (usedMemory * 100 / maxMemory), email.getId());
                return StringUtils.EMPTY;
            }
            
            String subject = email.getSubject();
            LOG.info("开始处理邮件: {}, ID: {}, 内存使用率: {}%", subject, email.getId(), (usedMemory * 100 / maxMemory));

            // 检查fetch_status，如果是0，则需要先获取完整内容
            Integer fetchStatus = email.getFetchStatus();
            if (fetchStatus != null && fetchStatus == 0) {
                LOG.info("邮件 {} 需要获取完整内容", email.getId());
                fetchCompleteEmail(email);
            }
            // 获取邮件HTML内容
            String htmlContent = email.getContentHtml();
            if (htmlContent == null || htmlContent.trim().isEmpty()) {
                LOG.info("邮件 {} 没有HTML内容，使用文本内容", email.getId());
                htmlContent = email.getContentText();
                if (htmlContent == null || htmlContent.trim().isEmpty()) {
                    LOG.warn("邮件 {} 没有内容，跳过处理", email.getId());
                    return StringUtils.EMPTY;
                }
                // 将纯文本转换为HTML格式
                htmlContent = "<html><body><pre>" + htmlContent + "</pre></body></html>";
            }


            // 提取最新的邮件内容，过滤掉引用的旧邮件
            LOG.info("提取邮件 {} 的最新内容，过滤引用的旧邮件", email.getId());
            String latestContent = HtmlScreenshotUtil.extractLatestEmailContent(htmlContent);

            // 清理HTML内容，移除可能导致渲染问题的元素
            latestContent = cleanHtml(latestContent);

            // 如果提取结果不为空，则使用提取的内容
            if (StringUtils.isNotEmpty(latestContent)) {
                // 确保提取的内容有适当的HTML结构
                if (!latestContent.trim().toLowerCase().startsWith("<html")) {
                    latestContent = "<html><body>" + latestContent + "</body></html>";
                }
            } else {
                LOG.warn("提取邮件 {} 的最新内容失败，使用原始内容", email.getId());
            }

            // 添加邮件的主题、收件人、发件人和发件时间信息到HTML内容
            String fromAddress = email.getFromAddress();
            String toAddress = email.getToAddress();
            Date sentDate = email.getSentDate();
            String sentDateStr = sentDate != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(sentDate) : "未知";

            // 创建包含邮件信息的HTML头部
            String emailInfoHtml =
                    "<div style='background-color: #f5f5f5; padding: 10px; border-bottom: 1px solid #ddd; font-family: Arial, sans-serif;'>" +
                            "<div style='margin-bottom: 5px;'><strong>发件人：</strong>" + emailNameCache.getDisplayName(fromAddress) + "</div>" +
                            "<div style='margin-bottom: 5px;'><strong>收件人：</strong>" + emailNameCache.getDisplayName(toAddress) + "</div>" +
                            "<div><strong>时间：</strong>" + sentDateStr + "</div>" +
                            "</div>";

            // 将邮件信息插入到HTML内容的开头
            if (latestContent.contains("<body")) {
                latestContent = latestContent.replaceFirst("<body[^>]*>", "$0" + emailInfoHtml);
            } else if (latestContent.contains("<html")) {
                latestContent = latestContent.replaceFirst("<html[^>]*>", "$0<body>" + emailInfoHtml);
                if (!latestContent.contains("</body>")) {
                    latestContent = latestContent.replaceFirst("</html>", "</body></html>");
                }
            } else {
                latestContent = "<html><body>" + emailInfoHtml + latestContent + "</body></html>";
            }

            // 生成截图
            String screenshotFilePath = generateScreenshot(email.getId(), latestContent, EmailParsingService.createScreenshotPath(email));
            if (screenshotFilePath == null) {
                LOG.warn("邮件 {} 生成截图失败，跳过处理", email.getId());
                return StringUtils.EMPTY;
            }

            AiPrompt aiPrompt = new AiPrompt().dao().findFirst("select * from ai_prompt where enable='1' and `key`='email_monument_translate' order by id");

            // 创建文件列表，首先添加邮件截图
            Set<String> fileSet = Sets.newHashSet(screenshotFilePath);

            // 提取HTML中的图片路径
            List<String> imagePaths = extractImagePathsWithJsoup(latestContent);
            fileSet.addAll(imagePaths);

            List<String> pdfPaths = Lists.newArrayList();
            List<String> excelPaths = Lists.newArrayList();
            List<String> wordPaths = Lists.newArrayList();
            List<String> txtPaths = Lists.newArrayList();
            List<String> attachImagePaths = Lists.newArrayList();
            List<File> files = FileKit.listEmailAttachments(email);
            for (File file : files) {
                String fileName = file.getName();
                String fileNameLowerCase = fileName.toLowerCase();
                if (htmlContent.contains(fileName)) {
                    continue;
                }
                if (file.isFile() && fileNameLowerCase.endsWith(".pdf")) {
                    pdfPaths.add(file.getAbsolutePath());
                } else if (file.isFile() && (fileNameLowerCase.endsWith(".xlsx") || fileNameLowerCase.endsWith(".xls"))) {
                    excelPaths.add(file.getAbsolutePath());
                } else if (file.isFile() && (fileNameLowerCase.endsWith(".docx") || fileNameLowerCase.endsWith(".doc"))) {
                    wordPaths.add(file.getAbsolutePath());
                } else if (file.isFile() && fileNameLowerCase.endsWith(".txt")) {
                    txtPaths.add(file.getAbsolutePath());
                } else if (file.isFile() && (fileNameLowerCase.endsWith(".jpg") || fileNameLowerCase.endsWith(".jpeg")
                        || fileName.endsWith(".png") || fileName.endsWith(".gif") || fileName.endsWith(".bmp")
                        || fileName.endsWith(".webp") || fileName.endsWith(".tiff") || fileName.endsWith(".tif"))) {
                    attachImagePaths.add(file.getAbsolutePath());
                }
            }

            // 从HTML内容中提取更多附件路径
            pdfPaths.addAll(extractPdfPathsWithJsoup(latestContent));
            excelPaths.addAll(extractExcelPathsWithJsoup(latestContent));
            wordPaths.addAll(extractWordPathsWithJsoup(latestContent));
            txtPaths.addAll(extractTxtPathsWithJsoup(latestContent));

            if (!pdfPaths.isEmpty()) {
                LOG.info("发现 {} 个PDF附件，开始转换为图片", pdfPaths.size());
                
                // 限制PDF处理数量，防止内存溢出
                int maxPdfCount = 100;
                int processedPdfCount = 0;
                
                for (String pdfPath : pdfPaths) {
                    if (processedPdfCount >= maxPdfCount) {
                        LOG.warn("PDF附件过多，只处理前{}个", maxPdfCount);
                        break;
                    }
                    
                    try {
                        // 检查内存使用情况 - 重用已声明的变量
                        freeMemory = runtime.freeMemory();
                        totalMemory = runtime.totalMemory();
                        usedMemory = totalMemory - freeMemory;
                        maxMemory = runtime.maxMemory();
                        
                        // 如果内存使用率超过70%，跳过剩余PDF处理
                        if (usedMemory > maxMemory * 0.7) {
                            LOG.warn("内存使用率过高 ({}%), 跳过剩余PDF处理", (usedMemory * 100 / maxMemory));
                            break;
                        }
                        
                        // 确保PDF文件存在并检查文件大小
                        File pdfFile = new File(pdfPath);
                        if (pdfFile.exists() && pdfFile.isFile()) {
                            // 检查PDF文件大小，超过30MB跳过
                            long fileSize = pdfFile.length();
                            if (fileSize > 30 * 1024 * 1024) {
                                LOG.warn("PDF文件过大 ({}MB)，跳过处理: {}", fileSize / (1024 * 1024), pdfPath);
                                continue;
                            }
                            
                            LOG.info("开始处理PDF文件: {} ({}MB)", pdfPath, fileSize / (1024 * 1024));
                            
                            // 转换PDF为图片
                            String pdfImageDir = EmailParsingService.createScreenshotPath(email);
                            List<String> pdfImagePaths = PdfToImageUtil.convertPdfToImages(pdfPath, pdfImageDir);
                            
                            if (!pdfImagePaths.isEmpty()) {
                                LOG.info("PDF文件 {} 成功转换为 {} 张图片", pdfPath, pdfImagePaths.size());
                                fileSet.addAll(pdfImagePaths);
                                processedPdfCount++;
                                
                                // 限制总图片数量，防止内存过载
                                if (fileSet.size() > 100) {
                                    LOG.warn("图片数量过多 ({}张)，停止处理更多PDF", fileSet.size());
                                    break;
                                }
                            } else {
                                LOG.warn("PDF文件 {} 转换失败，可能文件损坏或格式不支持", pdfPath);
                            }
                        } else {
                            LOG.warn("PDF文件不存在: {}", pdfPath);
                        }
                    } catch (OutOfMemoryError e) {
                        LOG.error("处理PDF文件 {} 时内存不足，停止处理更多PDF: {}", pdfPath, e.getMessage());
                        // 强制GC并跳出循环
                        System.gc();
                        break;
                    } catch (Exception e) {
                        LOG.error("处理PDF文件 {} 失败: {}", pdfPath, e.getMessage(), e);
                        // 继续处理下一个文件
                    }
                }
            }

            if (!excelPaths.isEmpty()) {
                LOG.info("发现 {} 个Excel附件，开始转换为图片", excelPaths.size());
                for (String excelPath : excelPaths) {
                    try {
                        // 确保Excel文件存在
                        File excelFile = new File(excelPath);
                        if (excelFile.exists() && excelFile.isFile()) {
                            // 转换Excel为图片
                            String excelImageDir = EmailParsingService.createScreenshotPath(email);
                            List<String> excelImagePaths = ExcelToImageUtil.convertExcelToImages(excelPath, excelImageDir, email.getId());
                            if (!excelImagePaths.isEmpty()) {
                                LOG.info("Excel文件 {} 成功转换为 {} 张图片", excelPath, excelImagePaths.size());
                                fileSet.addAll(excelImagePaths);
                            }
                        } else {
                            LOG.warn("Excel文件不存在: {}", excelPath);
                        }
                    } catch (Exception e) {
                        LOG.error("处理Excel文件 {} 失败: {}", excelPath, e.getMessage(), e);
                    }
                }
            }

            if (!wordPaths.isEmpty()) {
                LOG.info("发现 {} 个Word附件，开始转换为图片", wordPaths.size());
                for (String wordPath : wordPaths) {
                    try {
                        // 确保Word文件存在
                        File wordFile = new File(wordPath);
                        if (wordFile.exists() && wordFile.isFile()) {
                            // 转换Word为图片
                            String wordImageDir = EmailParsingService.createScreenshotPath(email);
                            List<String> wordImagePaths = WordToImageUtil.convertWordToImages(wordPath, wordImageDir, email.getId());
                            if (!wordImagePaths.isEmpty()) {
                                LOG.info("Word文件 {} 成功转换为 {} 张图片", wordPath, wordImagePaths.size());
                                fileSet.addAll(wordImagePaths);
                            }
                        } else {
                            LOG.warn("Word文件不存在: {}", wordPath);
                        }
                    } catch (Exception e) {
                        LOG.error("处理Word文件 {} 失败: {}", wordPath, e.getMessage(), e);
                    }
                }
            }

            if (!txtPaths.isEmpty()) {
                LOG.info("发现 {} 个TXT附件，开始转换为图片", txtPaths.size());
                for (String txtPath : txtPaths) {
                    try {
                        // 确保TXT文件存在
                        File txtFile = new File(txtPath);
                        if (txtFile.exists() && txtFile.isFile()) {
                            // 转换TXT为图片
                            String txtImageDir = EmailParsingService.createScreenshotPath(email);
                            List<String> txtImagePaths = TextToImageUtil.convertTextToImages(txtPath, txtImageDir, email.getId());
                            if (!txtImagePaths.isEmpty()) {
                                LOG.info("TXT文件 {} 成功转换为 {} 张图片", txtPath, txtImagePaths.size());
                                fileSet.addAll(txtImagePaths);
                            }
                        } else {
                            LOG.warn("TXT文件不存在: {}", txtPath);
                        }
                    } catch (Exception e) {
                        LOG.error("处理TXT文件 {} 失败: {}", txtPath, e.getMessage(), e);
                    }
                }
            }

            // 处理图片附件
            if (!attachImagePaths.isEmpty()) {
                LOG.info("发现 {} 个图片附件，直接添加到处理列表", attachImagePaths.size());
                for (String imagePath : attachImagePaths) {
                    try {
                        // 确保图片文件存在
                        File imageFile = new File(imagePath);
                        if (imageFile.exists() && imageFile.isFile()) {
                            LOG.info("添加图片附件: {}", imagePath);
                            fileSet.add(imagePath);
                        } else {
                            LOG.warn("图片文件不存在: {}", imagePath);
                        }
                    } catch (Exception e) {
                        LOG.error("处理图片文件 {} 失败: {}", imagePath, e.getMessage(), e);
                    }
                }
            }

            // 使用新的分离翻译方法
            SeparateTranslationResult separateResult = processEmailWithSeparateTranslationsNew(aiPrompt, subject, latestContent, fileSet, translateImages);

            // 保存翻译结果
            saveTranslationSeparate(email.getId(), subject, latestContent, separateResult, screenshotFilePath);

            // 为了兼容性，也生成合并的翻译内容
            String translatedContent = buildCombinedTranslationContent(separateResult);

            LOG.info("邮件 {} 翻译处理完成", email.getId());
            return translatedContent;
        } catch (Exception e) {
            LOG.error("处理邮件 {} 翻译失败: {}", email.getId(), e.getMessage(), e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 使用分离翻译方法处理邮件（新版本，返回结构化结果）
     *
     * @param aiPrompt AI提示词对象
     * @param subject 邮件主题
     * @param latestContent 邮件内容
     * @param fileSet 所有图片文件集合
     * @param translateImages 是否翻译图片
     * @return 分离翻译结果
     */
    private SeparateTranslationResult processEmailWithSeparateTranslationsNew(AiPrompt aiPrompt, String subject, String latestContent, Set<String> fileSet, boolean translateImages) {
        SeparateTranslationResult result = new SeparateTranslationResult();

        try {
            // 1. 翻译标题
            String translatedSubject = translateSubject(aiPrompt, subject);
            result.setTranslatedSubject(translatedSubject);

            // 2. 翻译内容
            String translatedContent = translateContent(aiPrompt, latestContent);
            result.setTranslatedContent(translatedContent);

            // 3. 翻译图片（一张一张翻译）
            if (translateImages && !fileSet.isEmpty()) {
                LOG.info("开始翻译图片，共{}张", fileSet.size());
                List<String> fileList = new ArrayList<>(fileSet);
                for (int i = 0; i < fileList.size(); i++) {
                    String imagePath = fileList.get(i);
                    String imageTranslation = translateSingleImage(aiPrompt, imagePath, i + 1);
                    if (imageTranslation != null && !imageTranslation.trim().isEmpty()) {
                        result.addImageTranslation(imagePath, imageTranslation, i + 1);
                    }
                }
            } else if (!translateImages) {
                LOG.info("图片翻译已禁用，跳过{}张图片的翻译", fileSet.size());
            }

            return result;
        } catch (Exception e) {
            LOG.error("分离翻译处理失败: {}", e.getMessage(), e);
            return result;
        }
    }

    /**
     * 构建合并的翻译内容（用于兼容性）
     *
     * @param result 分离翻译结果
     * @return 合并的翻译内容
     */
    private String buildCombinedTranslationContent(SeparateTranslationResult result) {
        StringBuilder combined = new StringBuilder();

        if (result.getTranslatedSubject() != null && !result.getTranslatedSubject().trim().isEmpty()) {
            combined.append("【翻译标题】\n").append(result.getTranslatedSubject()).append("\n\n");
        }

        if (result.getTranslatedContent() != null && !result.getTranslatedContent().trim().isEmpty()) {
            combined.append("【翻译内容】\n").append(result.getTranslatedContent()).append("\n\n");
        }

        if (!result.getImageTranslations().isEmpty()) {
            for (EmailTranslation.ImageTranslationResult imageResult : result.getImageTranslations()) {
                combined.append("【图片翻译 ").append(imageResult.getOrder()).append("】\n")
                        .append("图片路径: ").append(imageResult.getImagePath()).append("\n")
                        .append("翻译内容: ").append(imageResult.getTranslatedContent()).append("\n\n");
            }
        }

        return combined.toString();
    }

    /**
     * 使用分离翻译方法处理邮件（旧版本，保留兼容性）
     *
     * @param aiPrompt AI提示词对象
     * @param subject 邮件主题
     * @param latestContent 邮件内容
     * @param fileSet 所有图片文件集合
     * @return 翻译结果
     */
    @Deprecated
    private String processEmailWithSeparateTranslations(AiPrompt aiPrompt, String subject, String latestContent, Set<String> fileSet) {
        try {
            StringBuilder finalTranslatedContent = new StringBuilder();

            // 1. 翻译标题
            String translatedSubject = translateSubject(aiPrompt, subject);
            if (translatedSubject != null && !translatedSubject.trim().isEmpty()) {
                finalTranslatedContent.append("【翻译标题】\n").append(translatedSubject).append("\n\n");
            }

            // 2. 翻译内容
            String translatedContent = translateContent(aiPrompt, latestContent);
            if (translatedContent != null && !translatedContent.trim().isEmpty()) {
                finalTranslatedContent.append("【翻译内容】\n").append(translatedContent).append("\n\n");
            }

            // 3. 翻译图片（一张一张翻译）
            if (!fileSet.isEmpty()) {
                List<String> fileList = new ArrayList<>(fileSet);
                for (int i = 0; i < fileList.size(); i++) {
                    String imagePath = fileList.get(i);
                    String imageTranslation = translateSingleImage(aiPrompt, imagePath, i + 1);
                    if (imageTranslation != null && !imageTranslation.trim().isEmpty()) {
                        finalTranslatedContent.append("【图片翻译 ").append(i + 1).append("】\n")
                                .append("图片路径: ").append(imagePath).append("\n")
                                .append("翻译内容: ").append(imageTranslation).append("\n\n");
                    }
                }
            }

            return finalTranslatedContent.toString();
        } catch (Exception e) {
            LOG.error("分离翻译处理失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 翻译邮件标题
     *
     * @param aiPrompt AI提示词对象
     * @param subject 邮件标题
     * @return 翻译结果
     */
    private String translateSubject(AiPrompt aiPrompt, String subject) {
        if (subject == null || subject.trim().isEmpty()) {
            return "";
        }

        try {
            LOG.info("开始翻译邮件标题: {}", subject);
            ConfigurableTranslationService translationService = ConfigurableTranslationService.getInstance();
            String result = translationService.translateText(subject, ConfigurableTranslationService.TranslationType.SUBJECT);
            LOG.info("标题翻译完成，结果长度: {}", result != null ? result.length() : 0);
            return result != null ? result : "";
        } catch (Exception e) {
            LOG.error("翻译标题失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 翻译邮件内容
     *
     * @param aiPrompt AI提示词对象
     * @param content 邮件内容
     * @return 翻译结果
     */
    private String translateContent(AiPrompt aiPrompt, String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        try {
            LOG.info("开始翻译邮件内容，长度: {}", content.length());
            ConfigurableTranslationService translationService = ConfigurableTranslationService.getInstance();
            String result = translationService.translateText(content, ConfigurableTranslationService.TranslationType.CONTENT);
            LOG.info("内容翻译完成，结果长度: {}", result != null ? result.length() : 0);
            return result != null ? result : "";
        } catch (Exception e) {
            LOG.error("翻译内容失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 翻译单张图片
     *
     * @param aiPrompt AI提示词对象
     * @param imagePath 图片路径
     * @param order 图片序号
     * @return 翻译结果
     */
    private String translateSingleImage(AiPrompt aiPrompt, String imagePath, int order) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            return "";
        }

        try {
            LOG.info("开始翻译第{}张图片: {}", order, imagePath);
            List<String> imageList = new ArrayList<>();
            imageList.add(imagePath);
            ConfigurableTranslationService translationService = ConfigurableTranslationService.getInstance();
            String result = translationService.translateImage(imageList);
            LOG.info("第{}张图片翻译完成，结果长度: {}", order, result != null ? result.length() : 0);
            return result != null ? result : "";
        } catch (Exception e) {
            LOG.error("翻译第{}张图片失败: {}", order, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 分批处理邮件和图片附件（保留原方法作为备用）
     *
     * @param aiPrompt AI提示词对象
     * @param subject 邮件主题
     * @param latestContent 邮件内容
     * @param fileSet 所有图片文件集合
     * @return 翻译结果
     */
    @Deprecated
    private String processEmailWithBatchImages(AiPrompt aiPrompt, String subject, String latestContent, Set<String> fileSet) {
        try {
            StringBuilder finalTranslatedContent = new StringBuilder();
            List<String> fileList = new ArrayList<>(fileSet);
            
            // 每批最多处理5个文件
            final int BATCH_SIZE = 5;
            
            if (fileList.isEmpty()) {
                // 没有附件，直接翻译邮件内容
                String result = LlmService.me().callLlm("gemini", "gemini-2.0-flash-exp",
                    aiPrompt.getSystemContent() + "\n邮件主题：" + subject + "\n邮件内容:" + latestContent);
                return result != null ? result : "";
            }
            
            // 第一批：包含邮件内容和前5个附件
            int startIndex = 0;
            int endIndex = Math.min(BATCH_SIZE, fileList.size());
            List<String> firstBatch = new ArrayList<>(fileList.subList(startIndex, endIndex));

            LOG.info("处理第1批附件，包含邮件内容，附件数量: {}", firstBatch.size());
            String firstPrompt = aiPrompt.getSystemContent() + "\n邮件主题：" + subject + "\n邮件内容:" + latestContent + "\n图片都是附件。";
            String firstResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", firstPrompt, firstBatch);
            
            if (firstResult != null) {
                finalTranslatedContent.append(firstResult);
            }
            
            // 处理剩余批次：只包含附件
            int batchNumber = 2;
            for (int i = BATCH_SIZE; i < fileList.size(); i += BATCH_SIZE) {
                startIndex = i;
                endIndex = Math.min(i + BATCH_SIZE, fileList.size());
                List<String> currentBatch = new ArrayList<>(fileList.subList(startIndex, endIndex));

                LOG.info("处理第{}批附件，数量: {}", batchNumber, currentBatch.size());
                String batchPrompt = aiPrompt.getSystemContent();
                String batchResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", batchPrompt, currentBatch);
                
                if (batchResult != null) {
                    // 添加批次分隔符
                    if (finalTranslatedContent.length() > 0) {
                        finalTranslatedContent.append("\n\n--- 附件翻译 (第").append(batchNumber).append("批) ---\n");
                    }
                    finalTranslatedContent.append(batchResult);
                }
                
                batchNumber++;
                
                // 添加延迟避免API频率限制
                try {
                    Thread.sleep(1000); // 1秒延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOG.warn("批次处理被中断");
                    break;
                }
            }
            
            String result = finalTranslatedContent.toString();
            LOG.info("完成分批处理，总共处理{}批，结果长度: {}", batchNumber - 1, result.length());
            return result;
            
        } catch (Exception e) {
            LOG.error("分批处理图片翻译失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取邮件完整内容
     *
     * @param email 邮件对象
     */
    private void fetchCompleteEmail(EmailMessages email) {
        try {
            // 获取邮箱账号
            String emailAccount = email.getEmailAccount();
            EmailAccount account = new EmailAccount().dao().findFirst(
                    "SELECT * FROM email_account WHERE username = ?", emailAccount);

            if (account == null) {
                LOG.error("找不到邮箱账号: {}", emailAccount);
                return;
            }

            // 获取邮件客户端
            EmailClient client = clientPool.getClient(account, EmailClient.Mode.COMPLETE_MODE);

            // 连接邮件服务器
            if (!client.connect()) {
                LOG.error("连接邮箱 {} 失败", account.getUsername());
                return;
            }

            // 获取指定邮件的完整内容
            boolean success = client.fetchEmailByEmailMessages(email);

            if (success) {
                LOG.info("成功获取邮件 {} 的完整内容", email.getId());
                // 重新从数据库获取更新后的邮件内容
                email = new EmailMessages().dao().findById(email.getId());
            } else {
                LOG.error("获取邮件 {} 的完整内容失败", email.getId());
            }
        } catch (Exception e) {
            LOG.error("获取邮件 {} 完整内容失败: {}", email.getId(), e.getMessage(), e);
        }
    }

    /**
     * 清理HTML内容
     *
     * @param html 原始HTML内容
     * @return 清理后的HTML内容
     */
    private String cleanHtml(String html) {
        if (html == null) return "";

        // 移除脚本和样式标签
        String cleaned = html.replaceAll("<script[^>]*>[\\s\\S]*?</script>", "")
                .replaceAll("<style[^>]*>[\\s\\S]*?</style>", "");

        // 限制HTML内容大小，避免渲染过大的内容
        if (cleaned.length() > 100000) {
            cleaned = cleaned.substring(0, 100000) + "... [内容过长，已截断]";
        }

        return cleaned;
    }

    /**
     * 生成邮件内容截图
     *
     * @param emailId     邮件ID
     * @param htmlContent HTML内容
     * @return 截图文件路径，失败返回null
     */
    private String generateScreenshot(Object emailId, String htmlContent, String screenshotPath) {
        // 使用HtmlScreenshotUtil生成截图
        return HtmlScreenshotUtil.generateScreenshot(htmlContent, screenshotPath, emailId + "_last" + ".png");
    }

    /**
     * 从HTML中提取纯文本
     *
     * @param html HTML内容
     * @return 纯文本内容
     */
    private String extractTextFromHtml(String html) {
        // 使用HtmlScreenshotUtil提取纯文本
        return HtmlScreenshotUtil.extractTextFromHtml(html);
    }

    /**
     * 保存分离翻译结果到数据库
     *
     * @param emailId           邮件ID
     * @param originalSubject   原始标题
     * @param originalContent   原始内容
     * @param result           分离翻译结果
     * @param screenshotPath    截图路径
     */
    private void saveTranslationSeparate(Object emailId, String originalSubject, String originalContent,
                                       SeparateTranslationResult result, String screenshotPath) {
        try {
            Record emailTranslation = Db.findFirst("select * from email_translation where email_id = ?", emailId);
            EmailTranslation translation = new EmailTranslation();
            if(emailTranslation != null){
                translation.put(emailTranslation);
            }

            // 设置基本信息
            translation.setEmailId(Long.valueOf(emailId.toString()));
            translation.setOriginalContent(originalContent);
            translation.setScreenshotPath(screenshotPath);

            // 设置分离的翻译内容
            translation.setSubjectOriginal(originalSubject);
            translation.setSubjectTranslated(result.getTranslatedSubject());
            translation.setContentOriginal(originalContent);
            translation.setContentTranslated(result.getTranslatedContent());
            translation.setImageTranslationResults(result.getImageTranslations());

            // 为了兼容性，也设置合并的翻译内容
            String combinedTranslation = buildCombinedTranslationContent(result);
            translation.setTranslatedContent(combinedTranslation);

            // 设置时间戳
            if (translation.getId() == null) {
                translation.setCreateTime(new Date());
            }
            translation.updateTimestamp();

            // 保存到数据库
            boolean success;
            if(translation.getId() != null){
                success = translation.update();
            } else {
                success = translation.save();
            }

            if (success) {
                LOG.info("邮件 {} 分离翻译结果保存成功", emailId);
            } else {
                LOG.error("邮件 {} 分离翻译结果保存失败", emailId);
            }
        } catch (Exception e) {
            LOG.error("保存邮件 {} 分离翻译结果失败: {}", emailId, e.getMessage(), e);
        }
    }

    /**
     * 保存翻译结果
     *
     * @param emailId           邮件ID
     * @param originalContent   原始内容
     * @param translatedContent 翻译内容
     * @param screenshotPath    截图路径
     */
    @Deprecated
    private void saveTranslation(Object emailId, String originalContent, String translatedContent, String screenshotPath) {
        try {
            Record emailTranslation = Db.findFirst("select * from email_translation where email_id = ?", emailId);
            EmailTranslation translation = new EmailTranslation();
            if(emailTranslation!=null){
                translation.put(emailTranslation);
            }
            // 创建翻译记录
            translation.setEmailId(Long.valueOf(emailId.toString()));
            translation.setOriginalContent(originalContent);
            translation.setTranslatedContent(translatedContent);
            translation.setScreenshotPath(screenshotPath);
            translation.setCreateTime(new Date());

            // 保存到数据库
            boolean success;
            if(translation.getId()!=null){
                success = translation.update();
            }else{
                success = translation.save();
            }

            if (success) {
                LOG.info("邮件 {} 翻译结果保存成功", emailId);
            } else {
                LOG.error("邮件 {} 翻译结果保存失败", emailId);
            }
        } catch (Exception e) {
            LOG.error("保存邮件 {} 翻译结果失败: {}", emailId, e.getMessage(), e);
        }
    }

    /**
     * 手动触发处理最近邮件
     */
    public void triggerProcessRecentEmails() {
        if (!running.get()) {
            LOG.warn("邮件翻译插件未运行，无法触发处理");
            return;
        }

        LOG.info("手动触发处理最近邮件翻译");
        processRecentEmails();
    }
}
