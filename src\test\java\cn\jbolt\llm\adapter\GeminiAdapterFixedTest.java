package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * 测试修复后的Gemini适配器
 * 使用真实的API响应格式进行测试
 */
public class GeminiAdapterFixedTest {
    
    public static void main(String[] args) {
        GeminiAdapterFixedTest test = new GeminiAdapterFixedTest();
        
        System.out.println("测试修复后的Gemini适配器");
        System.out.println("========================\n");
        
        test.testRealResponseFormat();
        test.testEmptyContentResponse();
        test.testSafetyFilteredResponse();
        test.testErrorResponse();
        
        System.out.println("测试完成！");
    }
    
    /**
     * 测试真实的API响应格式
     */
    public void testRealResponseFormat() {
        System.out.println("=== 测试真实API响应格式 ===");
        
        // 你提供的真实响应格式（但缺少文本内容）
        String realResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokensDetails\": [{\n" +
            "      \"modality\": \"TEXT\",\n" +
            "      \"tokenCount\": 19\n" +
            "    }],\n" +
            "    \"thoughtsTokenCount\": 214,\n" +
            "    \"totalTokenCount\": 233,\n" +
            "    \"promptTokenCount\": 19\n" +
            "  },\n" +
            "  \"responseId\": \"XjGtaPzjEv2BmtkPsaCSkA4\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(realResponse);
        
        System.out.println("原始响应:");
        System.out.println(formatJson(realResponse));
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(converted));
        
        // 验证转换结果
        JSONObject result = JSON.parseObject(converted);
        System.out.println("\n验证结果:");
        System.out.println("ID: " + result.getString("id"));
        System.out.println("模型: " + result.getString("model"));
        System.out.println("消息内容: " + result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content"));
        System.out.println("完成原因: " + result.getJSONArray("choices").getJSONObject(0).getString("finish_reason"));
        
        if (result.containsKey("usage")) {
            JSONObject usage = result.getJSONObject("usage");
            System.out.println("Token使用: 输入=" + usage.getIntValue("prompt_tokens") + 
                             ", 输出=" + usage.getIntValue("completion_tokens") + 
                             ", 总计=" + usage.getIntValue("total_tokens"));
            if (usage.containsKey("thoughts_tokens")) {
                System.out.println("思考Token: " + usage.getIntValue("thoughts_tokens"));
            }
        }
        System.out.println();
    }
    
    /**
     * 测试包含文本内容的完整响应
     */
    public void testCompleteResponse() {
        System.out.println("=== 测试完整响应格式 ===");
        
        String completeResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"你好！我是Gemini，一个由Google开发的大型语言模型。我可以帮助你进行对话、回答问题、协助创作等多种任务。有什么我可以为你做的吗？\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 10,\n" +
            "    \"candidatesTokenCount\": 35,\n" +
            "    \"totalTokenCount\": 45\n" +
            "  },\n" +
            "  \"responseId\": \"test-response-123\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(completeResponse);
        
        System.out.println("转换后的响应:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * 测试空内容响应
     */
    public void testEmptyContentResponse() {
        System.out.println("=== 测试空内容响应 ===");
        
        String emptyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 5,\n" +
            "    \"totalTokenCount\": 5\n" +
            "  }\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(emptyResponse);
        
        System.out.println("转换后的响应:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * 测试安全过滤响应
     */
    public void testSafetyFilteredResponse() {
        System.out.println("=== 测试安全过滤响应 ===");
        
        String safetyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"SAFETY\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(safetyResponse);
        
        System.out.println("转换后的响应:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * 测试错误响应
     */
    public void testErrorResponse() {
        System.out.println("=== 测试错误响应 ===");
        
        String errorResponse = "{\n" +
            "  \"error\": {\n" +
            "    \"code\": 400,\n" +
            "    \"message\": \"Invalid request\",\n" +
            "    \"status\": \"INVALID_ARGUMENT\"\n" +
            "  }\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(errorResponse);
        
        System.out.println("转换后的错误响应:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * 格式化JSON输出
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}