package cn.jbolt.llm.adapter;

import cn.jbolt.llm.service.LlmService;
import cn.jbolt.mail.gpt.InitEnv;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.Kv;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GeminiAdapter使用示例测试类
 * 演示如何使用GeminiAdapter进行文本和图片处理
 */
public class GeminiAdapterExampleTest {
    
    private GeminiAdapter adapter;
    
    @Before
    public void setUp() {
        System.out.println("=== 初始化测试环境 ===");
        try {
            System.out.println("步骤1: 调用InitEnv.initEnvironment()");
            InitEnv.initEnvironment();
            System.out.println("步骤2: 创建GeminiAdapter实例");
            adapter = new GeminiAdapter();
            System.out.println("测试环境初始化完成");
        } catch (Exception e) {
            System.err.println("初始化过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
    }

    /**
     * 基础测试：验证适配器是否正常工作
     */
    @Test
    public void basicTest() {
        System.out.println("=== 基础测试：验证适配器是否正常工作 ===");

        try {
            System.out.println("测试1: 检查适配器是否创建成功");
            if (adapter != null) {
                System.out.println("✓ 适配器创建成功");
            } else {
                System.out.println("✗ 适配器创建失败");
                return;
            }

            System.out.println("测试2: 检查支持的模态类型");
            System.out.println("支持的模态类型: " + adapter.getSupportedModalities());

            System.out.println("测试3: 测试简单的请求转换");
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", "Hello"));

            String request = adapter.convertRequest("gemini-2.5-pro", messages);
            System.out.println("请求转换成功，长度: " + request.length());
            System.out.println("请求内容预览: " + request.substring(0, Math.min(200, request.length())) + "...");

            System.out.println("✓ 基础测试全部通过");

        } catch (Exception e) {
            System.err.println("基础测试中出现异常: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("基础测试完成");
        System.out.println();
    }

    /**
     * 示例1：翻译文本
     */
    @Test
    public void example1_TranslateText() {
        System.out.println("=== 示例1：翻译文本 ===");
        System.out.println("开始执行测试...");

        try {
            // 先测试适配器基本功能
            System.out.println("步骤1: 测试适配器基本功能");
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                .set("content", "请将以下英文翻译成中文：Hello, how are you today? I hope you have a wonderful day!"));

            System.out.println("步骤2: 调用适配器convertRequest方法");
            String request = adapter.convertRequest("gemini-2.5-pro", messages);
            System.out.println("生成的请求JSON:");
            System.out.println(formatJson(request));

            // 方式1：使用LlmService统一接口（可能会有网络请求，先注释掉）
            System.out.println("步骤3: 准备调用LlmService（注意：这可能需要网络连接和API配置）");
            String response = LlmService.me().callLlm(
                "gemini",
                "gemini-2.5-pro",
                "请将以下英文翻译成中文：Hello, how are you today? I hope you have a wonderful day!"
            );
            System.out.println("翻译结果: " + response);
            System.out.println("LlmService调用已注释，避免网络请求阻塞");

        } catch (Exception e) {
            System.err.println("执行过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("示例1执行完成");
        System.out.println();
    }
    
    /**
     * 示例2：分析单张图片
     */
    @Test
    public void example2_AnalyzeSingleImage() {
        System.out.println("=== 示例2：分析单张图片 ===");
        System.out.println("开始执行图片分析测试...");

        try {
            // 先测试适配器的图片处理功能
            System.out.println("步骤1: 测试适配器图片处理功能");
            List<String> imagePaths = Arrays.asList("menu.jpg");
            String prompt = "请分析这张餐厅菜单图片，识别其中的英文菜名并翻译成中文，同时描述菜单的整体布局";

            System.out.println("步骤2: 调用processImages方法");
            Object parts = adapter.processImages(imagePaths, prompt);
            System.out.println("图片处理后的parts结构:");
            System.out.println(formatJson(JSON.toJSONString(parts)));

            System.out.println("步骤3: 构建完整请求");
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", parts));
            String request = adapter.convertRequest("gemini-2.5-pro", messages);
            System.out.println("完整请求JSON:");
            System.out.println(formatJson(request));

            // LlmService调用（可能需要网络，先注释）
            System.out.println("步骤4: LlmService调用已注释，避免网络请求");
            String response = LlmService.me().callLlmWithImages(
                "gemini",
                "gemini-2.5-pro",
                prompt,
                imagePaths
            );
            System.out.println("图片分析结果: " + response);

        } catch (Exception e) {
            System.err.println("图片分析测试中出现异常: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("示例2执行完成");
        System.out.println();
    }
    
    /**
     * 示例3：对比多张图片
     */
    @Test
    public void example3_CompareMultipleImages() {
        System.out.println("=== 示例3：对比多张图片 ===");
        
        // 方式1：使用LlmService统一接口
        List<String> imagePaths = Arrays.asList("sign1.jpg", "sign2.jpg", "sign3.jpg");
        String response = LlmService.me().callLlmWithImages(
            "gemini", 
            "gemini-2.5-pro", 
            "请对比这三张路标图片，识别并翻译所有英文标识，分析它们的设计风格差异", 
            imagePaths
        );
        System.out.println("多图片对比结果: " + response);
        
        // 方式2：直接使用适配器处理多张图片
        String prompt = "请对比这三张路标图片，识别并翻译所有英文标识，分析它们的设计风格差异";
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("多图片处理后的parts结构:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("多图片完整请求JSON:");
        System.out.println(formatJson(request));
        System.out.println();
    }
    
    /**
     * 示例4：图片中的文字识别和翻译
     */
    @Test
    public void example4_ImageTextRecognitionAndTranslation() {
        System.out.println("=== 示例4：图片文字识别和翻译 ===");
        
        List<String> imagePaths = Arrays.asList("street-sign.jpg");
        String prompt = "请识别图片中的所有文字内容，并将非中文文字翻译成中文。同时描述图片的场景。";
        
        // 使用LlmService统一接口
        String response = LlmService.me().callLlmWithImages(
            "gemini", 
            "gemini-2.5-pro", 
            prompt, 
            imagePaths
        );
        System.out.println("文字识别和翻译结果: " + response);
        
        // 直接使用适配器
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("处理后的parts:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        System.out.println();
    }
    
    /**
     * 示例5：多轮对话
     */
    @Test
    public void example5_MultiTurnConversation() {
        System.out.println("=== 示例5：多轮对话 ===");
        
        // 构建多轮对话消息
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", "你好，我想学习中文"));
        messages.add(Kv.by("role", "assistant").set("content", "你好！很高兴帮助你学习中文。你想从哪个方面开始学习呢？"));
        messages.add(Kv.by("role", "user").set("content", "我想学习一些基本的问候语，请用英文和中文对照的方式教我"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("多轮对话请求JSON:");
        System.out.println(formatJson(request));
        System.out.println();
    }
    
    /**
     * 示例6：视频内容分析（演示Gemini的多模态能力）
     */
    @Test
    public void example6_VideoAnalysis() {
        System.out.println("=== 示例6：视频内容分析 ===");
        
        List<String> videoPaths = Arrays.asList("sample-video.mp4");
        String prompt = "请分析这个视频的内容，描述其中的场景和动作，如果有文字或语音，请翻译成中文";
        
        // 使用适配器处理视频
        Object parts = adapter.processVideos(videoPaths, prompt);
        System.out.println("视频处理后的parts结构:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("视频分析请求JSON:");
        System.out.println(formatJson(request));
        System.out.println();
    }
    
    /**
     * 示例7：音频内容分析（演示Gemini的多模态能力）
     */
    @Test
    public void example7_AudioAnalysis() {
        System.out.println("=== 示例7：音频内容分析 ===");
        
        List<String> audioPaths = Arrays.asList("sample-audio.mp3");
        String prompt = "请分析这个音频文件的内容，如果是语音，请转录并翻译成中文";
        
        // 使用适配器处理音频
        Object parts = adapter.processAudios(audioPaths, prompt);
        System.out.println("音频处理后的parts结构:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("音频分析请求JSON:");
        System.out.println(formatJson(request));
        System.out.println();
    }
    
    /**
     * 示例8：检查适配器支持的模态类型
     */
    @Test
    public void example8_CheckSupportedModalities() {
        System.out.println("=== 示例8：检查适配器支持的模态类型 ===");
        
        System.out.println("GeminiAdapter支持的模态类型:");
        for (AbstractMultimodalAdapter.ModalityType modality : adapter.getSupportedModalities()) {
            System.out.println("- " + modality);
        }
        
        System.out.println("\n模态类型支持检查:");
        System.out.println("支持文本: " + adapter.supportsModality(AbstractMultimodalAdapter.ModalityType.TEXT));
        System.out.println("支持图片: " + adapter.supportsModality(AbstractMultimodalAdapter.ModalityType.IMAGE));
        System.out.println("支持视频: " + adapter.supportsModality(AbstractMultimodalAdapter.ModalityType.VIDEO));
        System.out.println("支持音频: " + adapter.supportsModality(AbstractMultimodalAdapter.ModalityType.AUDIO));
        System.out.println("支持文档: " + adapter.supportsModality(AbstractMultimodalAdapter.ModalityType.DOCUMENT));
        System.out.println();
    }
    
    /**
     * 格式化JSON字符串，便于阅读
     */
    private String formatJson(String jsonString) {
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj, true);
        } catch (Exception e) {
            return jsonString;
        }
    }
    
    /**
     * 运行所有示例
     */
    @Test
    public void runAllExamples() {
        System.out.println("========================================");
        System.out.println("GeminiAdapter使用示例演示");
        System.out.println("========================================");
        
        example1_TranslateText();
        example2_AnalyzeSingleImage();
        example3_CompareMultipleImages();
        example4_ImageTextRecognitionAndTranslation();
        example5_MultiTurnConversation();
        example6_VideoAnalysis();
        example7_AudioAnalysis();
        example8_CheckSupportedModalities();
        
        System.out.println("========================================");
        System.out.println("所有示例演示完成");
        System.out.println("========================================");
    }
}
