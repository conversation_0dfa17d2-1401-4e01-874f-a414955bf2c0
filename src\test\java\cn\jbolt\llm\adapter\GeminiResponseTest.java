package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * Test fixed Gemini adapter with real API response format
 */
public class GeminiResponseTest {
    
    public static void main(String[] args) {
        GeminiResponseTest test = new GeminiResponseTest();
        
        System.out.println("Testing Fixed Gemini Adapter");
        System.out.println("============================\n");
        
        test.testRealResponseFormat();
        test.testCompleteResponse();
        test.testEmptyContentResponse();
        test.testSafetyFilteredResponse();
        test.testErrorResponse();
        
        System.out.println("All tests completed!");
    }
    
    /**
     * Test real API response format (your provided response)
     */
    public void testRealResponseFormat() {
        System.out.println("=== Test Real API Response Format ===");
        
        // Your real response format (missing text content)
        String realResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokensDetails\": [{\n" +
            "      \"modality\": \"TEXT\",\n" +
            "      \"tokenCount\": 19\n" +
            "    }],\n" +
            "    \"thoughtsTokenCount\": 214,\n" +
            "    \"totalTokenCount\": 233,\n" +
            "    \"promptTokenCount\": 19\n" +
            "  },\n" +
            "  \"responseId\": \"XjGtaPzjEv2BmtkPsaCSkA4\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(realResponse);
        
        System.out.println("Original Response:");
        System.out.println(formatJson(realResponse));
        System.out.println("\nConverted Response:");
        System.out.println(formatJson(converted));
        
        // Verify conversion result
        JSONObject result = JSON.parseObject(converted);
        System.out.println("\nVerification Results:");
        System.out.println("ID: " + result.getString("id"));
        System.out.println("Model: " + result.getString("model"));
        System.out.println("Message Content: " + result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content"));
        System.out.println("Finish Reason: " + result.getJSONArray("choices").getJSONObject(0).getString("finish_reason"));
        
        if (result.containsKey("usage")) {
            JSONObject usage = result.getJSONObject("usage");
            System.out.println("Token Usage: input=" + usage.getIntValue("prompt_tokens") + 
                             ", output=" + usage.getIntValue("completion_tokens") + 
                             ", total=" + usage.getIntValue("total_tokens"));
            if (usage.containsKey("thoughts_tokens")) {
                System.out.println("Thoughts Tokens: " + usage.getIntValue("thoughts_tokens"));
            }
        }
        System.out.println();
    }
    
    /**
     * Test complete response with text content
     */
    public void testCompleteResponse() {
        System.out.println("=== Test Complete Response Format ===");
        
        String completeResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"Hello! I am Gemini, a large language model developed by Google. I can help you with conversations, answering questions, creative writing, and many other tasks. What can I do for you?\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 10,\n" +
            "    \"candidatesTokenCount\": 35,\n" +
            "    \"totalTokenCount\": 45\n" +
            "  },\n" +
            "  \"responseId\": \"test-response-123\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(completeResponse);
        
        System.out.println("Converted Response:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * Test empty content response
     */
    public void testEmptyContentResponse() {
        System.out.println("=== Test Empty Content Response ===");
        
        String emptyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 5,\n" +
            "    \"totalTokenCount\": 5\n" +
            "  }\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(emptyResponse);
        
        System.out.println("Converted Response:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * Test safety filtered response
     */
    public void testSafetyFilteredResponse() {
        System.out.println("=== Test Safety Filtered Response ===");
        
        String safetyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"SAFETY\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\"\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(safetyResponse);
        
        System.out.println("Converted Response:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * Test error response
     */
    public void testErrorResponse() {
        System.out.println("=== Test Error Response ===");
        
        String errorResponse = "{\n" +
            "  \"error\": {\n" +
            "    \"code\": 400,\n" +
            "    \"message\": \"Invalid request\",\n" +
            "    \"status\": \"INVALID_ARGUMENT\"\n" +
            "  }\n" +
            "}";
        
        GeminiAdapter adapter = new GeminiAdapter();
        String converted = adapter.convertResponse(errorResponse);
        
        System.out.println("Converted Error Response:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * Format JSON output
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}