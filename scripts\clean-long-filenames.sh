#!/bin/bash

# 清理超长文件名脚本
# 用于解决Maven打包时的文件名长度警告问题

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
MAX_FILENAME_LENGTH=80  # 最大文件名长度
UPLOAD_DIR="src/main/webapp/upload/email/attachment"
BACKUP_DIR="backup/long-filenames-$(date +%Y%m%d_%H%M%S)"
DRY_RUN=false

# 显示帮助信息
show_help() {
    echo -e "${BLUE}超长文件名清理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --dry-run          只显示将要处理的文件，不实际执行"
    echo "  -l, --length LENGTH    设置最大文件名长度 (默认: 80)"
    echo "  -p, --path PATH        设置要处理的目录路径 (默认: $UPLOAD_DIR)"
    echo "  -b, --backup DIR       设置备份目录 (默认: $BACKUP_DIR)"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --dry-run                    # 预览模式，不实际修改文件"
    echo "  $0 --length 60                  # 设置最大文件名长度为60"
    echo "  $0 --path upload/attachments    # 处理指定目录"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -l|--length)
            MAX_FILENAME_LENGTH="$2"
            shift 2
            ;;
        -p|--path)
            UPLOAD_DIR="$2"
            shift 2
            ;;
        -b|--backup)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查目录是否存在
if [ ! -d "$UPLOAD_DIR" ]; then
    echo -e "${RED}错误: 目录 '$UPLOAD_DIR' 不存在${NC}"
    exit 1
fi

echo -e "${BLUE}=== 超长文件名清理工具 ===${NC}"
echo -e "处理目录: ${YELLOW}$UPLOAD_DIR${NC}"
echo -e "最大文件名长度: ${YELLOW}$MAX_FILENAME_LENGTH${NC}"
echo -e "备份目录: ${YELLOW}$BACKUP_DIR${NC}"
echo -e "预览模式: ${YELLOW}$([ "$DRY_RUN" = true ] && echo "是" || echo "否")${NC}"
echo ""

# 统计变量
total_files=0
long_files=0
processed_files=0

# 创建备份目录
if [ "$DRY_RUN" = false ]; then
    mkdir -p "$BACKUP_DIR"
    echo -e "${GREEN}创建备份目录: $BACKUP_DIR${NC}"
fi

# 生成短文件名的函数
generate_short_name() {
    local original_name="$1"
    local extension="${original_name##*.}"
    local basename="${original_name%.*}"
    local max_base_length=$((MAX_FILENAME_LENGTH - ${#extension} - 1))
    
    # 如果基础名称太长，截取并添加哈希
    if [ ${#basename} -gt $max_base_length ]; then
        local hash=$(echo -n "$basename" | md5sum | cut -c1-8)
        local truncated_length=$((max_base_length - 9)) # 为哈希和下划线留空间
        local truncated_base="${basename:0:$truncated_length}"
        echo "${truncated_base}_${hash}.${extension}"
    else
        echo "$original_name"
    fi
}

# 处理文件的函数
process_file() {
    local filepath="$1"
    local filename=$(basename "$filepath")
    local dirname=$(dirname "$filepath")
    
    total_files=$((total_files + 1))
    
    # 检查文件名长度
    if [ ${#filename} -gt $MAX_FILENAME_LENGTH ]; then
        long_files=$((long_files + 1))
        
        # 生成新的短文件名
        local new_filename=$(generate_short_name "$filename")
        local new_filepath="$dirname/$new_filename"
        
        echo -e "${YELLOW}发现超长文件名:${NC}"
        echo -e "  原文件: ${RED}$filename${NC} (${#filename} 字符)"
        echo -e "  新文件: ${GREEN}$new_filename${NC} (${#new_filename} 字符)"
        echo -e "  路径: $dirname"
        
        if [ "$DRY_RUN" = false ]; then
            # 备份原文件信息
            echo "$filepath -> $new_filepath" >> "$BACKUP_DIR/rename_log.txt"
            
            # 检查新文件名是否已存在
            if [ -f "$new_filepath" ]; then
                echo -e "  ${RED}警告: 目标文件已存在，跳过处理${NC}"
                return
            fi
            
            # 重命名文件
            if mv "$filepath" "$new_filepath"; then
                processed_files=$((processed_files + 1))
                echo -e "  ${GREEN}✓ 重命名成功${NC}"
            else
                echo -e "  ${RED}✗ 重命名失败${NC}"
            fi
        else
            echo -e "  ${BLUE}[预览模式] 将会重命名此文件${NC}"
        fi
        
        echo ""
    fi
}

# 遍历所有文件
echo -e "${BLUE}开始扫描文件...${NC}"
echo ""

# 使用find命令遍历所有文件
while IFS= read -r -d '' file; do
    process_file "$file"
done < <(find "$UPLOAD_DIR" -type f -print0)

# 显示统计结果
echo -e "${BLUE}=== 处理完成 ===${NC}"
echo -e "总文件数: ${YELLOW}$total_files${NC}"
echo -e "超长文件名: ${YELLOW}$long_files${NC}"

if [ "$DRY_RUN" = false ]; then
    echo -e "已处理文件: ${GREEN}$processed_files${NC}"
    if [ $processed_files -gt 0 ]; then
        echo -e "备份信息: ${YELLOW}$BACKUP_DIR/rename_log.txt${NC}"
    fi
else
    echo -e "${BLUE}这是预览模式，没有实际修改文件${NC}"
    echo -e "${BLUE}要执行实际操作，请运行: $0 (不带 --dry-run 参数)${NC}"
fi

# 如果有超长文件名，提供Maven配置建议
if [ $long_files -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}=== Maven打包建议 ===${NC}"
    echo "为了避免Maven打包警告，你可以："
    echo "1. 运行此脚本清理文件名 (推荐)"
    echo "2. 在 package.xml 中添加排除规则"
    echo "3. 在文件上传时限制文件名长度"
fi

exit 0
