package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Gemini适配器测试类
 * 测试文字翻译、图片解析等功能
 */
public class GeminiAdapterTest {
    
    private GeminiAdapter adapter;
    
    @Before
    public void setUp() {
        adapter = new GeminiAdapter();
    }
    
    /**
     * 测试1: 单纯文字翻译 - 英文翻译成中文
     */
    @Test
    public void testTextTranslation() {
        System.out.println("=== 测试1: 英文翻译成中文 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user")
                .set("content", "Please translate the following English text to Chinese: 'Hello, how are you today? I hope you have a wonderful day!'"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("请求JSON:");
        System.out.println(formatJson(request));
        
        // 模拟Gemini响应
        String mockResponse = createMockGeminiResponse("你好，你今天怎么样？我希望你有美好的一天！");
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 测试2: 日文翻译成中文
     */
    @Test
    public void testJapaneseTranslation() {
        System.out.println("=== 测试2: 日文翻译成中文 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user")
                .set("content", "请将以下日文翻译成中文：「こんにちは、今日はいい天気ですね。一緒にお茶を飲みませんか？」"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("请求JSON:");
        System.out.println(formatJson(request));
        
        String mockResponse = createMockGeminiResponse("你好，今天天气真好呢。要不要一起喝茶？");
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 测试3: 单图片解析 - 解析图片内容并翻译
     */
    @Test
    public void testSingleImageAnalysis() {
        System.out.println("=== 测试3: 单图片解析和翻译 ===");
        
        // 模拟图片路径
        List<String> imagePaths = Arrays.asList("test-images/menu.jpg");
        String prompt = "请分析这张图片中的内容，如果有非中文文字，请翻译成中文并描述图片内容";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("处理后的parts:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        // 构建完整请求
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("\n完整请求JSON:");
        System.out.println(formatJson(request));
        
        // 模拟响应
        String mockResponse = createMockGeminiResponse(
            "这是一张餐厅菜单的图片。菜单上显示了以下内容：\n" +
            "- Appetizers (开胃菜)\n" +
            "- Main Course (主菜)\n" +
            "- Desserts (甜点)\n" +
            "菜单采用英文书写，整体设计简洁优雅，背景为白色，文字为黑色。"
        );
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 测试4: 多图片对比分析
     */
    @Test
    public void testMultipleImagesAnalysis() {
        System.out.println("=== 测试4: 多图片对比分析 ===");
        
        List<String> imagePaths = Arrays.asList(
            "test-images/sign1.jpg", 
            "test-images/sign2.jpg", 
            "test-images/sign3.jpg"
        );
        String prompt = "请对比分析这三张图片中的标识牌内容，将所有非中文文字翻译成中文，并说明它们的异同点";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("处理后的parts:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        // 构建完整请求
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("\n完整请求JSON:");
        System.out.println(formatJson(request));
        
        String mockResponse = createMockGeminiResponse(
            "对比分析三张标识牌图片：\n\n" +
            "图片1：显示\"Welcome to Tokyo\"（欢迎来到东京），红色背景，白色文字\n" +
            "图片2：显示\"Exit\"（出口），绿色背景，白色箭头指示\n" +
            "图片3：显示\"No Smoking\"（禁止吸烟），白色背景，红色禁止标志\n\n" +
            "共同点：都是公共场所的指示标识\n" +
            "不同点：用途不同，颜色搭配不同，传达的信息类型不同"
        );
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 测试5: 韩文翻译
     */
    @Test
    public void testKoreanTranslation() {
        System.out.println("=== 测试5: 韩文翻译成中文 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user")
                .set("content", "请将以下韩文翻译成中文：\"안녕하세요! 오늘 날씨가 정말 좋네요. 같이 커피 한 잔 하실래요?\""));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("请求JSON:");
        System.out.println(formatJson(request));
        
        String mockResponse = createMockGeminiResponse("你好！今天天气真的很好呢。要不要一起喝杯咖啡？");
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 测试6: 图片中的文字识别和翻译
     */
    @Test
    public void testImageTextRecognitionAndTranslation() {
        System.out.println("=== 测试6: 图片文字识别和翻译 ===");
        
        List<String> imagePaths = Arrays.asList("test-images/street-sign.jpg");
        String prompt = "请识别图片中的所有文字内容，并将非中文文字翻译成中文。同时描述图片的场景。";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("处理后的parts:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("\n完整请求JSON:");
        System.out.println(formatJson(request));
        
        String mockResponse = createMockGeminiResponse(
            "图片场景：这是一张街道标识牌的照片，拍摄于城市街头。\n\n" +
            "识别到的文字内容：\n" +
            "- \"Main Street\" → 主街\n" +
            "- \"Central Park\" → 中央公园\n" +
            "- \"Shopping Mall\" → 购物中心\n" +
            "- \"2.5 km\" → 2.5公里\n\n" +
            "标识牌为蓝色背景，白色文字，箭头指向不同方向，为行人和车辆提供方向指引。"
        );
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("\n转换后的响应:");
        System.out.println(formatJson(convertedResponse));
    }
    
    /**
     * 创建模拟的Gemini响应
     */
    private String createMockGeminiResponse(String content) {
        JSONObject response = new JSONObject();
        
        JSONArray candidates = new JSONArray();
        JSONObject candidate = new JSONObject();
        
        JSONObject contentObj = new JSONObject();
        JSONArray parts = new JSONArray();
        JSONObject part = new JSONObject();
        part.put("text", content);
        parts.add(part);
        contentObj.put("parts", parts);
        
        candidate.put("content", contentObj);
        candidate.put("finishReason", "STOP");
        candidates.add(candidate);
        
        response.put("candidates", candidates);
        
        // 添加用量信息
        JSONObject usageMetadata = new JSONObject();
        usageMetadata.put("promptTokenCount", 50);
        usageMetadata.put("candidatesTokenCount", 100);
        usageMetadata.put("totalTokenCount", 150);
        response.put("usageMetadata", usageMetadata);
        
        return response.toJSONString();
    }
    
    /**
     * 格式化JSON输出
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}