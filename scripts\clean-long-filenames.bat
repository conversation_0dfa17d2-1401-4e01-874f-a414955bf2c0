@echo off
setlocal enabledelayedexpansion

REM 清理超长文件名脚本 (Windows版本)
REM 用于解决Maven打包时的文件名长度警告问题

REM 配置参数
set MAX_FILENAME_LENGTH=80
set UPLOAD_DIR=src\main\webapp\upload\email\attachment
set BACKUP_DIR=backup\long-filenames-%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set DRY_RUN=false

REM 清理时间字符串中的空格和冒号
set BACKUP_DIR=%BACKUP_DIR: =0%
set BACKUP_DIR=%BACKUP_DIR::=%

REM 解析命令行参数
:parse_args
if "%~1"=="" goto start_process
if "%~1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto parse_args
)
if "%~1"=="--length" (
    set MAX_FILENAME_LENGTH=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--path" (
    set UPLOAD_DIR=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--help" (
    goto show_help
)
echo 未知选项: %~1
goto show_help

:show_help
echo 超长文件名清理脚本 (Windows版本)
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   --dry-run          只显示将要处理的文件，不实际执行
echo   --length LENGTH    设置最大文件名长度 (默认: 80)
echo   --path PATH        设置要处理的目录路径
echo   --help             显示此帮助信息
echo.
echo 示例:
echo   %~nx0 --dry-run                    预览模式，不实际修改文件
echo   %~nx0 --length 60                  设置最大文件名长度为60
echo   %~nx0 --path upload\attachments    处理指定目录
goto end

:start_process
REM 检查目录是否存在
if not exist "%UPLOAD_DIR%" (
    echo 错误: 目录 '%UPLOAD_DIR%' 不存在
    goto end
)

echo === 超长文件名清理工具 ===
echo 处理目录: %UPLOAD_DIR%
echo 最大文件名长度: %MAX_FILENAME_LENGTH%
echo 备份目录: %BACKUP_DIR%
if "%DRY_RUN%"=="true" (
    echo 预览模式: 是
) else (
    echo 预览模式: 否
)
echo.

REM 统计变量
set total_files=0
set long_files=0
set processed_files=0

REM 创建备份目录
if "%DRY_RUN%"=="false" (
    if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
    echo 创建备份目录: %BACKUP_DIR%
)

echo 开始扫描文件...
echo.

REM 遍历所有文件
for /r "%UPLOAD_DIR%" %%f in (*.*) do (
    call :process_file "%%f"
)

REM 显示统计结果
echo === 处理完成 ===
echo 总文件数: !total_files!
echo 超长文件名: !long_files!

if "%DRY_RUN%"=="false" (
    echo 已处理文件: !processed_files!
    if !processed_files! gtr 0 (
        echo 备份信息: %BACKUP_DIR%\rename_log.txt
    )
) else (
    echo 这是预览模式，没有实际修改文件
    echo 要执行实际操作，请运行: %~nx0 ^(不带 --dry-run 参数^)
)

REM 如果有超长文件名，提供Maven配置建议
if !long_files! gtr 0 (
    echo.
    echo === Maven打包建议 ===
    echo 为了避免Maven打包警告，你可以：
    echo 1. 运行此脚本清理文件名 ^(推荐^)
    echo 2. 在 package.xml 中添加排除规则
    echo 3. 在文件上传时限制文件名长度
)

goto end

:process_file
set filepath=%~1
set filename=%~nx1
set dirname=%~dp1

REM 计算文件名长度
set filename_length=0
set temp_filename=!filename!
:count_loop
if "!temp_filename!"=="" goto count_done
set temp_filename=!temp_filename:~1!
set /a filename_length+=1
goto count_loop

:count_done
set /a total_files+=1

REM 检查文件名长度
if !filename_length! gtr %MAX_FILENAME_LENGTH% (
    set /a long_files+=1
    
    REM 生成新的短文件名
    call :generate_short_name "!filename!" new_filename
    set new_filepath=!dirname!!new_filename!
    
    echo 发现超长文件名:
    echo   原文件: !filename! ^(!filename_length! 字符^)
    echo   新文件: !new_filename!
    echo   路径: !dirname!
    
    if "%DRY_RUN%"=="false" (
        REM 备份原文件信息
        echo !filepath! -^> !new_filepath! >> "%BACKUP_DIR%\rename_log.txt"
        
        REM 检查新文件名是否已存在
        if exist "!new_filepath!" (
            echo   警告: 目标文件已存在，跳过处理
        ) else (
            REM 重命名文件
            ren "!filepath!" "!new_filename!" >nul 2>&1
            if !errorlevel! equ 0 (
                set /a processed_files+=1
                echo   √ 重命名成功
            ) else (
                echo   × 重命名失败
            )
        )
    ) else (
        echo   [预览模式] 将会重命名此文件
    )
    
    echo.
)
goto :eof

:generate_short_name
set original_name=%~1
set return_var=%~2

REM 获取文件扩展名
for %%i in ("%original_name%") do (
    set extension=%%~xi
    set basename=%%~ni
)

REM 计算基础名称的最大长度
set /a max_base_length=%MAX_FILENAME_LENGTH% - 10
if !max_base_length! lss 10 set max_base_length=10

REM 如果基础名称太长，截取
set basename_length=0
set temp_basename=!basename!
:basename_count_loop
if "!temp_basename!"=="" goto basename_count_done
set temp_basename=!temp_basename:~1!
set /a basename_length+=1
goto basename_count_loop

:basename_count_done
if !basename_length! gtr !max_base_length! (
    set truncated_base=!basename:~0,%max_base_length%!
    REM 生成简单的哈希（使用时间戳）
    set hash=%random%
    set new_name=!truncated_base!_!hash!!extension!
) else (
    set new_name=!original_name!
)

set %return_var%=!new_name!
goto :eof

:end
endlocal
