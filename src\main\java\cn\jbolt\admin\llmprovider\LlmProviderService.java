package cn.jbolt.admin.llmprovider;

import com.jfinal.plugin.activerecord.Page;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import cn.jbolt.core.service.base.JBoltBaseService;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.llm.service.LlmService;
import com.jfinal.kit.LogKit;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 大模型提供商表
 * 
 * @ClassName: LlmProviderService
 * @author: 总管理
 * @date: 2025-05-08 16:52
 */
public class LlmProviderService extends JBoltBaseService<LlmProvider> {
	private final LlmProvider dao = new LlmProvider().dao();

	@Override
	protected LlmProvider dao() {
		return dao;
	}

	@Override
	protected int systemLogTargetType() {
		return ProjectSystemLogTargetType.NONE.getValue();
	}

	/**
	 * 后台管理数据查询
	 * 
	 * @param pageNumber 第几页
	 * @param pageSize   每页几条数据
	 * @param keywords   关键词
	 * @param sortColumn 排序列名
	 * @param sortType   排序方式 asc desc
	 * @param apiType    API类型(openai/custom)
	 * @param status     状态(0-禁用,1-启用)
	 * @return
	 */
	public Page<LlmProvider> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn,
			String sortType, String apiType, Boolean status) {
		// 创建sql对象
		Sql sql = selectSql().page(pageNumber, pageSize);
		// sql条件处理
		sql.eq("api_type", apiType);
		sql.eqBooleanToChar("status", status);
		// 关键词模糊查询
		sql.likeMulti(keywords, "name", "remark");
		// 排序
		sql.orderBy(sortColumn, sortType);
		return paginate(sql);
	}

	/**
	 * 保存
	 * 
	 * @param llmProvider
	 * @return
	 */
	public Ret save(LlmProvider llmProvider) {
		if (llmProvider == null || isOk(llmProvider.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		boolean success = llmProvider.save();
		if (success) {
			// 添加日志
			// addSaveSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(),
			// llmProvider.getName());
		}
		return ret(success);
	}

	/**
	 * 更新
	 * 
	 * @param llmProvider
	 * @return
	 */
	public Ret update(LlmProvider llmProvider) {
		if (llmProvider == null || notOk(llmProvider.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		// 更新时需要判断数据存在
		LlmProvider dbLlmProvider = findById(llmProvider.getId());
		if (dbLlmProvider == null) {
			return fail(JBoltMsg.DATA_NOT_EXIST);
		}
		boolean success = llmProvider.update();
		if (success) {
			// 添加日志
			// addUpdateSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(),
			// llmProvider.getName());
		}
		return ret(success);
	}

	/**
	 * 删除数据后执行的回调
	 * 
	 * @param llmProvider 要删除的model
	 * @param kv          携带额外参数一般用不上
	 * @return
	 */
	@Override
	protected String afterDelete(LlmProvider llmProvider, Kv kv) {
		// addDeleteSystemLog(llmProvider.getId(),
		// JBoltUserKit.getUserId(),llmProvider.getName());
		return null;
	}

	/**
	 * 检测是否可以删除
	 * 
	 * @param llmProvider model
	 * @param kv          携带额外参数一般用不上
	 * @return
	 */
	@Override
	public String checkInUse(LlmProvider llmProvider, Kv kv) {
		// 这里用来覆盖 检测是否被其它表引用
		return null;
	}

	/**
	 * toggle操作执行后的回调处理
	 */
	@Override
	protected String afterToggleBoolean(LlmProvider llmProvider, String column, Kv kv) {
		// addUpdateSystemLog(llmProvider.getId(), JBoltUserKit.getUserId(),
		// llmProvider.getName(),"的字段["+column+"]值:"+llmProvider.get(column));
		/**
		 * switch(column){
		 * case "status":
		 * break;
		 * }
		 */
		return null;
	}

	/**
	 * 获取启用的提供商列表
	 * 
	 * @return
	 */
	public java.util.List<LlmProvider> getEnabledList() {
		return find("SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority ASC");
	}

	/**
	 * 导出大模型配置到Excel
	 */
	public void exportToExcel(HttpServletResponse response) {
		try {
			// 获取所有提供商数据
			List<LlmProvider> providers = find("SELECT * FROM llm_provider ORDER BY priority ASC");
			List<LlmModel> models = new LlmModel().dao().find("SELECT * FROM llm_model ORDER BY provider_id, id");

			// 创建工作簿
			Workbook workbook = new XSSFWorkbook();

			// 创建提供商工作表
			Sheet providerSheet = workbook.createSheet("大模型提供商");
			createProviderSheet(providerSheet, providers);

			// 创建模型工作表
			Sheet modelSheet = workbook.createSheet("大模型列表");
			createModelSheet(modelSheet, models);

			// 设置响应头
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment; filename="
					+ URLEncoder.encode("大模型配置_" + System.currentTimeMillis() + ".xlsx", "UTF-8"));

			// 输出文件
			OutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			workbook.close();
		} catch (Exception e) {
			LogKit.error("导出大模型配置失败", e);
		}
	}

	/**
	 * 创建提供商工作表
	 */
	private void createProviderSheet(Sheet sheet, List<LlmProvider> providers) {
		// 创建标题行
		Row headerRow = sheet.createRow(0);
		String[] headers = { "ID", "厂商名称", "API基础URL", "API类型", "适配器类名", "超时时间", "默认模型", "状态", "优先级", "备注" };

		for (int i = 0; i < headers.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(headers[i]);
		}

		// 填充数据
		for (int i = 0; i < providers.size(); i++) {
			Row row = sheet.createRow(i + 1);
			LlmProvider provider = providers.get(i);

			row.createCell(0).setCellValue(provider.getId());
			row.createCell(1).setCellValue(provider.getName());
			row.createCell(2).setCellValue(provider.getApiBaseUrl());
			row.createCell(3).setCellValue(provider.getApiType());
			row.createCell(4).setCellValue(provider.getAdapterClass());
			row.createCell(5).setCellValue(provider.getRequestTimeout());
			row.createCell(6).setCellValue(provider.getDefaultModel());
			row.createCell(7).setCellValue(provider.getStatus() ? "启用" : "禁用");
			row.createCell(8).setCellValue(provider.getPriority());
			row.createCell(9).setCellValue(provider.getRemark());
		}
	}

	/**
	 * 创建模型工作表
	 */
	private void createModelSheet(Sheet sheet, List<LlmModel> models) {
		// 创建标题行
		Row headerRow = sheet.createRow(0);
		String[] headers = { "ID", "提供商ID", "模型名称", "模型标识符", "最大Token", "输入价格", "输出价格", "上下文长度", "能力", "描述", "状态" };

		for (int i = 0; i < headers.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(headers[i]);
		}

		// 填充数据
		for (int i = 0; i < models.size(); i++) {
			Row row = sheet.createRow(i + 1);
			LlmModel model = models.get(i);

			row.createCell(0).setCellValue(model.getId());
			row.createCell(1).setCellValue(model.getProviderId());
			row.createCell(2).setCellValue(model.getModelName());
			row.createCell(3).setCellValue(model.getModelIdentifier());
			row.createCell(4).setCellValue(model.getMaxTokens());
			row.createCell(5).setCellValue(model.getInputPrice() != null ? model.getInputPrice().doubleValue() : 0);
			row.createCell(6).setCellValue(model.getOutputPrice() != null ? model.getOutputPrice().doubleValue() : 0);
			row.createCell(7).setCellValue(model.getContextLength());
			row.createCell(8).setCellValue(model.getCapabilities());
			row.createCell(9).setCellValue(model.getDescription());
			row.createCell(10).setCellValue(model.getStatus() ? "启用" : "禁用");
		}
	}

	/**
	 * 从Excel导入大模型配置
	 */
	public Ret importFromExcel(HttpServletRequest request) {
		try {
			// 这里需要处理文件上传，具体实现根据你的文件上传组件
			// MultipartFile file = ((MultipartHttpServletRequest) request).getFile("file");
			// 暂时返回成功，实际项目中需要实现文件解析逻辑
			return success("导入功能待实现");
		} catch (Exception e) {
			LogKit.error("导入大模型配置失败", e);
			return fail("导入失败：" + e.getMessage());
		}
	}

	/**
	 * 下载导入模板
	 */
	public void downloadTemplate(HttpServletResponse response) {
		try {
			// 创建工作簿
			Workbook workbook = new XSSFWorkbook();

			// 创建提供商模板
			Sheet providerSheet = workbook.createSheet("大模型提供商模板");
			Row headerRow = providerSheet.createRow(0);
			String[] headers = { "厂商名称*", "API基础URL*", "API密钥*", "API密钥2", "API类型*", "适配器类名", "超时时间", "默认模型", "优先级",
					"备注" };

			for (int i = 0; i < headers.length; i++) {
				Cell cell = headerRow.createCell(i);
				cell.setCellValue(headers[i]);
			}

			// 添加示例数据
			Row exampleRow = providerSheet.createRow(1);
			exampleRow.createCell(0).setCellValue("openai");
			exampleRow.createCell(1).setCellValue("https://api.openai.com/v1/chat/completions");
			exampleRow.createCell(2).setCellValue("your-api-key");
			exampleRow.createCell(3).setCellValue("");
			exampleRow.createCell(4).setCellValue("openai");
			exampleRow.createCell(5).setCellValue("");
			exampleRow.createCell(6).setCellValue(60);
			exampleRow.createCell(7).setCellValue("gpt-3.5-turbo");
			exampleRow.createCell(8).setCellValue(1);
			exampleRow.createCell(9).setCellValue("OpenAI官方API");

			// 设置响应头
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition",
					"attachment; filename=" + URLEncoder.encode("大模型配置模板.xlsx", "UTF-8"));

			// 输出文件
			OutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			workbook.close();

		} catch (Exception e) {
			LogKit.error("下载模板失败", e);
		}
	}

	/**
	 * 批量初始化常用大模型
	 */
	public Ret initCommonModels() {
		try {
			// 检查是否已经初始化过
			long count = dao.findFirst("SELECT COUNT(*) as count FROM llm_provider").getLong("count");
			if (count > 0) {
				return fail("系统已存在大模型配置，请手动添加或清空后重新初始化");
			}

			// 初始化提供商数据
			List<LlmProvider> providers = createCommonProviders();
			for (LlmProvider provider : providers) {
				provider.save();
			}

			// 初始化模型数据
			List<LlmModel> models = createCommonModels();
			for (LlmModel model : models) {
				model.save();
			}

			return success("成功初始化 " + providers.size() + " 个提供商和 " + models.size() + " 个模型");

		} catch (Exception e) {
			LogKit.error("初始化常用大模型失败", e);
			return fail("初始化失败：" + e.getMessage());
		}
	}

	/**
	 * 创建常用提供商列表
	 */
	private List<LlmProvider> createCommonProviders() {
		List<LlmProvider> providers = new ArrayList<>();
		Date now = new Date();

		// OpenAI
		providers.add(new LlmProvider()
				.setName("openai")
				.setApiBaseUrl("https://api.openai.com/v1/chat/completions")
				.setApiKey("your-openai-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("gpt-3.5-turbo")
				.setRateLimitPerMinute(20)
				.setStatus(true)
				.setPriority(1)
				.setRemark("OpenAI官方API，支持GPT系列模型")
				.setCreateTime(now)
				.setUpdateTime(now));

		// Claude
		providers.add(new LlmProvider()
				.setName("claude")
				.setApiBaseUrl("https://api.anthropic.com/v1/messages")
				.setApiKey("your-claude-api-key")
				.setApiType("custom")
				.setAdapterClass("cn.jbolt.llm.adapter.ClaudeAdapter")
				.setRequestTimeout(60)
				.setDefaultModel("claude-3-haiku-20240307")
				.setRateLimitPerMinute(10)
				.setStatus(true)
				.setPriority(2)
				.setRemark("Anthropic Claude系列，擅长对话和分析")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 通义千问
		providers.add(new LlmProvider()
				.setName("qwen")
				.setApiBaseUrl("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation")
				.setApiKey("your-qwen-api-key")
				.setApiType("custom")
				.setAdapterClass("cn.jbolt.llm.adapter.QwenAdapter")
				.setRequestTimeout(60)
				.setDefaultModel("qwen-turbo")
				.setRateLimitPerMinute(30)
				.setStatus(true)
				.setPriority(3)
				.setRemark("阿里云通义千问，支持中文对话")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 文心一言
		providers.add(new LlmProvider()
				.setName("ernie")
				.setApiBaseUrl("https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions")
				.setApiKey("your-ernie-api-key")
//				.setApiKey2("your-ernie-secret-key")
				.setApiType("custom")
				.setAdapterClass("cn.jbolt.llm.adapter.ErnieAdapter")
				.setRequestTimeout(60)
				.setDefaultModel("ernie-bot-turbo")
				.setStatus(true)
				.setPriority(4)
				.setRemark("百度文心一言，中文理解能力强")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 智谱GLM
		providers.add(new LlmProvider()
				.setName("zhipu")
				.setApiBaseUrl("https://open.bigmodel.cn/api/paas/v4/chat/completions")
				.setApiKey("your-zhipu-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("glm-4")
				.setStatus(true)
				.setPriority(5)
				.setRemark("智谱AI GLM系列，支持多模态")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 讯飞星火
		providers.add(new LlmProvider()
				.setName("spark")
				.setApiBaseUrl("wss://spark-api.xf-yun.com/v3.5/chat")
				.setApiKey("your-spark-app-id")
//				.setApiKey2("your-spark-api-secret")
				.setApiType("custom")
				.setAdapterClass("cn.jbolt.llm.adapter.SparkAdapter")
				.setRequestTimeout(60)
				.setDefaultModel("generalv3.5")
				.setStatus(true)
				.setPriority(6)
				.setRemark("科大讯飞星火认知大模型")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 月之暗面Kimi
		providers.add(new LlmProvider()
				.setName("moonshot")
				.setApiBaseUrl("https://api.moonshot.cn/v1/chat/completions")
				.setApiKey("your-moonshot-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("moonshot-v1-8k")
				.setStatus(true)
				.setPriority(7)
				.setRemark("月之暗面Kimi，超长上下文")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 豆包
		providers.add(new LlmProvider()
				.setName("doubao")
				.setApiBaseUrl("https://ark.cn-beijing.volces.com/api/v3/chat/completions")
				.setApiKey("your-doubao-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("ep-20240611091441-2v8kg")
				.setStatus(true)
				.setPriority(8)
				.setRemark("字节跳动豆包大模型")
				.setCreateTime(now)
				.setUpdateTime(now));

		// DeepSeek
		providers.add(new LlmProvider()
				.setName("deepseek")
				.setApiBaseUrl("https://api.deepseek.com/chat/completions")
				.setApiKey("your-deepseek-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("deepseek-chat")
				.setStatus(true)
				.setPriority(9)
				.setRemark("DeepSeek深度求索，代码能力强")
				.setCreateTime(now)
				.setUpdateTime(now));

		// 零一万物
		providers.add(new LlmProvider()
				.setName("yi")
				.setApiBaseUrl("https://api.lingyiwanwu.com/v1/chat/completions")
				.setApiKey("your-yi-api-key")
				.setApiType("openai")
				.setRequestTimeout(60)
				.setDefaultModel("yi-34b-chat-0205")
				.setStatus(true)
				.setPriority(10)
				.setRemark("零一万物Yi系列模型")
				.setCreateTime(now)
				.setUpdateTime(now));

		return providers;
	}

	/**
	 * 创建常用模型列表
	 */
	private List<LlmModel> createCommonModels() {
		List<LlmModel> models = new ArrayList<>();
		Date now = new Date();

		// OpenAI模型
		models.add(new LlmModel()
				.setProviderId(1L)
				.setModelName("GPT-3.5 Turbo")
				.setModelIdentifier("gpt-3.5-turbo")
				.setMaxTokens(4096)
				.setContextLength(16385)
				.setCapabilities("text,image")
				.setDescription("OpenAI GPT-3.5 Turbo，性价比高的对话模型")
				.setStatus(true)
				.setCreateTime(now)
				.setUpdateTime(now));

		// 其他模型...

		return models;
	}

	/**
	 * 测试大模型连接
	 */
	public Ret testConnection(Long providerId) {
		try {
			LlmProvider provider = findById(providerId);
			if (provider == null) {
				return fail("提供商不存在");
			}

			// 使用LlmService测试连接
			String testPrompt = "Hello, this is a connection test.";
			String response = LlmService.me().callLlm(provider, provider.getDefaultModel(), testPrompt);

			if (response != null && !response.contains("error")) {
				return success("连接测试成功", response);
			} else {
				return fail("连接测试失败：" + response);
			}

		} catch (Exception e) {
			LogKit.error("测试大模型连接失败", e);
			return fail("连接测试失败：" + e.getMessage());
		}
	}
}