package cn.jbolt.mail.gpt.service;

import cn.jbolt.common.model.AiPrompt;
import cn.jbolt.common.model.EmailTranslationConfig;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

/**
 * 配置化翻译服务测试
 */
public class ConfigurableTranslationServiceTest {
    
    private ConfigurableTranslationService service;
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
        service = ConfigurableTranslationService.getInstance();
    }
    
    /**
     * 测试1：验证提示词获取
     */
    @Test
    public void testPromptRetrieval() {
        System.out.println("=== 测试1：验证提示词获取 ===");
        
        try {
            // 查询现有的翻译提示词
            AiPrompt aiPrompt = new AiPrompt().dao().findFirst(
                "SELECT * FROM ai_prompt WHERE enable = '1' AND `key` = 'email_monument_translate' ORDER BY id LIMIT 1"
            );
            
            if (aiPrompt != null) {
                System.out.println("✓ 找到翻译提示词");
                System.out.println("提示词ID: " + aiPrompt.getId());
                System.out.println("提示词Key: " + aiPrompt.getKey());
                System.out.println("系统内容: " + (aiPrompt.getSystemContent() != null ? 
                                 aiPrompt.getSystemContent().substring(0, Math.min(100, aiPrompt.getSystemContent().length())) + "..." : "无"));
                System.out.println("用户内容: " + (aiPrompt.getUserContent() != null ? 
                                 aiPrompt.getUserContent().substring(0, Math.min(100, aiPrompt.getUserContent().length())) + "..." : "无"));
            } else {
                System.out.println("✗ 未找到翻译提示词");
                System.out.println("请确保在ai_prompt表中存在key为'email_monument_translate'且enable=1的记录");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询提示词失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：验证配置获取
     */
    @Test
    public void testConfigRetrieval() {
        System.out.println("=== 测试2：验证配置获取 ===");
        
        try {
            // 测试主要提供商配置
            EmailTranslationConfig.TranslationProviderConfig primary = EmailTranslationConfig.getPrimaryProvider();
            System.out.println("✓ 主要提供商: " + primary.toString());
            
            // 测试备用提供商配置
            EmailTranslationConfig.TranslationProviderConfig backup = EmailTranslationConfig.getBackupProvider();
            System.out.println("✓ 备用提供商: " + backup.toString());
            
            // 测试重试配置
            EmailTranslationConfig.RetryConfig retry = EmailTranslationConfig.getRetryConfig();
            System.out.println("✓ 重试配置: 最大" + retry.getMaxAttempts() + "次，延迟" + retry.getDelaySeconds() + "秒");
            
            // 测试功能开关
            System.out.println("✓ 图片翻译: " + (service.isImageTranslationEnabled() ? "启用" : "禁用"));
            System.out.println("✓ 批量处理: " + (service.isBatchProcessingEnabled() ? "启用" : "禁用"));
            System.out.println("✓ 批量大小: " + service.getBatchSize());
            System.out.println("✓ 超时时间: " + service.getTimeoutSeconds() + "秒");
            
        } catch (Exception e) {
            System.err.println("✗ 获取配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：验证配置摘要
     */
    @Test
    public void testConfigSummary() {
        System.out.println("=== 测试3：验证配置摘要 ===");
        
        try {
            String summary = service.getConfigSummary();
            System.out.println("✓ 配置摘要: " + summary);
            
        } catch (Exception e) {
            System.err.println("✗ 获取配置摘要失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：模拟翻译调用（不实际调用LLM）
     */
    @Test
    public void testTranslationCall() {
        System.out.println("=== 测试4：模拟翻译调用 ===");
        
        try {
            // 测试文本翻译（这里只测试配置获取，不实际调用LLM）
            System.out.println("准备翻译标题...");
            
            // 获取提示词
            AiPrompt aiPrompt = new AiPrompt().dao().findFirst(
                "SELECT * FROM ai_prompt WHERE enable = '1' AND `key` = 'email_monument_translate' ORDER BY id LIMIT 1"
            );
            
            String testText = "Hello, this is a test email subject.";
            String prompt = testText;
            
            if (aiPrompt != null && aiPrompt.getSystemContent() != null) {
                prompt = aiPrompt.getSystemContent() + "\n" + testText;
                System.out.println("✓ 使用AI提示词构建prompt");
            } else {
                System.out.println("⚠ 使用默认prompt");
            }
            
            System.out.println("测试文本: " + testText);
            System.out.println("完整prompt长度: " + prompt.length() + " 字符");
            System.out.println("✓ 翻译调用准备完成（未实际调用LLM）");
            
        } catch (Exception e) {
            System.err.println("✗ 模拟翻译调用失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 测试5：验证数据库配置表
     */
    @Test
    public void testDatabaseConfig() {
        System.out.println("=== 测试5：验证数据库配置表 ===");
        
        try {
            // 检查配置表是否存在
            String primaryProvider = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PRIMARY_PROVIDER);
            String primaryModel = EmailTranslationConfig.getConfigValue(EmailTranslationConfig.PRIMARY_MODEL);
            
            if (primaryProvider != null) {
                System.out.println("✓ 配置表可访问");
                System.out.println("主要提供商: " + primaryProvider);
                System.out.println("主要模型: " + primaryModel);
                
                // 检查其他配置
                Integer maxRetries = EmailTranslationConfig.getIntConfigValue(EmailTranslationConfig.MAX_RETRY_ATTEMPTS, 3);
                Boolean imageEnabled = EmailTranslationConfig.getBooleanConfigValue(EmailTranslationConfig.ENABLE_IMAGE_TRANSLATION, true);
                
                System.out.println("最大重试次数: " + maxRetries);
                System.out.println("图片翻译启用: " + imageEnabled);
                
            } else {
                System.out.println("✗ 配置表不存在或无数据");
                System.out.println("请执行 src/main/resources/sql/email_translation_config.sql 创建配置表");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 访问配置表失败: " + e.getMessage());
            System.err.println("请确保已执行数据库初始化脚本");
            e.printStackTrace();
        }
        
        System.out.println("测试5完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("配置化翻译服务测试");
        System.out.println("========================================");
        
        testPromptRetrieval();
        testConfigRetrieval();
        testConfigSummary();
        testTranslationCall();
        testDatabaseConfig();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n使用说明:");
        System.out.println("1. 确保已执行数据库脚本: src/main/resources/sql/email_translation_config.sql");
        System.out.println("2. 在AI提示词管理中配置key为'email_monument_translate'的提示词");
        System.out.println("3. 访问配置管理界面: /admin/email/translation/config");
        System.out.println("4. 配置主要和备用翻译提供商");
    }
}
