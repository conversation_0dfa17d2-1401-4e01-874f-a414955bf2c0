package cn.jbolt.llm.manager;

import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * LLM API密钥重新加载测试
 */
public class LlmApiKeyReloadTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查数据库中的API密钥配置
     */
    @Test
    public void testDatabaseApiKeyConfig() {
        System.out.println("=== 测试1：检查数据库中的API密钥配置 ===");
        
        try {
            // 查询gemini提供商
            LlmProvider geminiProvider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = 'gemini'");
            
            if (geminiProvider == null) {
                System.out.println("✗ 数据库中未找到gemini提供商");
                System.out.println("请执行以下SQL创建gemini提供商:");
                System.out.println("INSERT INTO llm_provider (name, api_type, api_base_url, api_key, status, priority, default_model, adapter_class, rate_limit_per_minute)");
                System.out.println("VALUES ('gemini', 'gemini', 'https://generativelanguage.googleapis.com/v1beta/models/', 'YOUR_API_KEYS_HERE', 1, 1, 'gemini-2.5-pro', 'cn.jbolt.llm.adapter.GeminiAdapter', 15);");
                return;
            }
            
            System.out.println("✓ 找到gemini提供商:");
            System.out.println("  ID: " + geminiProvider.getId());
            System.out.println("  名称: " + geminiProvider.getName());
            System.out.println("  状态: " + (geminiProvider.getStatus() == 1 ? "启用" : "禁用"));
            System.out.println("  优先级: " + geminiProvider.getPriority());
            System.out.println("  频率限制: " + geminiProvider.getRateLimitPerMinute() + "/分钟");
            
            String apiKey = geminiProvider.getApiKey();
            if (apiKey == null || apiKey.trim().isEmpty()) {
                System.out.println("✗ API密钥为空");
                System.out.println("请执行以下SQL设置API密钥:");
                System.out.println("UPDATE llm_provider SET api_key = 'YOUR_COMMA_SEPARATED_API_KEYS' WHERE name = 'gemini';");
            } else {
                // 分析API密钥
                String[] keys = apiKey.split(",");
                System.out.println("✓ API密钥配置:");
                System.out.println("  原始长度: " + apiKey.length() + " 字符");
                System.out.println("  密钥数量: " + keys.length + " 个");
                
                for (int i = 0; i < keys.length; i++) {
                    String key = keys[i].trim();
                    if (!key.isEmpty()) {
                        String masked = key.length() > 8 ? 
                            key.substring(0, 4) + "****" + key.substring(key.length() - 4) : "****";
                        System.out.println("  密钥" + (i + 1) + ": " + masked + " (长度: " + key.length() + ")");
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查数据库配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：重新加载API密钥并验证
     */
    @Test
    public void testReloadApiKeys() {
        System.out.println("=== 测试2：重新加载API密钥并验证 ===");
        
        try {
            LlmApiKeyManager keyManager = LlmApiKeyManager.me();
            
            System.out.println("步骤1: 重新加载API密钥配置");
            keyManager.reload();
            
            System.out.println("步骤2: 检查gemini提供商的密钥池");
            List<LlmApiKeyManager.ApiKeyInfo> geminiKeys = keyManager.getProviderKeys("gemini");
            
            if (geminiKeys.isEmpty()) {
                System.out.println("✗ gemini提供商没有加载到任何API密钥");
                System.out.println("可能的原因:");
                System.out.println("1. llm_provider表中gemini记录的status不是1");
                System.out.println("2. api_key字段为空或null");
                System.out.println("3. 数据库连接问题");
            } else {
                System.out.println("✓ 成功加载 " + geminiKeys.size() + " 个API密钥:");
                
                for (int i = 0; i < geminiKeys.size(); i++) {
                    LlmApiKeyManager.ApiKeyInfo keyInfo = geminiKeys.get(i);
                    String masked = maskApiKey(keyInfo.getApiKey());
                    System.out.println("  密钥" + (i + 1) + ": " + masked + 
                                     " (频率限制: " + keyInfo.getRateLimitPerMinute() + "/分钟, " +
                                     "优先级: " + keyInfo.getPriority() + ", " +
                                     "状态: " + (keyInfo.isEnabled() ? "启用" : "禁用") + ")");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 重新加载API密钥失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：获取可用API密钥
     */
    @Test
    public void testGetAvailableApiKey() {
        System.out.println("=== 测试3：获取可用API密钥 ===");
        
        try {
            LlmApiKeyManager keyManager = LlmApiKeyManager.me();
            
            System.out.println("步骤1: 尝试获取gemini的可用API密钥");
            LlmApiKeyManager.ApiKeyInfo keyInfo = keyManager.getAvailableApiKey("gemini");
            
            if (keyInfo == null) {
                System.out.println("✗ 无法获取可用的API密钥");
                System.out.println("可能的原因:");
                System.out.println("1. 没有配置API密钥");
                System.out.println("2. 所有API密钥都被禁用");
                System.out.println("3. 所有API密钥都达到了频率限制");
                
                // 检查密钥池状态
                List<LlmApiKeyManager.ApiKeyInfo> allKeys = keyManager.getProviderKeys("gemini");
                System.out.println("当前密钥池状态: " + allKeys.size() + " 个密钥");
                
            } else {
                System.out.println("✓ 成功获取可用API密钥:");
                System.out.println("  提供商: " + keyInfo.getProviderName());
                System.out.println("  密钥: " + maskApiKey(keyInfo.getApiKey()));
                System.out.println("  频率限制: " + keyInfo.getRateLimitPerMinute() + "/分钟");
                System.out.println("  优先级: " + keyInfo.getPriority());
                System.out.println("  状态: " + (keyInfo.isEnabled() ? "启用" : "禁用"));
            }
            
            System.out.println("\n步骤2: 连续获取5次，测试轮询机制");
            for (int i = 1; i <= 5; i++) {
                LlmApiKeyManager.ApiKeyInfo key = keyManager.getAvailableApiKey("gemini");
                if (key != null) {
                    System.out.println("第" + i + "次: " + maskApiKey(key.getApiKey()));
                } else {
                    System.out.println("第" + i + "次: 无可用密钥");
                }
                Thread.sleep(100); // 短暂等待
            }
            
        } catch (Exception e) {
            System.err.println("✗ 获取可用API密钥失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：修复API密钥配置
     */
    @Test
    public void testFixApiKeyConfiguration() {
        System.out.println("=== 测试4：修复API密钥配置 ===");
        
        try {
            String expectedApiKeys = "AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog";
            
            System.out.println("步骤1: 检查当前配置");
            LlmProvider geminiProvider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = 'gemini'");
            
            if (geminiProvider == null) {
                System.out.println("✗ gemini提供商不存在，需要创建");
                System.out.println("请执行SQL: sql/fix_gemini_api_keys.sql");
                return;
            }
            
            String currentApiKey = geminiProvider.getApiKey();
            System.out.println("当前API密钥长度: " + (currentApiKey != null ? currentApiKey.length() : 0));
            System.out.println("期望API密钥长度: " + expectedApiKeys.length());
            
            if (currentApiKey == null || currentApiKey.trim().isEmpty()) {
                System.out.println("✗ API密钥为空，需要设置");
                System.out.println("请执行以下SQL:");
                System.out.println("UPDATE llm_provider SET api_key = '" + expectedApiKeys + "' WHERE name = 'gemini';");
            } else if (!expectedApiKeys.equals(currentApiKey.trim())) {
                System.out.println("⚠ API密钥不匹配，可能需要更新");
                System.out.println("如果需要更新，请执行:");
                System.out.println("UPDATE llm_provider SET api_key = '" + expectedApiKeys + "' WHERE name = 'gemini';");
            } else {
                System.out.println("✓ API密钥配置正确");
            }
            
            System.out.println("\n步骤2: 验证状态和其他配置");
            System.out.println("状态: " + (geminiProvider.getStatus() == 1 ? "✓ 启用" : "✗ 禁用"));
            System.out.println("频率限制: " + geminiProvider.getRateLimitPerMinute() + "/分钟");
            
            if (geminiProvider.getStatus() != 1) {
                System.out.println("需要启用提供商:");
                System.out.println("UPDATE llm_provider SET status = 1 WHERE name = 'gemini';");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 修复配置检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("LLM API密钥重新加载测试");
        System.out.println("========================================");
        
        testDatabaseApiKeyConfig();
        testReloadApiKeys();
        testGetAvailableApiKey();
        testFixApiKeyConfiguration();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n修复步骤总结:");
        System.out.println("1. 执行 sql/fix_gemini_api_keys.sql 修复数据库配置");
        System.out.println("2. 重启应用或调用 LlmApiKeyManager.me().reload() 重新加载配置");
        System.out.println("3. 重新测试翻译功能");
        
        System.out.println("\n关键点:");
        System.out.println("- API密钥应该存储在 llm_provider 表的 api_key 字段中");
        System.out.println("- 多个密钥用逗号分隔");
        System.out.println("- 提供商的 status 字段必须为 1（启用）");
        System.out.println("- 系统会自动解析逗号分隔的密钥并实现轮询");
    }
    
    /**
     * 掩码显示API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
