#@jboltLayout()
#define css()
<link rel="stylesheet" href="/_view/admin/email/translation/test.css">
#end
#define main()
#set(pageId=RandomUtil.random(6))
<div class="jbolt_page" data-key="email_translation_test">
<div class="jbolt_page_title">
<div class="row">
    <div class="col-sm-auto"><h1><i class="jbicon2 jbi-flask"></i>翻译功能测试</h1></div>
    <div class="col">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-success btn-sm" onclick="runAllTests()">
                <i class="fa fa-play-circle"></i> 运行全部测试
            </button>
            <button type="button" class="btn btn-info btn-sm" onclick="clearAllResults()">
                <i class="fa fa-eraser"></i> 清空结果
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="exportResults()">
                <i class="fa fa-download"></i> 导出结果
            </button>
            <a href="/admin/email/translation/config" class="btn btn-warning btn-sm">
                <i class="fa fa-cog"></i> 翻译配置
            </a>
        </div>
    </div>
</div>
</div>

<div class="jbolt_page_content">
    <!-- 测试配置区域 -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">测试配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label>测试提供商</label>
                            <select class="form-control" id="testProvider" onchange="loadTestModels()">
                                <option value="">选择提供商</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>测试模型</label>
                            <select class="form-control" id="testModel">
                                <option value="">选择模型</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>目标语言</label>
                            <select class="form-control" id="targetLanguage">
                                <option value="zh-CN">中文</option>
                                <option value="en">英文</option>
                                <option value="ja">日文</option>
                                <option value="ko">韩文</option>
                                <option value="fr">法文</option>
                                <option value="de">德文</option>
                                <option value="es">西班牙文</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>并发测试数</label>
                            <input type="number" class="form-control" id="concurrentTests" value="1" min="1" max="5">
                        </div>
                        <div class="col-md-1">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-warning btn-block btn-sm" onclick="debugParams()" title="调试参数传递">
                                <i class="fa fa-bug"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试项目区域 -->
    <div class="row">
        <!-- 文本翻译测试 -->
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">📝 文本翻译测试</h5>
                    <button class="btn btn-primary btn-sm" onclick="runTextTest()">
                        <i class="fa fa-play"></i> 测试
                    </button>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>测试文本类型</label>
                        <select class="form-control" id="textType">
                            <option value="simple">简单文本</option>
                            <option value="email">邮件内容</option>
                            <option value="html">HTML内容</option>
                            <option value="markdown">Markdown内容</option>
                            <option value="technical">技术文档</option>
                            <option value="business">商务邮件</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>输入文本</label>
                        <textarea class="form-control" id="textInput" rows="4" placeholder="输入要测试翻译的文本">Hello, this is a comprehensive test for translation functionality. We need to verify that the AI model can handle various types of content including technical terms, business language, and casual conversation.</textarea>
                    </div>
                    <div id="textResult" class="test-result" style="display: none;">
                        <div class="alert alert-info">
                            <strong>测试结果：</strong>
                            <div id="textResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片翻译测试 -->
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">🖼️ 图片翻译测试</h5>
                    <button class="btn btn-primary btn-sm" onclick="runImageTest()">
                        <i class="fa fa-play"></i> 测试
                    </button>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>图片类型</label>
                        <select class="form-control" id="imageType">
                            <option value="screenshot">屏幕截图</option>
                            <option value="document">文档图片</option>
                            <option value="chart">图表</option>
                            <option value="handwriting">手写文字</option>
                            <option value="sign">标识牌</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>上传图片</label>
                        <input type="file" class="form-control-file" id="imageInput" accept="image/*" multiple>
                        <small class="form-text text-muted">支持多张图片同时测试</small>
                    </div>
                    <div class="form-group">
                        <label>或使用测试图片URL</label>
                        <input type="text" class="form-control" id="imageUrl" placeholder="输入图片URL">
                    </div>
                    <div id="imageResult" class="test-result" style="display: none;">
                        <div class="alert alert-info">
                            <strong>测试结果：</strong>
                            <div id="imageResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 音频翻译测试 -->
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">🎵 音频翻译测试</h5>
                    <button class="btn btn-primary btn-sm" onclick="runAudioTest()">
                        <i class="fa fa-play"></i> 测试
                    </button>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>音频类型</label>
                        <select class="form-control" id="audioType">
                            <option value="speech">语音</option>
                            <option value="meeting">会议录音</option>
                            <option value="interview">访谈</option>
                            <option value="presentation">演讲</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>上传音频</label>
                        <input type="file" class="form-control-file" id="audioInput" accept="audio/*">
                        <small class="form-text text-muted">支持MP3, WAV, M4A等格式</small>
                    </div>
                    <div class="form-group">
                        <label>或使用测试音频URL</label>
                        <input type="text" class="form-control" id="audioUrl" placeholder="输入音频URL">
                    </div>
                    <div id="audioResult" class="test-result" style="display: none;">
                        <div class="alert alert-info">
                            <strong>测试结果：</strong>
                            <div id="audioResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频翻译测试 -->
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">🎬 视频翻译测试</h5>
                    <button class="btn btn-primary btn-sm" onclick="runVideoTest()">
                        <i class="fa fa-play"></i> 测试
                    </button>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>视频类型</label>
                        <select class="form-control" id="videoType">
                            <option value="tutorial">教程视频</option>
                            <option value="meeting">会议视频</option>
                            <option value="presentation">演示视频</option>
                            <option value="interview">访谈视频</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>上传视频</label>
                        <input type="file" class="form-control-file" id="videoInput" accept="video/*">
                        <small class="form-text text-muted">支持MP4, AVI, MOV等格式</small>
                    </div>
                    <div class="form-group">
                        <label>或使用测试视频URL</label>
                        <input type="text" class="form-control" id="videoUrl" placeholder="输入视频URL">
                    </div>
                    <div id="videoResult" class="test-result" style="display: none;">
                        <div class="alert alert-info">
                            <strong>测试结果：</strong>
                            <div id="videoResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量测试区域 -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">📊 批量测试与性能分析</h5>
                    <button class="btn btn-success btn-sm" onclick="runBatchTest()">
                        <i class="fa fa-tasks"></i> 批量测试
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label>批量测试内容</label>
                                <textarea class="form-control" id="batchInput" rows="6" placeholder="每行一个测试内容，支持文本、图片URL、音频URL、视频URL">Hello, how are you today?
Good morning, I hope you have a great day!
This is a technical document about artificial intelligence.
Please translate this business email content.
https://example.com/test-image.jpg
https://example.com/test-audio.mp3</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>测试统计</label>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h4 id="totalTests">0</h4>
                                                <small>总测试数</small>
                                            </div>
                                            <div class="col-6">
                                                <h4 id="successTests">0</h4>
                                                <small>成功数</small>
                                            </div>
                                        </div>
                                        <div class="row text-center mt-2">
                                            <div class="col-6">
                                                <h4 id="avgTime">0ms</h4>
                                                <small>平均耗时</small>
                                            </div>
                                            <div class="col-6">
                                                <h4 id="successRate">0%</h4>
                                                <small>成功率</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="batchResult" class="test-result" style="display: none;">
                        <div class="alert alert-success">
                            <strong>批量测试结果：</strong>
                            <div id="batchResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试历史记录 -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📈 测试历史记录</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm" id="testHistoryTable">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>类型</th>
                                    <th>提供商</th>
                                    <th>模型</th>
                                    <th>状态</th>
                                    <th>耗时</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

#define js()
<script>
let providers = [];
let testHistory = [];
let currentTestId = 0;

$(document).ready(function() {
    loadProviders();
    initializeTestData();
});

// 加载提供商列表
function loadProviders() {
    console.log('开始加载提供商列表...');
    $.get('/admin/email/translation/config/providers', function(data) {
        console.log('提供商数据响应:', data);
        if (data.state === 'ok') {
            providers = data.data;

            let providerOptions = '<option value="">选择提供商</option>';
            providers.forEach(function(provider) {
                providerOptions += `<option value="${provider.name}">${provider.name}</option>`;
            });

            $('#testProvider').html(providerOptions);
            console.log('提供商选项已更新，数量:', providers.length);

            // 自动选择gemini提供商（如果存在）
            const geminiProvider = providers.find(p => p.name === 'gemini');
            if (geminiProvider) {
                $('#testProvider').val('gemini');
                console.log('自动选择gemini提供商');
                loadTestModels(); // 自动加载模型
            }
        } else {
            console.error('加载提供商失败:', data.msg);
            showToast('加载提供商失败: ' + data.msg, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('提供商请求失败:', error);
        showToast('提供商请求失败: ' + error, 'error');
    });
}

// 加载测试模型
function loadTestModels() {
    let provider = $('#testProvider').val();
    console.log('加载模型，提供商:', provider);

    if (!provider) {
        $('#testModel').html('<option value="">请先选择提供商</option>');
        return;
    }

    $.get('/admin/email/translation/config/models', {provider: provider}, function(data) {
        console.log('模型数据响应:', data);
        if (data.state === 'ok') {
            let modelOptions = '<option value="">选择模型</option>';
            data.data.forEach(function(model) {
                modelOptions += `<option value="${model.model_identifier}">${model.model_name || model.model_identifier}</option>`;
            });
            $('#testModel').html(modelOptions);
            console.log('模型选项已更新，数量:', data.data.length);

            // 自动选择gemini-2.5-pro模型（如果存在）
            const geminiModel = data.data.find(m => m.model_identifier === 'gemini-2.5-pro');
            if (geminiModel) {
                $('#testModel').val('gemini-2.5-pro');
                console.log('自动选择gemini-2.5-pro模型');
            }
        } else {
            console.error('加载模型失败:', data.msg);
            showToast('加载模型失败: ' + data.msg, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('模型请求失败:', error);
        showToast('模型请求失败: ' + error, 'error');
    });
}

// 初始化测试数据
function initializeTestData() {
    // 预设一些测试文本
    const testTexts = {
        simple: "Hello, how are you today?",
        email: "Dear Sir/Madam,\n\nI hope this email finds you well. I am writing to inquire about your services.\n\nBest regards,\nJohn Smith",
        html: "<h1>Welcome</h1><p>This is a <strong>test</strong> HTML content with <a href='#'>links</a>.</p>",
        markdown: "# Title\n\n## Subtitle\n\n- Item 1\n- Item 2\n\n**Bold text** and *italic text*.",
        technical: "The API endpoint returns a JSON response with status code 200 for successful requests.",
        business: "We are pleased to inform you that your proposal has been approved. Please proceed with the next phase."
    };
    
    $('#textType').on('change', function() {
        const selectedType = $(this).val();
        if (testTexts[selectedType]) {
            $('#textInput').val(testTexts[selectedType]);
        }
    });
}

// 运行文本翻译测试
function runTextTest() {
    const testText = $('#textInput').val();
    const textType = $('#textType').val();
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();

    if (!testText || !provider || !model) {
        showToast('请填写完整的测试信息', 'warning');
        return;
    }

    const testId = ++currentTestId;
    const startTime = Date.now();

    showTestProgress('textResult', '正在翻译文本...');

    $.post('/admin/email/translation/config/testTranslation', {
        testText: testText,
        provider: provider,
        model: model,
        contentType: 'text',
        textType: textType,
        targetLanguage: targetLang
    }, function(data) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        if (data.state === 'ok') {
            const result = data.data;
            showTestResult('textResult', 'textResultContent', {
                type: '文本翻译',
                provider: result.provider,
                model: result.model,
                original: testText,
                translated: result.translatedText,
                duration: duration,
                success: true
            });

            addTestHistory(testId, '文本翻译', provider, model, true, duration, {
                textType: textType,
                originalLength: testText.length,
                translatedLength: result.translatedText.length
            });
        } else {
            showTestResult('textResult', 'textResultContent', {
                type: '文本翻译',
                provider: provider,
                model: model,
                error: data.msg,
                duration: duration,
                success: false
            });

            addTestHistory(testId, '文本翻译', provider, model, false, duration, {
                error: data.msg
            });
        }
    }).fail(function() {
        const duration = Date.now() - startTime;
        showTestResult('textResult', 'textResultContent', {
            type: '文本翻译',
            error: '网络请求失败',
            duration: duration,
            success: false
        });

        addTestHistory(testId, '文本翻译', provider, model, false, duration, {
            error: '网络请求失败'
        });
    });
}

// 运行图片翻译测试
function runImageTest() {
    const imageFile = $('#imageInput')[0].files[0];
    const imageUrl = $('#imageUrl').val();
    const imageType = $('#imageType').val();
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();

    // 调试信息
    console.log('图片翻译测试参数:', {
        provider: provider,
        model: model,
        imageType: imageType,
        targetLang: targetLang,
        hasImageFile: !!imageFile,
        imageUrl: imageUrl
    });

    if ((!imageFile && !imageUrl) || !provider || !model) {
        showToast('请选择图片文件或输入图片URL，并配置提供商和模型', 'warning');
        console.error('参数验证失败:', {
            hasImage: !!(imageFile || imageUrl),
            provider: provider,
            model: model
        });
        return;
    }

    const testId = ++currentTestId;
    const startTime = Date.now();

    showTestProgress('imageResult', '正在分析图片并翻译...');

    // 创建FormData用于文件上传
    const formData = new FormData();
    if (imageFile) {
        formData.append('imageFile', imageFile);
    }
    if (imageUrl) {
        formData.append('imageUrl', imageUrl);
    }
    formData.append('provider', provider);
    formData.append('model', model);
    formData.append('contentType', 'image');
    formData.append('imageType', imageType);
    formData.append('targetLanguage', targetLang);

    $.ajax({
        url: '/admin/email/translation/config/testImageTranslation',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            if (data.state === 'ok') {
                const result = data.data;
                showTestResult('imageResult', 'imageResultContent', {
                    type: '图片翻译',
                    provider: result.provider,
                    model: result.model,
                    original: result.extractedText || '图片内容',
                    translated: result.translatedText,
                    duration: duration,
                    success: true,
                    imageInfo: result.imageInfo
                });

                addTestHistory(testId, '图片翻译', provider, model, true, duration, {
                    imageType: imageType,
                    extractedTextLength: result.extractedText ? result.extractedText.length : 0,
                    translatedLength: result.translatedText.length
                });
            } else {
                showTestResult('imageResult', 'imageResultContent', {
                    type: '图片翻译',
                    provider: provider,
                    model: model,
                    error: data.msg,
                    duration: duration,
                    success: false
                });

                addTestHistory(testId, '图片翻译', provider, model, false, duration, {
                    error: data.msg
                });
            }
        },
        error: function() {
            const duration = Date.now() - startTime;
            showTestResult('imageResult', 'imageResultContent', {
                type: '图片翻译',
                error: '网络请求失败',
                duration: duration,
                success: false
            });

            addTestHistory(testId, '图片翻译', provider, model, false, duration, {
                error: '网络请求失败'
            });
        }
    });
}

// 运行音频翻译测试
function runAudioTest() {
    const audioFile = $('#audioInput')[0].files[0];
    const audioUrl = $('#audioUrl').val();
    const audioType = $('#audioType').val();
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();

    if ((!audioFile && !audioUrl) || !provider || !model) {
        showToast('请选择音频文件或输入音频URL，并配置提供商和模型', 'warning');
        return;
    }

    const testId = ++currentTestId;
    const startTime = Date.now();

    showTestProgress('audioResult', '正在转录音频并翻译...');

    // 创建FormData用于文件上传
    const formData = new FormData();
    if (audioFile) {
        formData.append('audioFile', audioFile);
    }
    if (audioUrl) {
        formData.append('audioUrl', audioUrl);
    }
    formData.append('provider', provider);
    formData.append('model', model);
    formData.append('contentType', 'audio');
    formData.append('audioType', audioType);
    formData.append('targetLanguage', targetLang);

    $.ajax({
        url: '/admin/email/translation/config/testAudioTranslation',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            if (data.state === 'ok') {
                const result = data.data;
                showTestResult('audioResult', 'audioResultContent', {
                    type: '音频翻译',
                    provider: result.provider,
                    model: result.model,
                    original: result.transcribedText || '音频内容',
                    translated: result.translatedText,
                    duration: duration,
                    success: true,
                    audioInfo: result.audioInfo
                });

                addTestHistory(testId, '音频翻译', provider, model, true, duration, {
                    audioType: audioType,
                    transcribedTextLength: result.transcribedText ? result.transcribedText.length : 0,
                    translatedLength: result.translatedText.length
                });
            } else {
                showTestResult('audioResult', 'audioResultContent', {
                    type: '音频翻译',
                    provider: provider,
                    model: model,
                    error: data.msg,
                    duration: duration,
                    success: false
                });

                addTestHistory(testId, '音频翻译', provider, model, false, duration, {
                    error: data.msg
                });
            }
        },
        error: function() {
            const duration = Date.now() - startTime;
            showTestResult('audioResult', 'audioResultContent', {
                type: '音频翻译',
                error: '网络请求失败',
                duration: duration,
                success: false
            });

            addTestHistory(testId, '音频翻译', provider, model, false, duration, {
                error: '网络请求失败'
            });
        }
    });
}

// 运行视频翻译测试
function runVideoTest() {
    const videoFile = $('#videoInput')[0].files[0];
    const videoUrl = $('#videoUrl').val();
    const videoType = $('#videoType').val();
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();

    if ((!videoFile && !videoUrl) || !provider || !model) {
        showToast('请选择视频文件或输入视频URL，并配置提供商和模型', 'warning');
        return;
    }

    const testId = ++currentTestId;
    const startTime = Date.now();

    showTestProgress('videoResult', '正在提取视频内容并翻译...');

    // 创建FormData用于文件上传
    const formData = new FormData();
    if (videoFile) {
        formData.append('videoFile', videoFile);
    }
    if (videoUrl) {
        formData.append('videoUrl', videoUrl);
    }
    formData.append('provider', provider);
    formData.append('model', model);
    formData.append('contentType', 'video');
    formData.append('videoType', videoType);
    formData.append('targetLanguage', targetLang);

    $.ajax({
        url: '/admin/email/translation/config/testVideoTranslation',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            if (data.state === 'ok') {
                const result = data.data;
                showTestResult('videoResult', 'videoResultContent', {
                    type: '视频翻译',
                    provider: result.provider,
                    model: result.model,
                    original: result.extractedContent || '视频内容',
                    translated: result.translatedText,
                    duration: duration,
                    success: true,
                    videoInfo: result.videoInfo
                });

                addTestHistory(testId, '视频翻译', provider, model, true, duration, {
                    videoType: videoType,
                    extractedContentLength: result.extractedContent ? result.extractedContent.length : 0,
                    translatedLength: result.translatedText.length
                });
            } else {
                showTestResult('videoResult', 'videoResultContent', {
                    type: '视频翻译',
                    provider: provider,
                    model: model,
                    error: data.msg,
                    duration: duration,
                    success: false
                });

                addTestHistory(testId, '视频翻译', provider, model, false, duration, {
                    error: data.msg
                });
            }
        },
        error: function() {
            const duration = Date.now() - startTime;
            showTestResult('videoResult', 'videoResultContent', {
                type: '视频翻译',
                error: '网络请求失败',
                duration: duration,
                success: false
            });

            addTestHistory(testId, '视频翻译', provider, model, false, duration, {
                error: '网络请求失败'
            });
        }
    });
}

// 运行批量测试
function runBatchTest() {
    const batchInput = $('#batchInput').val();
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();
    const concurrentTests = parseInt($('#concurrentTests').val()) || 1;

    if (!batchInput || !provider || !model) {
        showToast('请填写批量测试内容并配置提供商和模型', 'warning');
        return;
    }

    const testItems = batchInput.split('\n').filter(item => item.trim());
    if (testItems.length === 0) {
        showToast('请输入有效的测试内容', 'warning');
        return;
    }

    showTestProgress('batchResult', '正在执行批量测试...');

    let completedTests = 0;
    let successfulTests = 0;
    let totalDuration = 0;
    const results = [];

    // 更新统计显示
    $('#totalTests').text(testItems.length);
    $('#successTests').text(0);
    $('#avgTime').text('0ms');
    $('#successRate').text('0%');

    // 分批处理测试项目
    const batches = [];
    for (let i = 0; i < testItems.length; i += concurrentTests) {
        batches.push(testItems.slice(i, i + concurrentTests));
    }

    // 递归处理每个批次
    function processBatch(batchIndex) {
        if (batchIndex >= batches.length) {
            // 所有批次完成，显示最终结果
            showBatchResults(results, totalDuration, successfulTests, testItems.length);
            return;
        }

        const batch = batches[batchIndex];
        const batchPromises = batch.map(item => testSingleItem(item, provider, model, targetLang));

        Promise.allSettled(batchPromises).then(batchResults => {
            batchResults.forEach((result, index) => {
                completedTests++;
                if (result.status === 'fulfilled' && result.value.success) {
                    successfulTests++;
                    totalDuration += result.value.duration;
                }
                results.push(result.value || { success: false, error: result.reason });

                // 更新统计显示
                $('#successTests').text(successfulTests);
                $('#avgTime').text(Math.round(totalDuration / completedTests) + 'ms');
                $('#successRate').text(Math.round((successfulTests / completedTests) * 100) + '%');
            });

            // 处理下一个批次
            setTimeout(() => processBatch(batchIndex + 1), 1000);
        });
    }

    // 开始处理第一个批次
    processBatch(0);
}

// 测试单个项目
function testSingleItem(item, provider, model, targetLang) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        const testId = ++currentTestId;

        // 判断内容类型
        let contentType = 'text';
        let requestData = {
            provider: provider,
            model: model,
            targetLanguage: targetLang
        };

        if (item.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i) || item.startsWith('data:image/')) {
            contentType = 'image';
            requestData.imageUrl = item;
            requestData.contentType = 'image';
        } else if (item.match(/\.(mp3|wav|m4a|aac|ogg)$/i)) {
            contentType = 'audio';
            requestData.audioUrl = item;
            requestData.contentType = 'audio';
        } else if (item.match(/\.(mp4|avi|mov|wmv|flv|webm)$/i)) {
            contentType = 'video';
            requestData.videoUrl = item;
            requestData.contentType = 'video';
        } else {
            requestData.testText = item;
            requestData.contentType = 'text';
        }

        // 选择合适的API端点
        let apiUrl = '/admin/email/translation/config/testTranslation';
        if (contentType === 'image') {
            apiUrl = '/admin/email/translation/config/testImageTranslation';
        } else if (contentType === 'audio') {
            apiUrl = '/admin/email/translation/config/testAudioTranslation';
        } else if (contentType === 'video') {
            apiUrl = '/admin/email/translation/config/testVideoTranslation';
        }

        $.post(apiUrl, requestData)
            .done(function(data) {
                const duration = Date.now() - startTime;
                const result = {
                    testId: testId,
                    item: item,
                    contentType: contentType,
                    provider: provider,
                    model: model,
                    duration: duration,
                    success: data.state === 'ok',
                    translatedText: data.state === 'ok' ? data.data.translatedText : null,
                    error: data.state !== 'ok' ? data.msg : null
                };

                addTestHistory(testId, contentType + '翻译', provider, model, result.success, duration, {
                    batchTest: true,
                    originalContent: item.substring(0, 50) + (item.length > 50 ? '...' : '')
                });

                resolve(result);
            })
            .fail(function() {
                const duration = Date.now() - startTime;
                const result = {
                    testId: testId,
                    item: item,
                    contentType: contentType,
                    provider: provider,
                    model: model,
                    duration: duration,
                    success: false,
                    error: '网络请求失败'
                };

                addTestHistory(testId, contentType + '翻译', provider, model, false, duration, {
                    batchTest: true,
                    error: '网络请求失败'
                });

                resolve(result);
            });
    });
}

// 显示批量测试结果
function showBatchResults(results, totalDuration, successfulTests, totalTests) {
    let resultHtml = `
        <div class="row">
            <div class="col-md-12">
                <h6>批量测试完成</h6>
                <p>总测试数: ${totalTests}, 成功: ${successfulTests}, 失败: ${totalTests - successfulTests}</p>
                <p>成功率: ${Math.round((successfulTests / totalTests) * 100)}%, 平均耗时: ${Math.round(totalDuration / totalTests)}ms</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>内容</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>耗时</th>
                                <th>结果预览</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    results.forEach(result => {
        const statusBadge = result.success ?
            '<span class="badge badge-success">成功</span>' :
            '<span class="badge badge-danger">失败</span>';

        const contentPreview = result.item.length > 30 ?
            result.item.substring(0, 30) + '...' : result.item;

        const resultPreview = result.success ?
            (result.translatedText && result.translatedText.length > 50 ?
                result.translatedText.substring(0, 50) + '...' : result.translatedText || '') :
            (result.error || '未知错误');

        resultHtml += `
            <tr>
                <td title="${result.item}">${contentPreview}</td>
                <td>${result.contentType}</td>
                <td>${statusBadge}</td>
                <td>${result.duration}ms</td>
                <td title="${result.success ? result.translatedText : result.error}">${resultPreview}</td>
            </tr>
        `;
    });

    resultHtml += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    $('#batchResultContent').html(resultHtml);
    $('#batchResult').show();
}

// 调试参数传递
function debugParams() {
    const provider = $('#testProvider').val();
    const model = $('#testModel').val();
    const targetLang = $('#targetLanguage').val();

    console.log('调试参数传递:', {
        provider: provider,
        model: model,
        targetLang: targetLang
    });

    // 创建FormData测试
    const formData = new FormData();
    formData.append('provider', provider);
    formData.append('model', model);
    formData.append('targetLanguage', targetLang);
    formData.append('testType', 'debug');

    console.log('FormData内容:');
    for (let [key, value] of formData.entries()) {
        console.log('  ' + key + ' = ' + value);
    }

    $.ajax({
        url: '/admin/email/translation/config/debugParams',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            console.log('调试响应:', data);
            if (data.state === 'ok') {
                showToast('参数调试成功，请查看控制台和服务器日志', 'success');

                // 显示调试结果
                let debugInfo = `
                    <strong>调试结果：</strong><br>
                    getPara方式 - provider: ${data.data.getPara_provider || 'null'}<br>
                    getPara方式 - model: ${data.data.getPara_model || 'null'}<br>
                    getParameter方式 - provider: ${data.data.getParameter_provider || 'null'}<br>
                    getParameter方式 - model: ${data.data.getParameter_model || 'null'}<br>
                    Content-Type: ${data.data.contentType || 'null'}<br>
                    Method: ${data.data.method || 'null'}
                `;

                // 在页面上显示调试信息
                if ($('#debugResult').length === 0) {
                    $('<div id="debugResult" class="alert alert-warning mt-3"><strong>调试信息：</strong><div id="debugContent"></div></div>')
                        .insertAfter($('#testProvider').closest('.row'));
                }
                $('#debugContent').html(debugInfo);
                $('#debugResult').show();

            } else {
                showToast('参数调试失败: ' + data.msg, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('调试请求失败:', error);
            showToast('调试请求失败: ' + error, 'error');
        }
    });
}

// 运行全部测试
function runAllTests() {
    if (!$('#testProvider').val() || !$('#testModel').val()) {
        showToast('请先配置测试提供商和模型', 'warning');
        return;
    }

    showToast('开始运行全部测试，请稍候...', 'info');

    // 依次运行各种测试
    setTimeout(() => runTextTest(), 500);
    setTimeout(() => runImageTest(), 2000);
    setTimeout(() => runAudioTest(), 4000);
    setTimeout(() => runVideoTest(), 6000);
    setTimeout(() => runBatchTest(), 8000);
}

// 清空所有结果
function clearAllResults() {
    $('.test-result').hide();
    testHistory = [];
    currentTestId = 0;
    updateTestHistoryTable();

    $('#totalTests').text('0');
    $('#successTests').text('0');
    $('#avgTime').text('0ms');
    $('#successRate').text('0%');

    showToast('所有测试结果已清空', 'success');
}

// 导出测试结果
function exportResults() {
    if (testHistory.length === 0) {
        showToast('没有测试结果可以导出', 'warning');
        return;
    }

    const csvContent = generateCSVContent();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `translation_test_results_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('测试结果已导出', 'success');
}

// 生成CSV内容
function generateCSVContent() {
    let csv = '时间,测试ID,类型,提供商,模型,状态,耗时(ms),详细信息\n';

    testHistory.forEach(test => {
        const status = test.success ? '成功' : '失败';
        const details = JSON.stringify(test.details || {}).replace(/"/g, '""');

        csv += `${test.timestamp},${test.testId},${test.type},${test.provider},${test.model},${status},${test.duration},"${details}"\n`;
    });

    return csv;
}

// 显示测试进度
function showTestProgress(resultId, message) {
    $(`#${resultId}`).show();
    $(`#${resultId} .alert`).removeClass('alert-info alert-success alert-danger').addClass('alert-warning');
    $(`#${resultId} .alert strong`).text('测试中：');
    $(`#${resultId} .alert div`).html(`<i class="fa fa-spinner fa-spin"></i> ${message}`);
}

// 显示测试结果
function showTestResult(resultId, contentId, result) {
    const alertClass = result.success ? 'alert-success' : 'alert-danger';
    const statusText = result.success ? '测试成功：' : '测试失败：';

    $(`#${resultId} .alert`).removeClass('alert-warning alert-info alert-success alert-danger').addClass(alertClass);
    $(`#${resultId} .alert strong`).text(statusText);

    let resultHtml = `
        <div class="row">
            <div class="col-md-6">
                <strong>提供商:</strong> ${result.provider || '未知'}<br>
                <strong>模型:</strong> ${result.model || '未知'}<br>
                <strong>类型:</strong> ${result.type}<br>
                <strong>耗时:</strong> ${result.duration}ms
            </div>
            <div class="col-md-6">
    `;

    if (result.success) {
        resultHtml += `
                <strong>原文:</strong><br>
                <div class="border p-2 mb-2" style="max-height: 100px; overflow-y: auto; font-size: 0.9em;">
                    ${escapeHtml(result.original || '').substring(0, 200)}${(result.original || '').length > 200 ? '...' : ''}
                </div>
                <strong>译文:</strong><br>
                <div class="border p-2" style="max-height: 100px; overflow-y: auto; font-size: 0.9em; background-color: #f8f9fa;">
                    ${escapeHtml(result.translated || '')}
                </div>
        `;

        if (result.imageInfo) {
            resultHtml += `<br><small class="text-muted">图片信息: ${JSON.stringify(result.imageInfo)}</small>`;
        }
        if (result.audioInfo) {
            resultHtml += `<br><small class="text-muted">音频信息: ${JSON.stringify(result.audioInfo)}</small>`;
        }
        if (result.videoInfo) {
            resultHtml += `<br><small class="text-muted">视频信息: ${JSON.stringify(result.videoInfo)}</small>`;
        }
    } else {
        resultHtml += `
                <strong>错误信息:</strong><br>
                <div class="alert alert-danger p-2" style="font-size: 0.9em;">
                    ${escapeHtml(result.error || '未知错误')}
                </div>
        `;
    }

    resultHtml += `
            </div>
        </div>
    `;

    $(`#${contentId}`).html(resultHtml);
    $(`#${resultId}`).show();
}

// 添加测试历史记录
function addTestHistory(testId, type, provider, model, success, duration, details) {
    const historyItem = {
        testId: testId,
        timestamp: new Date().toLocaleString('zh-CN'),
        type: type,
        provider: provider,
        model: model,
        success: success,
        duration: duration,
        details: details || {}
    };

    testHistory.unshift(historyItem); // 添加到开头

    // 限制历史记录数量
    if (testHistory.length > 100) {
        testHistory = testHistory.slice(0, 100);
    }

    updateTestHistoryTable();
}

// 更新测试历史表格
function updateTestHistoryTable() {
    const tbody = $('#testHistoryTable tbody');
    tbody.empty();

    testHistory.forEach(test => {
        const statusBadge = test.success ?
            '<span class="badge badge-success">成功</span>' :
            '<span class="badge badge-danger">失败</span>';

        const detailsBtn = `<button class="btn btn-sm btn-outline-info" onclick="showTestDetails(${test.testId})">详情</button>`;

        const row = `
            <tr>
                <td>${test.timestamp}</td>
                <td>${test.type}</td>
                <td>${test.provider}</td>
                <td>${test.model}</td>
                <td>${statusBadge}</td>
                <td>${test.duration}ms</td>
                <td>${detailsBtn}</td>
            </tr>
        `;

        tbody.append(row);
    });
}

// 显示测试详情
function showTestDetails(testId) {
    const test = testHistory.find(t => t.testId === testId);
    if (!test) {
        showToast('测试记录不存在', 'error');
        return;
    }

    const detailsHtml = `
        <div class="modal fade" id="testDetailsModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">测试详情 - ${test.type} (ID: ${test.testId})</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td>测试时间</td><td>${test.timestamp}</td></tr>
                                    <tr><td>测试类型</td><td>${test.type}</td></tr>
                                    <tr><td>提供商</td><td>${test.provider}</td></tr>
                                    <tr><td>模型</td><td>${test.model}</td></tr>
                                    <tr><td>状态</td><td>${test.success ? '成功' : '失败'}</td></tr>
                                    <tr><td>耗时</td><td>${test.duration}ms</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>详细信息</h6>
                                <pre class="bg-light p-2" style="max-height: 200px; overflow-y: auto; font-size: 0.8em;">${JSON.stringify(test.details, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#testDetailsModal').remove();

    // 添加新的模态框并显示
    $('body').append(detailsHtml);
    $('#testDetailsModal').modal('show');
}

// HTML转义函数
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示提示信息
function showToast(message, type) {
    // 简单的提示实现，可以根据实际UI框架调整
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const toast = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(toast);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
#end
#end
