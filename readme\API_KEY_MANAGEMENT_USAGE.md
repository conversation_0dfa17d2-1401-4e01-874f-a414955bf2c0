# API密钥管理功能使用指南

## 功能概述

新增的API密钥管理功能包括：

### 1. 数据库增强
- ✅ `llm_provider` 表新增 `rate_limit_per_minute` 字段
- ✅ 新增 `llm_api_key_usage` 统计表
- ✅ 新增 `llm_api_request_log` 日志表
- ✅ 新增统计视图和存储过程

### 2. 后端功能
- ✅ `LlmApiKeyManager` - 核心密钥管理器
- ✅ `LlmApiKeyStatsController` - 统计管理控制器
- ✅ 增强的 `LlmService` - 支持负载均衡
- ✅ 更新的模型类 - 支持频率限制字段

### 3. 前端界面
- ✅ 提供商管理页面增加频率限制字段
- ✅ API密钥使用统计页面
- ✅ 实时监控和管理功能

## 使用步骤

### 第一步：执行数据库迁移

```sql
-- 执行数据库迁移脚本
source src/main/resources/sql/llm_api_key_enhancement.sql;
```

### 第二步：配置多个API密钥

1. **通过管理界面配置**：
   - 访问：`/admin/llmProvider`
   - 编辑提供商，在API密钥字段中用逗号分隔多个密钥
   - 设置每分钟请求限制数量

2. **通过SQL直接配置**：
```sql
-- 配置多个Gemini API密钥
UPDATE llm_provider SET 
  api_key = 'AIzaXXXXXXXXXXXXXXXX1,AIzaXXXXXXXXXXXXXXXX2,AIzaXXXXXXXXXXXXXXXX3',
  rate_limit_per_minute = 15
WHERE name = 'gemini';
```

### 第三步：查看统计信息

访问统计页面：`/admin/llm/apikey/stats`

页面包含四个选项卡：
- **提供商概览** - 各提供商使用情况汇总
- **详细统计** - 每个API密钥的详细使用数据
- **实时状态** - 当前分钟的使用情况
- **密钥配置** - 所有配置的密钥信息

## 界面功能说明

### 1. 提供商管理页面增强

**新增字段**：
- **频率限制** - 设置每分钟最大请求数
- **说明文字** - 解释多密钥配置方式

**表单验证**：
- 频率限制必须为正整数
- 默认值为10次/分钟

### 2. API密钥统计页面

**概览卡片**：
- 活跃提供商数量
- API密钥总数
- 总请求数
- 被阻塞密钥数

**管理功能**：
- 🔄 **刷新数据** - 更新所有统计信息
- 🔓 **重置阻塞** - 解除被阻塞的API密钥
- ⚙️ **重载配置** - 重新加载密钥配置
- 🗑️ **清理日志** - 删除过期的请求日志

**实时监控**：
- 自动刷新功能（每5秒更新一次）
- 当前分钟请求数实时显示
- 密钥状态实时更新

## 配置示例

### 1. 单个提供商多密钥配置

```sql
-- Gemini提供商配置3个API密钥
UPDATE llm_provider SET 
  api_key = 'AIzaSyABC123...,AIzaSyDEF456...,AIzaSyGHI789...',
  api_secret = NULL,
  rate_limit_per_minute = 15
WHERE name = 'gemini';
```

### 2. 不同优先级密钥配置

```sql
-- 设置不同优先级（通过priority字段间接实现）
-- 高优先级密钥
INSERT INTO llm_provider (name, api_key, rate_limit_per_minute, priority) 
VALUES ('gemini-high', 'AIzaSyHIGH123...', 20, 1);

-- 中优先级密钥
INSERT INTO llm_provider (name, api_key, rate_limit_per_minute, priority) 
VALUES ('gemini-medium', 'AIzaSyMED456...', 15, 2);
```

### 3. 频率限制建议配置

| 提供商 | 建议限制 | 说明 |
|--------|----------|------|
| OpenAI | 20/分钟 | 根据官方限制调整 |
| Gemini | 15/分钟 | 免费版限制较低 |
| Claude | 10/分钟 | 较为保守的设置 |
| 通义千问 | 30/分钟 | 国内服务限制较宽松 |
| 智谱AI | 20/分钟 | 中等限制 |

## 监控和维护

### 1. 日常监控

**关键指标**：
- 成功率 - 应保持在95%以上
- 被阻塞密钥数 - 应尽量为0
- 当前分钟请求数 - 不应接近限制值

**告警条件**：
- 成功率低于90%
- 超过50%的密钥被阻塞
- 单个密钥持续达到频率限制

### 2. 定期维护

**每日任务**：
- 检查统计页面，确认系统正常运行
- 查看是否有密钥需要重置

**每周任务**：
- 清理过期日志：`CALL CleanupApiRequestLogs(30);`
- 检查密钥使用分布是否均匀

**每月任务**：
- 评估频率限制设置是否合理
- 检查是否需要增加新的API密钥

### 3. 故障处理

**常见问题**：

1. **所有密钥都被阻塞**
   ```sql
   -- 重置所有阻塞状态
   CALL ResetBlockedApiKeys();
   ```

2. **某个提供商请求失败率高**
   - 检查API密钥是否有效
   - 确认网络连接正常
   - 查看错误日志定位问题

3. **负载不均衡**
   - 检查密钥优先级设置
   - 确认所有密钥都是有效的
   - 重新加载配置

## 开发集成

### 1. 代码中使用（无需修改）

```java
// 原有代码保持不变，系统自动使用负载均衡
String response = LlmService.me().callLlm(
    "gemini", 
    "gemini-2.5-pro", 
    "你好，这是一个测试请求"
);
```

### 2. 获取统计信息

```java
// 获取密钥使用统计
Map<String, LlmApiKeyManager.ApiKeyUsage> stats = 
    LlmApiKeyManager.me().getUsageStatistics();

// 获取可用密钥
LlmApiKeyManager.ApiKeyInfo keyInfo = 
    LlmApiKeyManager.me().getAvailableApiKey("gemini");
```

### 3. 手动管理

```java
// 重新加载配置
LlmApiKeyManager.me().reload();

// 标记密钥出错
LlmApiKeyManager.me().markApiKeyError(apiKey, 60 * 1000);
```

## 总结

通过这套API密钥管理系统，你可以：

✅ **突破频率限制** - 通过多密钥轮询提高并发能力
✅ **提高可用性** - 自动故障转移和恢复
✅ **实时监控** - 详细的使用统计和状态监控
✅ **简单维护** - 可视化管理界面和自动化工具
✅ **零代码改动** - 现有业务代码无需修改

这个系统可以有效解决单个API密钥频率限制的问题，让你的应用能够处理更高的并发请求。
