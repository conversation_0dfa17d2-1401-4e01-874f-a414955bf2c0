@echo off
setlocal enabledelayedexpansion

echo === 超长文件名解决方案测试 ===
echo.

REM 检查是否存在超长文件名
echo 1. 检查当前超长文件名...
set LONG_FILES_COUNT=0
set UPLOAD_DIR=src\main\webapp\upload\email\attachment

if exist "%UPLOAD_DIR%" (
    for /r "%UPLOAD_DIR%" %%f in (*.*) do (
        set filename=%%~nxf
        set filename_length=0
        set temp_filename=!filename!
        
        REM 计算文件名长度
        :count_loop
        if "!temp_filename!"=="" goto count_done
        set temp_filename=!temp_filename:~1!
        set /a filename_length+=1
        goto count_loop
        
        :count_done
        if !filename_length! gtr 80 (
            set /a LONG_FILES_COUNT+=1
            echo   发现超长文件名: !filename! ^(!filename_length! 字符^)
        )
    )
    
    if !LONG_FILES_COUNT! equ 0 (
        echo   ✓ 没有发现超长文件名
    ) else (
        echo   ✗ 发现 !LONG_FILES_COUNT! 个超长文件名
    )
) else (
    echo   ⚠ 上传目录不存在: %UPLOAD_DIR%
)

echo.

REM 检查Maven配置
echo 2. 检查Maven配置...
if exist "pom.xml" (
    findstr /C:"3.6.0" pom.xml >nul
    if !errorlevel! equ 0 (
        echo   ✓ Maven Assembly插件已升级到3.6.0
    ) else (
        echo   ✗ Maven Assembly插件版本需要升级
    )
    
    findstr /C:"tarLongFileMode" pom.xml >nul
    if !errorlevel! equ 0 (
        echo   ✓ 已配置tar长文件名支持
    ) else (
        echo   ✗ 未配置tar长文件名支持
    )
) else (
    echo   ✗ 找不到pom.xml文件
)

echo.

REM 检查package.xml配置
echo 3. 检查打包配置...
if exist "package.xml" (
    findstr /C:"Sélection des Produits Funéraires" package.xml >nul
    if !errorlevel! equ 0 (
        echo   ✓ 已配置排除超长文件名规则
    ) else (
        echo   ⚠ 未配置排除超长文件名规则
    )
) else (
    echo   ✗ 找不到package.xml文件
)

echo.

REM 检查工具脚本
echo 4. 检查工具脚本...
if exist "scripts\clean-long-filenames.bat" (
    echo   ✓ Windows清理脚本已就绪
) else (
    echo   ✗ Windows清理脚本不存在
)

if exist "scripts\clean-long-filenames.sh" (
    echo   ✓ Linux清理脚本已就绪
) else (
    echo   ✗ Linux清理脚本不存在
)

if exist "src\main\java\cn\jbolt\common\util\FileNameUtil.java" (
    echo   ✓ FileNameUtil工具类已就绪
) else (
    echo   ✗ FileNameUtil工具类不存在
)

echo.

REM 提供操作建议
echo === 操作建议 ===
if !LONG_FILES_COUNT! gtr 0 (
    echo 发现超长文件名，建议执行以下操作：
    echo.
    echo 1. 运行清理脚本（预览模式）：
    echo    scripts\clean-long-filenames.bat --dry-run
    echo.
    echo 2. 确认无误后执行清理：
    echo    scripts\clean-long-filenames.bat
    echo.
    echo 3. 重新打包测试：
    echo    mvn clean package
) else (
    echo 当前没有超长文件名问题，可以直接打包：
    echo    mvn clean package
)

echo.
echo === 测试完成 ===

pause
