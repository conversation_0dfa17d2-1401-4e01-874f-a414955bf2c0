# 参数传递调试指南

## 🔍 **问题现象**

用户反馈：前端选择框有值，但传到后台都是null

```
前端显示：
- 提供商: gemini
- 模型: gemini-2.5-pro

后端接收：
- provider: null
- model: null
```

## 🛠️ **调试工具**

我已经添加了专门的调试功能：

### 1. 后端调试接口

新增了 `debugParams()` 方法，会详细记录：
- `getPara()` 方式获取的参数
- `getRequest().getParameter()` 方式获取的参数
- 所有请求参数的完整列表
- 请求的Content-Type和Method

### 2. 前端调试按钮

在测试页面添加了 🐛 调试按钮，点击后会：
- 在控制台显示前端参数值
- 显示FormData的完整内容
- 发送调试请求到后端
- 在页面显示调试结果

## 🚀 **使用步骤**

### 步骤1：重启应用

确保新的调试代码生效：
```bash
# 重启应用服务器
```

### 步骤2：访问测试页面

打开：`http://127.0.0.1:8001/admin/email/translation/config/test`

### 步骤3：打开浏览器开发者工具

按F12打开开发者工具，切换到Console标签

### 步骤4：选择提供商和模型

确保下拉框中选择了：
- 提供商：gemini
- 模型：gemini-2.5-pro

### 步骤5：点击调试按钮

点击页面上的 🐛 按钮，观察：

**浏览器控制台输出**：
```javascript
调试参数传递: {
    provider: "gemini",
    model: "gemini-2.5-pro", 
    targetLang: "zh-CN"
}

FormData内容:
  provider = gemini
  model = gemini-2.5-pro
  targetLanguage = zh-CN
  testType = debug
```

**页面显示的调试结果**：
```
调试结果：
getPara方式 - provider: gemini (或null)
getPara方式 - model: gemini-2.5-pro (或null)
getParameter方式 - provider: gemini (或null)
getParameter方式 - model: gemini-2.5-pro (或null)
Content-Type: multipart/form-data; boundary=...
Method: POST
```

**服务器日志**：
```
[INFO] === 调试参数接收 ===
[INFO] getPara方式:
[INFO]   provider: gemini
[INFO]   model: gemini-2.5-pro
[INFO] getParameter方式:
[INFO]   provider: gemini
[INFO]   model: gemini-2.5-pro
[INFO] 所有请求参数:
[INFO]   provider = gemini
[INFO]   model = gemini-2.5-pro
[INFO]   targetLanguage = zh-CN
[INFO]   testType = debug
```

## 🔧 **问题诊断**

### 情况1：前端参数为空

**症状**：控制台显示 `provider: null` 或 `provider: ""`

**原因**：下拉框没有正确加载或选择

**解决方案**：
1. 检查提供商和模型是否正确加载
2. 手动选择提供商和模型
3. 检查JavaScript是否有错误

### 情况2：FormData为空

**症状**：FormData内容显示空值

**原因**：JavaScript获取DOM元素值失败

**解决方案**：
1. 检查元素ID是否正确
2. 确认页面完全加载后再操作
3. 检查是否有JavaScript错误

### 情况3：后端接收为null

**症状**：前端有值，但后端日志显示null

**原因**：参数接收方式不匹配

**可能的解决方案**：

#### 方案A：修改前端发送方式
```javascript
// 改为普通POST请求，不使用FormData
$.post('/admin/email/translation/config/testImageTranslation', {
    provider: provider,
    model: model,
    targetLanguage: targetLang,
    imageType: imageType
}, function(data) {
    // 处理响应
});
```

#### 方案B：修改后端接收方式
```java
// 使用@RequestParam注解
public void testImageTranslation(@RequestParam("provider") String providerName,
                                @RequestParam("model") String modelName) {
    // 处理逻辑
}
```

#### 方案C：使用JFinal的文件上传处理
```java
// 使用getFile()和getPara()组合
public void testImageTranslation() {
    UploadFile uploadFile = getFile("imageFile");
    String providerName = getPara("provider");
    String modelName = getPara("model");
    // ...
}
```

## 📊 **常见问题及解决方案**

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 下拉框未加载 | 选项为空 | 检查API接口，确保数据正确返回 |
| JavaScript错误 | 控制台有错误 | 修复JavaScript语法或逻辑错误 |
| 参数名不匹配 | 后端收不到参数 | 确保前后端参数名一致 |
| Content-Type问题 | multipart请求参数丢失 | 使用正确的参数接收方式 |
| 编码问题 | 中文参数乱码 | 设置正确的字符编码 |

## 🎯 **预期结果**

调试完成后，应该看到：

1. **前端正常**：
   - 下拉框正确显示选项
   - 控制台显示正确的参数值
   - FormData包含完整参数

2. **后端正常**：
   - 服务器日志显示接收到的参数
   - 参数值与前端发送的一致
   - 不再出现"提供商和模型不能为空"错误

3. **功能正常**：
   - 图片翻译测试成功执行
   - 返回正确的翻译结果

## 🔄 **下一步**

根据调试结果：

1. **如果参数正常传递**：问题可能在业务逻辑中，检查翻译服务
2. **如果参数传递失败**：根据具体情况选择上述解决方案
3. **如果仍有问题**：提供详细的调试日志进行进一步分析

使用这个调试工具，我们可以精确定位参数传递问题的根源！
