package cn.jbolt.llm.util;

import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

/**
 * 代理配置测试
 */
public class ProxyConfigTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查代理配置
     */
    @Test
    public void testProxyConfiguration() {
        System.out.println("=== 测试1：检查代理配置 ===");
        
        try {
            boolean isConfigured = ProxyConfigUtil.isProxyConfigured();
            String proxyInfo = ProxyConfigUtil.getProxyInfo();
            
            System.out.println("代理配置状态: " + (isConfigured ? "已配置" : "未配置"));
            System.out.println("代理信息: " + proxyInfo);
            
            if (isConfigured) {
                System.out.println("✓ 代理配置正常，将使用代理访问外部API");
            } else {
                System.out.println("⚠ 未配置代理，将直连访问外部API");
                System.out.println("如果访问Gemini API有网络问题，请配置代理:");
                System.out.println("1. 执行 sql/set_proxy_config.sql");
                System.out.println("2. 或在配置界面设置代理: 127.0.0.1:7890");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查代理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：设置代理配置
     */
    @Test
    public void testSetProxyConfig() {
        System.out.println("=== 测试2：设置代理配置 ===");
        
        try {
            // 设置本地代理（Clash默认配置）
            String proxyHost = "127.0.0.1";
            String proxyPort = "7890";
            
            System.out.println("设置代理配置: " + proxyHost + ":" + proxyPort);
            
            boolean success = ProxyConfigUtil.setProxyConfig(proxyHost, proxyPort);
            
            if (success) {
                System.out.println("✓ 代理配置设置成功");
                
                // 验证配置
                String proxyInfo = ProxyConfigUtil.getProxyInfo();
                System.out.println("当前代理配置: " + proxyInfo);
                
            } else {
                System.out.println("✗ 代理配置设置失败");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 设置代理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：测试代理连接
     */
    @Test
    public void testProxyConnection() {
        System.out.println("=== 测试3：测试代理连接 ===");
        
        try {
            if (!ProxyConfigUtil.isProxyConfigured()) {
                System.out.println("⚠ 未配置代理，跳过连接测试");
                return;
            }
            
            System.out.println("测试代理连接...");
            
            long startTime = System.currentTimeMillis();
            boolean connected = ProxyConfigUtil.testProxyConnection();
            long endTime = System.currentTimeMillis();
            
            if (connected) {
                System.out.println("✓ 代理连接测试成功");
                System.out.println("响应时间: " + (endTime - startTime) + "ms");
            } else {
                System.out.println("✗ 代理连接测试失败");
                System.out.println("可能的原因:");
                System.out.println("1. 代理服务未启动");
                System.out.println("2. 代理配置错误");
                System.out.println("3. 网络连接问题");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 代理连接测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：创建带代理的HTTP客户端
     */
    @Test
    public void testCreateHttpClientWithProxy() {
        System.out.println("=== 测试4：创建带代理的HTTP客户端 ===");
        
        try {
            okhttp3.OkHttpClient client = ProxyConfigUtil.createHttpClientWithProxy();
            
            System.out.println("✓ HTTP客户端创建成功");
            System.out.println("连接超时: " + client.connectTimeoutMillis() + "ms");
            System.out.println("读取超时: " + client.readTimeoutMillis() + "ms");
            System.out.println("写入超时: " + client.writeTimeoutMillis() + "ms");
            
            if (client.proxy() != null) {
                System.out.println("代理设置: " + client.proxy().address());
            } else {
                System.out.println("代理设置: 直连");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 创建HTTP客户端失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 测试5：清除代理配置
     */
    @Test
    public void testClearProxyConfig() {
        System.out.println("=== 测试5：清除代理配置 ===");
        
        try {
            System.out.println("清除代理配置...");
            
            boolean success = ProxyConfigUtil.clearProxyConfig();
            
            if (success) {
                System.out.println("✓ 代理配置清除成功");
                
                // 验证清除结果
                String proxyInfo = ProxyConfigUtil.getProxyInfo();
                System.out.println("当前代理配置: " + proxyInfo);
                
            } else {
                System.out.println("✗ 代理配置清除失败");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 清除代理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试5完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("代理配置测试");
        System.out.println("========================================");
        
        testProxyConfiguration();
        testSetProxyConfig();
        testProxyConnection();
        testCreateHttpClientWithProxy();
        // testClearProxyConfig(); // 注释掉，避免意外清除配置
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n使用说明:");
        System.out.println("1. 如果Gemini API连接失败，执行 sql/set_proxy_config.sql 设置代理");
        System.out.println("2. 确保本地代理服务（如Clash）正在运行");
        System.out.println("3. 常用代理配置: 127.0.0.1:7890 (Clash) 或 127.0.0.1:1080 (其他)");
        System.out.println("4. 配置后重启应用或重新测试翻译功能");
        
        System.out.println("\n故障排除:");
        System.out.println("- SSL握手失败 → 设置代理");
        System.out.println("- 连接超时 → 检查代理服务状态");
        System.out.println("- 代理连接失败 → 验证代理配置和端口");
    }
}
