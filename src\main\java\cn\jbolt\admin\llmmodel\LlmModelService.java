package cn.jbolt.admin.llmmodel;

import com.jfinal.plugin.activerecord.Page;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import cn.jbolt.core.service.base.JBoltBaseService;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.llm.service.LlmService;
import com.jfinal.kit.LogKit;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 大模型具体模型表
 * @ClassName: LlmModelService
 * @author: 系统管理员
 * @date: 2025-01-15
 */
public class LlmModelService extends JBoltBaseService<LlmModel> {
	private final LlmModel dao = new LlmModel().dao();

	@Override
	protected LlmModel dao() {
		return dao;
	}

	@Override
	protected int systemLogTargetType() {
		return ProjectSystemLogTargetType.NONE.getValue();
	}

	/**
	 * 后台管理数据查询
	 * @param pageNumber 第几页
	 * @param pageSize   每页几条数据
	 * @param keywords   关键词
	 * @param sortColumn  排序列名
	 * @param sortType  排序方式 asc desc
	 * @param providerId 提供商ID
	 * @param status 状态
	 * @return
	 */
	public Page<LlmModel> getAdminDatas(int pageNumber, int pageSize, String keywords, 
			String sortColumn, String sortType, Long providerId, Boolean status) {
		// 创建sql对象
		Sql sql = selectSql().page(pageNumber, pageSize);
		// sql条件处理
		sql.eq("provider_id", providerId);
		sql.eqBooleanToChar("status", status);
		// 关键词模糊查询
		sql.likeMulti(keywords, "model_name", "model_identifier", "description");
		// 排序
		sql.orderBy(sortColumn, sortType);
		return paginate(sql);
	}

	/**
	 * 保存
	 * @param llmModel
	 * @return
	 */
	public Ret save(LlmModel llmModel) {
		if (llmModel == null || isOk(llmModel.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		
		// 设置创建时间
		llmModel.setCreateTime(new Date()).setUpdateTime(new Date());
		
		boolean success = llmModel.save();
		if (success) {
			// 添加日志
			// addSaveSystemLog(llmModel.getId(), JBoltUserKit.getUserId(), llmModel.getModelName());
		}
		return ret(success);
	}

	/**
	 * 更新
	 * @param llmModel
	 * @return
	 */
	public Ret update(LlmModel llmModel) {
		if (llmModel == null || notOk(llmModel.getId())) {
			return fail(JBoltMsg.PARAM_ERROR);
		}
		
		// 更新时需要判断数据存在
		LlmModel dbLlmModel = findById(llmModel.getId());
		if (dbLlmModel == null) {
			return fail(JBoltMsg.DATA_NOT_EXIST);
		}
		
		// 设置更新时间
		llmModel.setUpdateTime(new Date());
		
		boolean success = llmModel.update();
		if (success) {
			// 添加日志
			// addUpdateSystemLog(llmModel.getId(), JBoltUserKit.getUserId(), llmModel.getModelName());
		}
		return ret(success);
	}

	/**
	 * 删除数据后执行的回调
	 * @param llmModel 要删除的model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	protected String afterDelete(LlmModel llmModel, Kv kv) {
		// addDeleteSystemLog(llmModel.getId(), JBoltUserKit.getUserId(), llmModel.getModelName());
		return null;
	}

	/**
	 * 检测是否可以删除
	 * @param llmModel model
	 * @param kv 携带额外参数一般用不上
	 * @return
	 */
	@Override
	public String checkInUse(LlmModel llmModel, Kv kv) {
		// 这里用来覆盖 检测是否被其它表引用
		return null;
	}

	/**
	 * toggle操作执行后的回调处理
	 */
	@Override
	protected String afterToggleBoolean(LlmModel llmModel, String column, Kv kv) {
		// addUpdateSystemLog(llmModel.getId(), JBoltUserKit.getUserId(), llmModel.getModelName(), "的字段[" + column + "]值:" + llmModel.get(column));
		return null;
	}

	/**
	 * 获取指定提供商的模型列表
	 * @param providerId 提供商ID
	 * @return
	 */
	public List<LlmModel> getModelsByProvider(Long providerId) {
		if (providerId == null) {
			return new ArrayList<>();
		}
		return find("SELECT * FROM llm_model WHERE provider_id = ? AND status = 1 ORDER BY id", providerId);
	}

	/**
	 * 获取启用的模型选项
	 * @return
	 */
	public List<Kv> getEnabledModelOptions() {
		List<LlmModel> models = find("SELECT m.*, p.name as provider_name FROM llm_model m " +
			"LEFT JOIN llm_provider p ON m.provider_id = p.id " +
			"WHERE m.status = 1 AND p.status = 1 ORDER BY p.priority, m.id");
		
		List<Kv> options = new ArrayList<>();
		for (LlmModel model : models) {
			options.add(Kv.by("value", model.getId())
				.set("label", model.getStr("provider_name") + " - " + model.getModelName())
				.set("modelIdentifier", model.getModelIdentifier())
				.set("capabilities", model.getCapabilities()));
		}
		return options;
	}

	/**
	 * 测试模型
	 * @param modelId 模型ID
	 * @param testPrompt 测试提示词
	 * @return
	 */
	public Ret testModel(Long modelId, String testPrompt) {
		try {
			LlmModel model = findById(modelId);
			if (model == null) {
				return fail("模型不存在");
			}

			LlmProvider provider = new LlmProvider().dao().findById(model.getProviderId());
			if (provider == null) {
				return fail("提供商不存在");
			}

			// 使用LlmService测试模型
			String response = LlmService.me().callLlm(provider, model.getModelIdentifier(), testPrompt);

			if (response != null && !response.contains("error")) {
				return success("模型测试成功", response);
			} else {
				return fail("模型测试失败：" + response);
			}

		} catch (Exception e) {
			LogKit.error("测试模型失败", e);
			return fail("测试失败：" + e.getMessage());
		}
	}

	/**
	 * 批量导入模型
	 * @param providerId 提供商ID
	 * @return
	 */
	public Ret batchImportModels(Long providerId) {
		try {
			LlmProvider provider = new LlmProvider().dao().findById(providerId);
			if (provider == null) {
				return fail("提供商不存在");
			}

			List<LlmModel> models = createDefaultModelsForProvider(provider);
			int successCount = 0;
			
			for (LlmModel model : models) {
				// 检查是否已存在
				LlmModel existing = dao.findFirst("SELECT * FROM llm_model WHERE provider_id = ? AND model_identifier = ?", 
					providerId, model.getModelIdentifier());
				
				if (existing == null) {
					if (model.save()) {
						successCount++;
					}
				}
			}

			return success("成功导入 " + successCount + " 个模型");

		} catch (Exception e) {
			LogKit.error("批量导入模型失败", e);
			return fail("导入失败：" + e.getMessage());
		}
	}

	/**
	 * 为提供商创建默认模型
	 * @param provider 提供商
	 * @return
	 */
	private List<LlmModel> createDefaultModelsForProvider(LlmProvider provider) {
		List<LlmModel> models = new ArrayList<>();
		Date now = new Date();
		String providerName = provider.getName().toLowerCase();

		switch (providerName) {
			case "openai":
				models.add(createModel(provider.getId(), "GPT-3.5 Turbo", "gpt-3.5-turbo", 4096, 
					new BigDecimal("0.0015"), new BigDecimal("0.002"), 16385, "text,image", 
					"OpenAI GPT-3.5 Turbo，性价比高的对话模型", now));
				models.add(createModel(provider.getId(), "GPT-4", "gpt-4", 8192, 
					new BigDecimal("0.03"), new BigDecimal("0.06"), 8192, "text,image", 
					"OpenAI GPT-4，最强大的多模态模型", now));
				break;
			case "claude":
				models.add(createModel(provider.getId(), "Claude 3 Haiku", "claude-3-haiku-20240307", 4096, 
					new BigDecimal("0.00025"), new BigDecimal("0.00125"), 200000, "text,image", 
					"Claude 3 Haiku，快速轻量级模型", now));
				models.add(createModel(provider.getId(), "Claude 3 Sonnet", "claude-3-sonnet-20240229", 4096, 
					new BigDecimal("0.003"), new BigDecimal("0.015"), 200000, "text,image", 
					"Claude 3 Sonnet，平衡性能和速度", now));
				break;
			case "gemini":
				models.add(createModel(provider.getId(), "Gemini Pro", "gemini-2.5-pro", 4096,
					new BigDecimal("0.0005"), new BigDecimal("0.0015"), 32768, "text,image", 
					"Google Gemini Pro，多模态处理能力强", now));
				break;
			// 其他提供商的默认模型...
		}

		return models;
	}

	/**
	 * 创建模型对象
	 */
	private LlmModel createModel(Long providerId, String modelName, String modelIdentifier, 
			Integer maxTokens, BigDecimal inputPrice, BigDecimal outputPrice, 
			Integer contextLength, String capabilities, String description, Date now) {
		return new LlmModel()
			.setProviderId(providerId)
			.setModelName(modelName)
			.setModelIdentifier(modelIdentifier)
			.setMaxTokens(maxTokens)
			.setInputPrice(inputPrice)
			.setOutputPrice(outputPrice)
			.setContextLength(contextLength)
			.setCapabilities(capabilities)
			.setDescription(description)
			.setStatus(true)
			.setCreateTime(now)
			.setUpdateTime(now);
	}

	/**
	 * 获取模型能力统计
	 * @return
	 */
	public Kv getCapabilityStats() {
		List<Kv> stats = new ArrayList<>();
		
		// 统计各种能力的模型数量
		String sql = "SELECT capabilities, COUNT(*) as count FROM llm_model WHERE status = 1 GROUP BY capabilities";
		List<LlmModel> results = dao.find(sql);
		
		int textCount = 0, imageCount = 0, videoCount = 0, audioCount = 0;
		
		for (LlmModel result : results) {
			String capabilities = result.getStr("capabilities");
			int count = result.getInt("count");
			
			if (capabilities != null) {
				if (capabilities.contains("text")) textCount += count;
				if (capabilities.contains("image")) imageCount += count;
				if (capabilities.contains("video")) videoCount += count;
				if (capabilities.contains("audio")) audioCount += count;
			}
		}
		
		return Kv.by("text", textCount)
			.set("image", imageCount)
			.set("video", videoCount)
			.set("audio", audioCount)
			.set("total", dao.findFirst("SELECT COUNT(*) as count FROM llm_model WHERE status = 1").getInt("count"));
	}
}