package cn.jbolt.common.util;

import cn.jbolt.common.model.EmailMessages;
import com.google.common.collect.Lists;
import jakarta.mail.internet.MimeUtility;
import jakarta.mail.internet.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.jbolt.mail.gpt.parser.EmailParsingService.createAttachmentPath;
import static cn.jbolt.mail.gpt.parser.EmailParsingService.createScreenshotPath;

public class FileKit {

    public static List<String> extractImagePathsWithJsoup(String htmlContent) {
        List<String> imagePaths = new ArrayList<>();
        if (StringUtils.isEmpty(htmlContent)) {
            return imagePaths;
        }

        Document doc = Jsoup.parse(htmlContent);

        // 处理系统内部图片链接格式
        Elements internalImgElements = doc.select("img[src*=/common/file?filePath=]");
        for (Element img : internalImgElements) {
            String src = img.attr("src");
            // 提取filePath参数
            if (src.contains("?filePath=")) {
                String filePath = src.substring(src.indexOf("?filePath=") + 10);
                // 处理可能的URL参数
                if (filePath.contains("&")) {
                    filePath = filePath.substring(0, filePath.indexOf("&"));
                }
                // 解码URL编码的路径
                try {
                    filePath = java.net.URLDecoder.decode(filePath, "UTF-8");
                    imagePaths.add(filePath);
                } catch (UnsupportedEncodingException e) {
                    // 忽略解码错误
                }
            }
        }

        // 处理直接的图片链接
        Elements directImgElements = doc.select("img[src]:not([src*=/common/file?filePath=])");
        for (Element img : directImgElements) {
            String src = img.attr("src");
            // 过滤掉数据URL和空URL
            if (StringUtils.isNotEmpty(src) && !src.startsWith("data:") && !src.equals("#") && !src.equals("about:blank")) {
                // 检查是否是图片格式
                String lowerSrc = src.toLowerCase();
                if (lowerSrc.endsWith(".jpg") || lowerSrc.endsWith(".jpeg") ||
                        lowerSrc.endsWith(".png") || lowerSrc.endsWith(".gif") ||
                        lowerSrc.endsWith(".bmp") || lowerSrc.endsWith(".webp") ||
                        lowerSrc.endsWith(".tiff") || lowerSrc.endsWith(".tif")) {

                    // 处理相对路径
                    if (src.startsWith("/")) {
                        // 相对于根目录的路径，这里可能需要根据实际情况调整
                        src = "." + src;
                    }

                    imagePaths.add(src);
                }
            }
        }

        return imagePaths;
    }

    public static List<String> extractPdfPathsWithJsoup(String htmlContent) {
        List<String> imagePaths = new ArrayList<>();

        Document doc = Jsoup.parse(htmlContent);
        Elements imgElements = doc.select("a[href*=/common/file?filePath=]");

        for (Element img : imgElements) {
            String src = img.attr("src");
            // 提取filePath参数
            if (src.contains("?filePath=")) {
                String filePath = src.substring(src.indexOf("?filePath=") + 10);
                // 处理可能的URL参数
                if (filePath.contains("&")) {
                    filePath = filePath.substring(0, filePath.indexOf("&"));
                }
                // 解码URL编码的路径
                filePath = java.net.URLDecoder.decode(filePath, StandardCharsets.UTF_8);
                imagePaths.add(filePath);
            }
        }

        return imagePaths;
    }

    public static String sanitizeText(String text) {
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        String safeText = text.replaceAll("[\\\\/:*?\"<>|\t\n\r\f\0\u0000-\u001F\u007F\u0080-\u009F\u00A0=]+", "_");

        // 去除前后空格和点
        safeText = safeText.trim().replaceAll("^\\.", "_");

        // 限制文件名长度为30个字符
        if (safeText.length() > 30) {
            safeText = safeText.substring(0, 30);
        }
        return safeText;
    }
    /*
      处理文件名，确保符合各操作系统的文件系统要求
      将所有 Linux、Windows、macOS、Unix 不允许的字符替换为下划线
      如果文件名超过50个字符，只取前50个字符加后缀

      @param fileName 原始文件名
     * @return 处理后的安全文件名
     */

    /**
     * 清理文件名，移除不合法字符，并根据文件内容检测文件类型
     * 改进版本：分离文件名处理和文件类型检测，避免影响原始输入流
     *
     * @param fileName 原始文件名
     * @return 清理后的安全文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "unnamed_attachment" + UUID.randomUUID();
        }

        // 新增：先解码混合编码的文件名
        try {
            fileName = MimeFileNameDecoder.decodeMixedEncodedFileName(fileName);
        } catch (Exception e) {
            // 解码失败时使用原始文件名
        }

        // 移除编码信息部分（如_utf-8_Q_）
        fileName = fileName.replaceAll("_utf-8_Q_[A-Za-z0-9_]+", "");

        // 提取文件名和扩展名
        String extension = "";
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            // 提取原始扩展名
            String rawExtension = fileName.substring(dotIndex);

            // 正则表达式提取有效的扩展名（如.doc）
            // 匹配点后面的字母、数字和一些常见的扩展名字符
            java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("(\\.[a-zA-Z0-9]{1,10})").matcher(rawExtension);
            if (matcher.find()) {
                extension = matcher.group(1);
            }
            fileName = fileName.substring(0, dotIndex);
        }
        if (StringUtils.isEmpty(extension)) {
            extension = ".unknown";
        }

        // 替换所有不合法的字符
        // 包括: Windows(\\/:|*?"<>|), Linux/Unix(/), macOS(:)等系统不允许的字符
        // 以及控制字符和特殊符号
        String safeFileName = fileName.replaceAll("[\\\\/:*?\"<>|\t\n\r\f\0\u0000-\u001F\u007F\u0080-\u009F\u00A0=]+", "_");

        // 去除前后空格和点
        safeFileName = safeFileName.trim().replaceAll("^\\.", "_");

        // 限制文件名长度为50个字符
        if (safeFileName.length() > 50) {
            safeFileName = safeFileName.substring(0, 50);
        }

        // 如果处理后的文件名为空，使用默认名称
        if (safeFileName.isEmpty()) {
            safeFileName = "unnamed_attachment";
        }

        // 返回完整的文件名
        return safeFileName + extension;
    }

    /**
     * 递归获取目录下所有文件
     *
     * @param dir 目录
     * @return 所有文件列表
     */
    public static List<File> listFilesRecursively(File dir) {
        List<File> fileList = new ArrayList<>();
        if (!dir.exists()) {
            return fileList;
        }
        if (dir.isFile()) {
            return Lists.newArrayList(dir);
        }
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    fileList.add(file);
                } else if (file.isDirectory()) {
                    fileList.addAll(listFilesRecursively(file));
                }
            }
        }
        return fileList;
    }

    public static List<File> listFilesRecursively(String dir) {
        System.out.println("获取邮件附件路径：" + dir);
        return listFilesRecursively(new File(dir));
    }

    public static List<File> listEmailAttachments(EmailMessages email) {
        String attachmentPath = createAttachmentPath(email, null);
        System.out.println("listEmailAttachments - 邮件ID: " + email.getId() + ", 扫描路径: " + attachmentPath);

        // 检查目录是否存在
        File dir = new File(attachmentPath);
        if (!dir.exists()) {
            System.out.println("listEmailAttachments - 目录不存在: " + attachmentPath);
            return new ArrayList<>();
        }

        List<File> files = listFilesRecursively(attachmentPath);
        System.out.println("listEmailAttachments - 找到文件数量: " + files.size());
        for (File file : files) {
            System.out.println("  - " + file.getAbsolutePath());
        }

        return files;
    }

    public static List<File> listEmailScreenshots(EmailMessages email) {
        String attachmentPath = createScreenshotPath(email);
        return listFilesRecursively(attachmentPath);
    }

    /**
     * 通过文件内容检测文件类型并返回适当的扩展名
     * 改进版本：确保不影响原始输入流，并正确处理流的关闭
     *
     * @param header 文件输入流
     * @return 检测到的文件扩展名（包括点，如".jpg"）
     */
    public static String detectExtension(byte[] header) {
        if (header == null) {
            return "";
        }

        // 检查文件头部字节以确定文件类型
        String extension = "";

        // 1. 图片文件
        if (isJPEG(header)) extension = ".jpg";
        else if (isPNG(header)) extension = ".png";
        else if (isGIF(header)) extension = ".gif";
        else if (isBMP(header)) extension = ".bmp";
        else if (isWebP(header)) extension = ".webp";

            // 2. 视频文件
        else if (isMP4(header)) extension = ".mp4";
        else if (isAVI(header)) extension = ".avi";
        else if (isMKV(header)) extension = ".mkv";

            // 3. 音频文件
        else if (isMP3(header)) extension = ".mp3";
        else if (isWAV(header)) extension = ".wav";

            // 4. PDF文件
        else if (isPDF(header)) extension = ".pdf";

            // 5. Office文件
        else if (isDOCX(header)) extension = ".docx";
        else if (isXLSX(header)) extension = ".xlsx";
        else if (isPPTX(header)) extension = ".pptx";
        else if (isDOC(header)) extension = ".doc";
        else if (isXLS(header)) extension = ".xls";
        else if (isPPT(header)) extension = ".ppt";

            // 6. 文本文件
        else if (isTXT(header)) extension = ".txt";
        else if (isHTML(header)) extension = ".html";
        else if (isXML(header)) extension = ".xml";
        else if (isJSON(header)) extension = ".json";
        else if (isCSV(header)) extension = ".csv";

            // 7. 压缩文件
        else if (isZIP(header)) extension = ".zip";
        else if (isRAR(header)) extension = ".rar";
        else if (is7Z(header)) extension = ".7z";

        return extension;

    }


// 以下是各种文件类型的检测方法

    public static boolean isJPEG(byte[] header) {
        return header.length >= 3 &&
                header[0] == (byte) 0xFF &&
                header[1] == (byte) 0xD8 &&
                header[2] == (byte) 0xFF;
    }

    public static boolean isPNG(byte[] header) {
        return header.length >= 8 &&
                header[0] == (byte) 0x89 &&
                header[1] == (byte) 0x50 &&
                header[2] == (byte) 0x4E &&
                header[3] == (byte) 0x47 &&
                header[4] == (byte) 0x0D &&
                header[5] == (byte) 0x0A &&
                header[6] == (byte) 0x1A &&
                header[7] == (byte) 0x0A;
    }

    public static boolean isGIF(byte[] header) {
        return header.length >= 6 &&
                header[0] == (byte) 0x47 &&
                header[1] == (byte) 0x49 &&
                header[2] == (byte) 0x46 &&
                header[3] == (byte) 0x38 &&
                (header[4] == (byte) 0x37 || header[4] == (byte) 0x39) &&
                header[5] == (byte) 0x61;
    }

    public static boolean isBMP(byte[] header) {
        return header.length >= 2 &&
                header[0] == (byte) 0x42 &&
                header[1] == (byte) 0x4D;
    }

    public static boolean isWebP(byte[] header) {
        return header.length >= 12 &&
                header[0] == (byte) 0x52 &&
                header[1] == (byte) 0x49 &&
                header[2] == (byte) 0x46 &&
                header[3] == (byte) 0x46 &&
                header[8] == (byte) 0x57 &&
                header[9] == (byte) 0x45 &&
                header[10] == (byte) 0x42 &&
                header[11] == (byte) 0x50;
    }

    public static boolean isPDF(byte[] header) {
        return header.length >= 5 &&
                header[0] == (byte) 0x25 &&
                header[1] == (byte) 0x50 &&
                header[2] == (byte) 0x44 &&
                header[3] == (byte) 0x46 &&
                header[4] == (byte) 0x2D;
    }

    public static boolean isDOCX(byte[] header) {
        return isOfficeOpenXML(header) && containsBytes(header, "word/".getBytes());
    }

    public static boolean isXLSX(byte[] header) {
        return isOfficeOpenXML(header) && containsBytes(header, "xl/".getBytes());
    }

    public static boolean isPPTX(byte[] header) {
        return isOfficeOpenXML(header) && containsBytes(header, "ppt/".getBytes());
    }

    public static boolean isOfficeOpenXML(byte[] header) {
        // Office Open XML files are ZIP files with specific content
        return isZIP(header);
    }

    public static boolean isDOC(byte[] header) {
        return header.length >= 8 &&
                (header[0] == (byte) 0xD0 && header[1] == (byte) 0xCF &&
                        header[2] == (byte) 0x11 && header[3] == (byte) 0xE0 &&
                        header[4] == (byte) 0xA1 && header[5] == (byte) 0xB1 &&
                        header[6] == (byte) 0x1A && header[7] == (byte) 0xE1);
    }

    public static boolean isXLS(byte[] header) {
        // XLS uses the same signature as DOC (Compound File Binary Format)
        return isDOC(header);
    }

    public static boolean isPPT(byte[] header) {
        // PPT uses the same signature as DOC (Compound File Binary Format)
        return isDOC(header);
    }

    public static boolean isMP4(byte[] header) {
        // Check for ISO Base Media File Format (ISOBMFF) which MP4 is based on
        if (header.length < 12) return false;

        // Skip first 4 bytes (file size) and check for 'ftyp'
        return header[4] == (byte) 0x66 &&
                header[5] == (byte) 0x74 &&
                header[6] == (byte) 0x79 &&
                header[7] == (byte) 0x70;
    }

    public static boolean isAVI(byte[] header) {
        return header.length >= 12 &&
                header[0] == (byte) 0x52 &&
                header[1] == (byte) 0x49 &&
                header[2] == (byte) 0x46 &&
                header[3] == (byte) 0x46 &&
                header[8] == (byte) 0x41 &&
                header[9] == (byte) 0x56 &&
                header[10] == (byte) 0x49 &&
                header[11] == (byte) 0x20;
    }

    public static boolean isMKV(byte[] header) {
        // Check for EBML header (Matroska files start with 0x1A 0x45 0xDF 0xA3)
        return header.length >= 4 &&
                header[0] == (byte) 0x1A &&
                header[1] == (byte) 0x45 &&
                header[2] == (byte) 0xDF &&
                header[3] == (byte) 0xA3;
    }

    public static boolean isMP3(byte[] header) {
        // Check for ID3v2 tag or MP3 frame header
        if (header.length < 3) return false;

        // ID3v2 tag
        if (header[0] == (byte) 0x49 && header[1] == (byte) 0x44 && header[2] == (byte) 0x33) {
            return true;
        }

        // MP3 frame header starts with 0xFF followed by 0xE0, 0xF0, etc.
        return header[0] == (byte) 0xFF && (header[1] & 0xE0) == 0xE0;
    }

    public static boolean isWAV(byte[] header) {
        return header.length >= 12 &&
                header[0] == (byte) 0x52 &&
                header[1] == (byte) 0x49 &&
                header[2] == (byte) 0x46 &&
                header[3] == (byte) 0x46 &&
                header[8] == (byte) 0x57 &&
                header[9] == (byte) 0x41 &&
                header[10] == (byte) 0x56 &&
                header[11] == (byte) 0x45;
    }

    public static boolean isZIP(byte[] header) {
        return header.length >= 4 &&
                header[0] == (byte) 0x50 &&
                header[1] == (byte) 0x4B &&
                (header[2] == (byte) 0x03 || header[2] == (byte) 0x05 || header[2] == (byte) 0x07) &&
                (header[3] == (byte) 0x04 || header[3] == (byte) 0x06 || header[3] == (byte) 0x08);
    }

    public static boolean isRAR(byte[] header) {
        return header.length >= 7 &&
                header[0] == (byte) 0x52 &&
                header[1] == (byte) 0x61 &&
                header[2] == (byte) 0x72 &&
                header[3] == (byte) 0x21 &&
                header[4] == (byte) 0x1A &&
                header[5] == (byte) 0x07 &&
                (header[6] == (byte) 0x00 || header[6] == (byte) 0x01);
    }

    public static boolean is7Z(byte[] header) {
        return header.length >= 6 &&
                header[0] == (byte) 0x37 &&
                header[1] == (byte) 0x7A &&
                header[2] == (byte) 0xBC &&
                header[3] == (byte) 0xAF &&
                header[4] == (byte) 0x27 &&
                header[5] == (byte) 0x1C;
    }

    public static boolean isTXT(byte[] header) {
        // 检查是否为文本文件（只包含ASCII字符或常见Unicode编码）
        for (int i = 0; i < Math.min(header.length, 1000); i++) {
            // 检查非打印字符（除了常见的控制字符如换行、制表符等）
            if ((header[i] < 0x20 && header[i] != 0x09 && header[i] != 0x0A && header[i] != 0x0D) ||
                    (header[i] == 0x7F)) {
                // 可能是二进制文件
                return false;
            }
        }
        return true;
    }

    public static boolean isHTML(byte[] header) {
        String content = new String(header, 0, Math.min(header.length, 1000));
        return content.toLowerCase().contains("<html") ||
                content.toLowerCase().contains("<!doctype html") ||
                content.toLowerCase().contains("<body");
    }

    public static boolean isXML(byte[] header) {
        String content = new String(header, 0, Math.min(header.length, 1000));
        return content.trim().startsWith("<?xml") ||
                content.trim().startsWith("<");
    }

    public static boolean isJSON(byte[] header) {
        String content = new String(header, 0, Math.min(header.length, 1000)).trim();
        return (content.startsWith("{") && content.contains(":")) ||
                (content.startsWith("[") && content.contains(","));
    }

    public static boolean isCSV(byte[] header) {
        String content = new String(header, 0, Math.min(header.length, 1000));
        // 检查是否有逗号分隔的值，且至少有一行
        String[] lines = content.split("\n", 2);
        if (lines.length > 0) {
            String firstLine = lines[0];
            return firstLine.contains(",") &&
                    firstLine.split(",").length > 1;
        }
        return false;
    }

    public static boolean containsBytes(byte[] source, byte[] target) {
        outer:
        for (int i = 0; i <= source.length - target.length; i++) {
            for (int j = 0; j < target.length; j++) {
                if (source[i + j] != target[j]) {
                    continue outer;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 从HTML内容中提取Excel文件路径
     *
     * @param htmlContent HTML内容
     * @return Excel文件路径列表
     */
    public static List<String> extractExcelPathsWithJsoup(String htmlContent) {
        List<String> excelPaths = new ArrayList<>();

        Document doc = Jsoup.parse(htmlContent);
        Elements linkElements = doc.select("a[href*=/common/file?filePath=]");

        for (Element link : linkElements) {
            String href = link.attr("href");
            // 提取filePath参数
            if (href.contains("?filePath=")) {
                String filePath = href.substring(href.indexOf("?filePath=") + 10);
                // 处理可能的URL参数
                if (filePath.contains("&")) {
                    filePath = filePath.substring(0, filePath.indexOf("&"));
                }
                // 解码URL编码的路径
                try {
                    filePath = java.net.URLDecoder.decode(filePath, "UTF-8");
                    // 检查是否为Excel文件
                    if (filePath.toLowerCase().endsWith(".xlsx") || filePath.toLowerCase().endsWith(".xls")) {
                        excelPaths.add(filePath);
                    }
                } catch (UnsupportedEncodingException e) {
                    // 忽略解码错误
                }
            }
        }

        return excelPaths;
    }

    /**
     * 从HTML内容中提取Word文件路径
     *
     * @param htmlContent HTML内容
     * @return Word文件路径列表
     */
    public static List<String> extractWordPathsWithJsoup(String htmlContent) {
        List<String> wordPaths = new ArrayList<>();

        Document doc = Jsoup.parse(htmlContent);
        Elements linkElements = doc.select("a[href*=/common/file?filePath=]");

        for (Element link : linkElements) {
            String href = link.attr("href");
            // 提取filePath参数
            if (href.contains("?filePath=")) {
                String filePath = href.substring(href.indexOf("?filePath=") + 10);
                // 处理可能的URL参数
                if (filePath.contains("&")) {
                    filePath = filePath.substring(0, filePath.indexOf("&"));
                }
                // 解码URL编码的路径
                try {
                    filePath = java.net.URLDecoder.decode(filePath, "UTF-8");
                    // 检查是否为Word文件
                    if (filePath.toLowerCase().endsWith(".docx") || filePath.toLowerCase().endsWith(".doc")) {
                        wordPaths.add(filePath);
                    }
                } catch (UnsupportedEncodingException e) {
                    // 忽略解码错误
                }
            }
        }

        return wordPaths;
    }

    /**
     * 从HTML内容中提取TXT文件路径
     *
     * @param htmlContent HTML内容
     * @return TXT文件路径列表
     */
    public static List<String> extractTxtPathsWithJsoup(String htmlContent) {
        List<String> txtPaths = new ArrayList<>();

        Document doc = Jsoup.parse(htmlContent);
        Elements linkElements = doc.select("a[href*=/common/file?filePath=]");

        for (Element link : linkElements) {
            String href = link.attr("href");
            // 提取filePath参数
            if (href.contains("?filePath=")) {
                String filePath = href.substring(href.indexOf("?filePath=") + 10);
                // 处理可能的URL参数
                if (filePath.contains("&")) {
                    filePath = filePath.substring(0, filePath.indexOf("&"));
                }
                // 解码URL编码的路径
                try {
                    filePath = java.net.URLDecoder.decode(filePath, "UTF-8");
                    // 检查是否为TXT文件
                    if (filePath.toLowerCase().endsWith(".txt")) {
                        txtPaths.add(filePath);
                    }
                } catch (UnsupportedEncodingException e) {
                    // 忽略解码错误
                }
            }
        }

        return txtPaths;
    }

    /**
     * 解码混合编码的文件名
     * 处理包含MIME编码和普通文本混合的文件名
     *
     * @param encodedFileName 编码的文件名
     * @return 解码后的文件名
     */
    public static String decodeMixedEncodedFileName(String encodedFileName) {
        if (StringUtils.isEmpty(encodedFileName)) {
            return encodedFileName;
        }

        try {
            // 设置宽松的解码模式
            System.setProperty("mail.mime.decodetext.strict", "false");

            // 先修复分隔符问题：将 === 替换为 =
            String fixedName = encodedFileName.replaceAll("===", "=");

            // 修复可能的编码格式问题
            // 处理 =?utf-8?Q?...?= 格式
            Pattern pattern = Pattern.compile("=\\?([^?]+)\\?([QqBb])\\?([^?]*)\\?=");
            Matcher matcher = pattern.matcher(fixedName);

            StringBuilder result = new StringBuilder();
            while (matcher.find()) {
                String charset = matcher.group(1);
                String encoding = matcher.group(2).toUpperCase();
                String encodedText = matcher.group(3);

                String decodedPart = "";
                try {
                    if ("Q".equals(encoding)) {
                        // Quoted-Printable 解码
                        decodedPart = decodeQuotedPrintable(encodedText, charset);
                    } else if ("B".equals(encoding)) {
                        // Base64 解码
                        decodedPart = new String(java.util.Base64.getDecoder().decode(encodedText), charset);
                    }
                } catch (Exception e) {
                    // 如果解码失败，保留原始文本
                    decodedPart = matcher.group(0);
                }

                matcher.appendReplacement(result, Matcher.quoteReplacement(decodedPart));
            }
            matcher.appendTail(result);

            return result.toString();

        } catch (Exception e) {
            // 如果所有解码方法都失败，尝试使用MimeUtility
            try {
                String fixedName = encodedFileName.replaceAll("===", "=");
                return MimeUtility.decodeText(fixedName);
            } catch (Exception ex) {
                // 最后的备选方案：返回原始文件名
                return encodedFileName;
            }
        }
    }

    /**
     * 解码Quoted-Printable编码的文本
     *
     * @param encodedText 编码的文本
     * @param charset 字符集
     * @return 解码后的文本
     */
    private static String decodeQuotedPrintable(String encodedText, String charset) {
        try {
            // 替换 = 编码的字符
            StringBuilder decoded = new StringBuilder();
            for (int i = 0; i < encodedText.length(); i++) {
                char c = encodedText.charAt(i);
                if (c == '=' && i + 2 < encodedText.length()) {
                    // 获取十六进制值
                    String hex = encodedText.substring(i + 1, i + 3);
                    try {
                        int value = Integer.parseInt(hex, 16);
                        decoded.append((char) value);
                        i += 2; // 跳过已处理的字符
                    } catch (NumberFormatException e) {
                        decoded.append(c);
                    }
                } else if (c == '_') {
                    // 在Quoted-Printable中，下划线通常表示空格
                    decoded.append(' ');
                } else {
                    decoded.append(c);
                }
            }

            // 将字节转换为指定字符集的字符串
            byte[] bytes = decoded.toString().getBytes("ISO-8859-1");
            return new String(bytes, charset);

        } catch (Exception e) {
            return encodedText;
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException, ParseException {
        String encodedName = "AFD19_壹林账=?utf-8?Q?=E5=8D=95=E5=AE=A2=E6=88=B7=E7%9F=B3=E7=A7%8D?===?utf-8?Q?=5F=E5=90=AB=E5=8D=95=E4%BB=B7=5F?===?utf-8?Q?=5F20250619161909=2Exls?=";

        System.out.println("原始编码: " + encodedName);

        // 使用新的解码方法
        String decoded = MimeFileNameDecoder.decodeMixedEncodedFileName(encodedName);
        System.out.println("解码结果: " + decoded);

        // 测试sanitizeFileName方法
        String sanitized = sanitizeFileName(encodedName);
        System.out.println("清理后的文件名: " + sanitized);

        // 也可以尝试原来的方法作为对比
        try {
            String fixedName = encodedName.replaceAll("===", "=");
            System.setProperty("mail.mime.decodetext.strict", "false");
            String mimeDecoded = MimeUtility.decodeText(fixedName);
            System.out.println("MimeUtility解码: " + mimeDecoded);
        } catch (Exception e) {
            System.out.println("MimeUtility解码失败: " + e.getMessage());
        }
    }
}
