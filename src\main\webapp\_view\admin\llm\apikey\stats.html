#@jboltLayout()
#define main()
#set(pageId=RandomUtil.random(6))
<div class="jbolt_page" data-key="llm_apikey_stats">
<div class="jbolt_page_title">
<div class="row">
    <div class="col-sm-auto"><h1><i class="jbicon2 jbi-bar-chart"></i>API密钥使用统计</h1></div>
    <div class="col">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                <i class="fa fa-refresh"></i> 刷新数据
            </button>
            <button type="button" class="btn btn-outline-warning btn-sm" onclick="resetBlockedKeys()">
                <i class="fa fa-unlock"></i> 重置阻塞
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" onclick="reloadConfig()">
                <i class="fa fa-cog"></i> 重载配置
            </button>
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="cleanupLogs()">
                <i class="fa fa-trash"></i> 清理日志
            </button>
        </div>
    </div>
</div>
</div>

<div class="jbolt_page_content">
    <!-- 概览卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalProviders">-</h4>
                            <p class="mb-0">活跃提供商</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-server fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalKeys">-</h4>
                            <p class="mb-0">API密钥总数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalRequests">-</h4>
                            <p class="mb-0">总请求数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-exchange fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="blockedKeys">-</h4>
                            <p class="mb-0">被阻塞密钥</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fa fa-ban fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 选项卡 -->
    <ul class="nav nav-tabs" id="statsTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                <i class="fa fa-dashboard"></i> 提供商概览
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                <i class="fa fa-list"></i> 详细统计
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="realtime-tab" data-bs-toggle="tab" data-bs-target="#realtime" type="button" role="tab">
                <i class="fa fa-clock-o"></i> 实时状态
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
                <i class="fa fa-cog"></i> 密钥配置
            </button>
        </li>
    </ul>

    <div class="tab-content" id="statsTabContent">
        <!-- 提供商概览 -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">提供商使用概览</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="providerOverviewTable">
                            <thead>
                                <tr>
                                    <th>提供商</th>
                                    <th>密钥数量</th>
                                    <th>总请求数</th>
                                    <th>成功请求</th>
                                    <th>失败请求</th>
                                    <th>成功率</th>
                                    <th>被阻塞密钥</th>
                                    <th>最后请求时间</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="tab-pane fade" id="details" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">API密钥详细统计</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm" id="detailStatsTable">
                            <thead>
                                <tr>
                                    <th>提供商</th>
                                    <th>密钥哈希</th>
                                    <th>总请求数</th>
                                    <th>成功请求</th>
                                    <th>失败请求</th>
                                    <th>成功率</th>
                                    <th>当前分钟请求</th>
                                    <th>最后请求时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时状态 -->
        <div class="tab-pane fade" id="realtime" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">实时使用状态</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAutoRefresh()">
                        <i class="fa fa-play" id="autoRefreshIcon"></i> <span id="autoRefreshText">开启自动刷新</span>
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="realtimeStatsTable">
                            <thead>
                                <tr>
                                    <th>API密钥</th>
                                    <th>当前分钟请求数</th>
                                    <th>总请求数</th>
                                    <th>最后请求时间</th>
                                    <th>状态</th>
                                    <th>阻塞到期时间</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 密钥配置 -->
        <div class="tab-pane fade" id="config" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">API密钥配置信息</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="keyConfigTable">
                            <thead>
                                <tr>
                                    <th>提供商</th>
                                    <th>密钥序号</th>
                                    <th>API密钥</th>
                                    <th>频率限制</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

#define js()
<script>
let autoRefreshInterval = null;
let isAutoRefresh = false;

$(document).ready(function() {
    loadOverviewData();
    loadProviderOverview();
    loadDetailStats();
    loadRealtimeStats();
    loadKeyConfigs();
});

// 加载概览数据
function loadOverviewData() {
    $.get('admin/llm/apikey/stats/providerOverview', function(data) {
        if (data.state === 'ok') {
            let totalProviders = data.data.length;
            let totalKeys = 0;
            let totalRequests = 0;
            let blockedKeys = 0;
            
            data.data.forEach(function(item) {
                totalKeys += item.key_count || 0;
                totalRequests += item.total_requests || 0;
                blockedKeys += item.blocked_keys || 0;
            });
            
            $('#totalProviders').text(totalProviders);
            $('#totalKeys').text(totalKeys);
            $('#totalRequests').text(totalRequests.toLocaleString());
            $('#blockedKeys').text(blockedKeys);
        }
    });
}

// 加载提供商概览
function loadProviderOverview() {
    $.get('admin/llm/apikey/stats/providerOverview', function(data) {
        if (data.state === 'ok') {
            let tbody = $('#providerOverviewTable tbody');
            tbody.empty();
            
            data.data.forEach(function(item) {
                let row = `
                    <tr>
                        <td><strong>${item.provider_name || '-'}</strong></td>
                        <td><span class="badge bg-primary">${item.key_count || 0}</span></td>
                        <td>${(item.total_requests || 0).toLocaleString()}</td>
                        <td class="text-success">${(item.success_requests || 0).toLocaleString()}</td>
                        <td class="text-danger">${(item.failed_requests || 0).toLocaleString()}</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getSuccessRateColor(item.success_rate)}" 
                                     style="width: ${item.success_rate || 0}%">
                                    ${item.success_rate || 0}%
                                </div>
                            </div>
                        </td>
                        <td>
                            ${item.blocked_keys > 0 ? 
                              `<span class="badge bg-warning">${item.blocked_keys}</span>` : 
                              '<span class="badge bg-success">0</span>'}
                        </td>
                        <td>${formatDateTime(item.last_request_time)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

// 加载详细统计
function loadDetailStats() {
    $.get('admin/llm/apikey/stats/datas', function(data) {
        if (data.state === 'ok') {
            let tbody = $('#detailStatsTable tbody');
            tbody.empty();
            
            data.data.forEach(function(item) {
                let status = getKeyStatus(item);
                let row = `
                    <tr>
                        <td>${item.providerName || '-'}</td>
                        <td><code>${item.apiKeyHash || '-'}</code></td>
                        <td>${(item.totalRequests || 0).toLocaleString()}</td>
                        <td class="text-success">${(item.successRequests || 0).toLocaleString()}</td>
                        <td class="text-danger">${(item.failedRequests || 0).toLocaleString()}</td>
                        <td>${item.successRate || 0}%</td>
                        <td>
                            <span class="badge ${item.currentMinuteRequests > 10 ? 'bg-warning' : 'bg-info'}">
                                ${item.currentMinuteRequests || 0}
                            </span>
                        </td>
                        <td>${formatDateTime(item.lastRequestTime)}</td>
                        <td>${status}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

// 加载实时状态
function loadRealtimeStats() {
    $.get('admin/llm/apikey/stats/realTimeStats', function(data) {
        if (data.state === 'ok') {
            let tbody = $('#realtimeStatsTable tbody');
            tbody.empty();
            
            data.data.forEach(function(item) {
                let statusBadge = item.isBlocked ? 
                    '<span class="badge bg-danger">阻塞中</span>' : 
                    '<span class="badge bg-success">正常</span>';
                
                let row = `
                    <tr>
                        <td><code>${item.apiKey}</code></td>
                        <td>
                            <span class="badge ${item.currentMinuteRequests > 10 ? 'bg-warning' : 'bg-info'}">
                                ${item.currentMinuteRequests}
                            </span>
                        </td>
                        <td>${item.totalRequests.toLocaleString()}</td>
                        <td>${formatDateTime(item.lastRequestTime)}</td>
                        <td>${statusBadge}</td>
                        <td>${formatDateTime(item.blockUntil)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

// 加载密钥配置
function loadKeyConfigs() {
    $.get('admin/llm/apikey/stats/keyConfigs', function(data) {
        if (data.state === 'ok') {
            let tbody = $('#keyConfigTable tbody');
            tbody.empty();
            
            data.data.forEach(function(item) {
                let statusBadge = item.enabled ? 
                    '<span class="badge bg-success">启用</span>' : 
                    '<span class="badge bg-secondary">禁用</span>';
                
                let row = `
                    <tr>
                        <td><strong>${item.providerName}</strong></td>
                        <td><span class="badge bg-primary">#${item.keyIndex}</span></td>
                        <td><code>${item.apiKey}</code></td>
                        <td>${item.rateLimitPerMinute}/分钟</td>
                        <td>${item.priority || '-'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

// 刷新所有数据
function refreshStats() {
    loadOverviewData();
    loadProviderOverview();
    loadDetailStats();
    loadRealtimeStats();
    loadKeyConfigs();
    showToast('数据已刷新', 'success');
}

// 重置被阻塞的密钥
function resetBlockedKeys() {
    if (confirm('确定要重置所有被阻塞的API密钥吗？')) {
        $.post('admin/llm/apikey/stats/resetBlockedKeys', function(data) {
            if (data.state === 'ok') {
                showToast('成功重置被阻塞的API密钥', 'success');
                refreshStats();
            } else {
                showToast('重置失败: ' + data.msg, 'error');
            }
        });
    }
}

// 重载配置
function reloadConfig() {
    $.post('admin/llm/apikey/stats/reloadConfig', function(data) {
        if (data.state === 'ok') {
            showToast('成功重新加载配置', 'success');
            refreshStats();
        } else {
            showToast('重载配置失败: ' + data.msg, 'error');
        }
    });
}

// 清理日志
function cleanupLogs() {
    let days = prompt('请输入要保留的天数（默认30天）:', '30');
    if (days !== null) {
        $.post('admin/llm/apikey/stats/cleanupLogs', {days: days}, function(data) {
            if (data.state === 'ok') {
                showToast('日志清理成功', 'success');
            } else {
                showToast('清理失败: ' + data.msg, 'error');
            }
        });
    }
}

// 切换自动刷新
function toggleAutoRefresh() {
    if (isAutoRefresh) {
        clearInterval(autoRefreshInterval);
        isAutoRefresh = false;
        $('#autoRefreshIcon').removeClass('fa-pause').addClass('fa-play');
        $('#autoRefreshText').text('开启自动刷新');
    } else {
        autoRefreshInterval = setInterval(function() {
            loadRealtimeStats();
        }, 5000); // 每5秒刷新一次实时数据
        isAutoRefresh = true;
        $('#autoRefreshIcon').removeClass('fa-play').addClass('fa-pause');
        $('#autoRefreshText').text('关闭自动刷新');
    }
}

// 辅助函数
function getSuccessRateColor(rate) {
    if (rate >= 95) return 'bg-success';
    if (rate >= 80) return 'bg-warning';
    return 'bg-danger';
}

function getKeyStatus(item) {
    if (item.isBlocked || item.memoryIsBlocked) {
        return '<span class="badge bg-danger">阻塞中</span>';
    }
    if (item.currentMinuteRequests > 10) {
        return '<span class="badge bg-warning">繁忙</span>';
    }
    if (item.lastRequestTime && new Date() - new Date(item.lastRequestTime) < 300000) {
        return '<span class="badge bg-success">活跃</span>';
    }
    return '<span class="badge bg-secondary">空闲</span>';
}

function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
}

function showToast(message, type) {
    // 简单的提示实现，可以根据实际UI框架调整
    alert(message);
}
</script>
#end
#end
