package cn.jbolt.admin.email;

import cn.jbolt.common.model.EmailTranslationConfig;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件翻译配置管理控制器
 */
@CheckPermission("admin.email")
@UnCheckIfSystemAdmin
@Path(value = "/admin/email/translation/config", viewPath = "/_view/admin/email/translation")
public class EmailTranslationConfigController extends JBoltBaseController {

    /**
     * 配置管理页面
     */
    public void index() {
        render("config.html");
    }

    /**
     * 获取所有配置
     */
    public void datas() {
        try {
            List<Record> configs = Db.find("SELECT * FROM v_email_translation_config ORDER BY config_key");
            renderJsonData(configs);
        } catch (Exception e) {
            renderJsonFail("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用的LLM提供商列表
     */
    public void providers() {
        try {
            List<Record> providers = Db.find(
                "SELECT name, default_model, status, priority " +
                "FROM llm_provider " +
                "WHERE status = 1 " +
                "ORDER BY priority, name"
            );
            renderJsonData(providers);
        } catch (Exception e) {
            renderJsonFail("获取提供商列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定提供商的可用模型
     */
    public void models() {
        String providerName = getPara("provider");
        if (providerName == null || providerName.trim().isEmpty()) {
            renderJsonFail("提供商名称不能为空");
            return;
        }

        try {
            // 查询提供商信息
            LlmProvider provider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = ? AND status = 1", providerName);
            
            if (provider == null) {
                renderJsonFail("找不到指定的提供商");
                return;
            }

            // 查询该提供商的可用模型
            List<Record> models = Db.find(
                "SELECT model_identifier, model_name, status " +
                "FROM llm_model " +
                "WHERE provider_id = ? AND status = 1 " +
                "ORDER BY model_name", 
                provider.getId()
            );

            // 如果没有找到模型，返回默认模型
            if (models.isEmpty() && provider.getDefaultModel() != null) {
                Record defaultModel = new Record();
                defaultModel.set("model_identifier", provider.getDefaultModel());
                defaultModel.set("model_name", provider.getDefaultModel());
                defaultModel.set("status", 1);
                models.add(defaultModel);
            }

            renderJsonData(models);
        } catch (Exception e) {
            renderJsonFail("获取模型列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新配置
     */
    public void update() {
        String configKey = getPara("configKey");
        String configValue = getPara("configValue");
        String changeReason = getPara("changeReason", "管理员手动更新");

        if (configKey == null || configKey.trim().isEmpty()) {
            renderJsonFail("配置键不能为空");
            return;
        }

        try {
            boolean success = EmailTranslationConfig.setConfigValue(configKey, configValue, changeReason);
            if (success) {
                renderJsonSuccess("配置更新成功");
            } else {
                renderJsonFail("配置更新失败");
            }
        } catch (Exception e) {
            renderJsonFail("配置更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新配置
     */
    public void batchUpdate() {
        try {
            // 获取所有配置参数
            Map<String, String> configs = new HashMap<>();
            
            // 主要提供商配置
            String primaryProvider = getPara("primaryProvider");
            String primaryModel = getPara("primaryModel");
            String backupProvider = getPara("backupProvider");
            String backupModel = getPara("backupModel");
            
            // 重试配置
            String maxAttempts = getPara("maxAttempts");
            String delaySeconds = getPara("delaySeconds");
            
            // 功能开关
            String enableImageTranslation = getPara("enableImageTranslation");
            String enableBatchProcessing = getPara("enableBatchProcessing");
            
            // 其他配置
            String batchSize = getPara("batchSize");
            String timeoutSeconds = getPara("timeoutSeconds");

            String changeReason = getPara("changeReason", "管理员批量更新配置");

            // 验证必填项
            if (primaryProvider == null || primaryModel == null) {
                renderJsonFail("主要提供商和模型不能为空");
                return;
            }

            // 批量更新配置
            boolean allSuccess = true;
            
            if (primaryProvider != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.PRIMARY_PROVIDER, primaryProvider, changeReason);
            }
            if (primaryModel != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.PRIMARY_MODEL, primaryModel, changeReason);
            }
            if (backupProvider != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BACKUP_PROVIDER, backupProvider, changeReason);
            }
            if (backupModel != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BACKUP_MODEL, backupModel, changeReason);
            }
            if (maxAttempts != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.MAX_RETRY_ATTEMPTS, maxAttempts, changeReason);
            }
            if (delaySeconds != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.RETRY_DELAY_SECONDS, delaySeconds, changeReason);
            }
            if (enableImageTranslation != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.ENABLE_IMAGE_TRANSLATION, enableImageTranslation, changeReason);
            }
            if (enableBatchProcessing != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.ENABLE_BATCH_PROCESSING, enableBatchProcessing, changeReason);
            }
            if (batchSize != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BATCH_SIZE, batchSize, changeReason);
            }
            if (timeoutSeconds != null) {
                allSuccess &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.TIMEOUT_SECONDS, timeoutSeconds, changeReason);
            }

            if (allSuccess) {
                renderJsonSuccess("配置批量更新成功");
            } else {
                renderJsonFail("部分配置更新失败，请检查日志");
            }

        } catch (Exception e) {
            renderJsonFail("批量更新配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试翻译配置
     */
    public void testTranslation() {
        String testText = getPara("testText", "Hello, this is a test message for translation.");
        String providerName = getPara("provider");
        String modelName = getPara("model");

        if (providerName == null || modelName == null) {
            renderJsonFail("提供商和模型不能为空");
            return;
        }

        try {
            // 调用LlmService进行实际测试
            cn.jbolt.llm.service.LlmService llmService = cn.jbolt.llm.service.LlmService.me();
            long startTime = System.currentTimeMillis();

            // 获取翻译提示词
            cn.jbolt.common.model.AiPrompt aiPrompt = new cn.jbolt.common.model.AiPrompt().dao().findFirst(
                "SELECT * FROM ai_prompt WHERE enable = '1' AND `key` = 'email_monument_translate' ORDER BY id LIMIT 1"
            );

            String prompt = testText;
            if (aiPrompt != null && aiPrompt.getSystemContent() != null) {
                prompt = aiPrompt.getSystemContent() + "\n" + testText;
            }

            // 验证提供商和模型是否存在
            cn.jbolt.common.model.LlmProvider llmProvider = new cn.jbolt.common.model.LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE status = 1 AND name = ? ORDER BY priority", providerName);

            if (llmProvider == null) {
                renderJsonFail("找不到提供商: " + providerName + "，请检查提供商配置");
                return;
            }

            cn.jbolt.common.model.LlmModel llmModel = new cn.jbolt.common.model.LlmModel().dao()
                .findFirst("SELECT * FROM llm_model WHERE status = 1 AND provider_id = ? AND model_identifier = ? ORDER BY id",
                          llmProvider.getId(), modelName);

            if (llmModel == null) {
                renderJsonFail("找不到模型: " + modelName + "，提供商: " + providerName + "，请检查模型配置");
                return;
            }

            String translatedText = llmService.callLlm(providerName, modelName, prompt);
            long endTime = System.currentTimeMillis();

            Map<String, Object> result = new HashMap<>();
            result.put("provider", providerName);
            result.put("model", modelName);
            result.put("testText", testText);
            result.put("translatedText", translatedText);
            result.put("success", translatedText != null && !translatedText.trim().isEmpty());
            result.put("responseTime", (endTime - startTime) + "ms");
            result.put("promptUsed", aiPrompt != null ? "使用AI提示词" : "使用默认提示词");
            result.put("providerFound", true);
            result.put("modelFound", true);
            result.put("promptLength", prompt.length());

            renderJsonData(result);

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("provider", providerName);
            errorResult.put("model", modelName);
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("errorType", e.getClass().getSimpleName());
            renderJsonData(errorResult);
        }
    }

    /**
     * 获取配置变更历史
     */
    public void history() {
        String configKey = getPara("configKey");
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 20);

        try {
            String sql = "SELECT * FROM email_translation_config_history ";
            String countSql = "SELECT COUNT(*) FROM email_translation_config_history ";
            
            if (configKey != null && !configKey.trim().isEmpty()) {
                sql += "WHERE config_key = ? ";
                countSql += "WHERE config_key = ? ";
            }
            
            sql += "ORDER BY change_time DESC LIMIT ?, ?";

            List<Record> records;
            Record countRecord;
            
            if (configKey != null && !configKey.trim().isEmpty()) {
                records = Db.find(sql, configKey, (pageNumber - 1) * pageSize, pageSize);
                countRecord = Db.findFirst(countSql, configKey);
            } else {
                records = Db.find(sql, (pageNumber - 1) * pageSize, pageSize);
                countRecord = Db.findFirst(countSql);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", records);
            result.put("totalRow", countRecord.getLong("COUNT(*)"));
            result.put("pageNumber", pageNumber);
            result.put("pageSize", pageSize);

            renderJsonData(result);

        } catch (Exception e) {
            renderJsonFail("获取配置历史失败: " + e.getMessage());
        }
    }

    /**
     * 重置为默认配置
     */
    public void resetToDefault() {
        try {
            String changeReason = "管理员重置为默认配置";
            
            boolean success = true;
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.PRIMARY_PROVIDER, "gemini", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.PRIMARY_MODEL, "gemini-2.0-flash-exp", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BACKUP_PROVIDER, "openai", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BACKUP_MODEL, "gpt-3.5-turbo", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.MAX_RETRY_ATTEMPTS, "3", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.RETRY_DELAY_SECONDS, "5", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.ENABLE_IMAGE_TRANSLATION, "true", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.ENABLE_BATCH_PROCESSING, "true", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.BATCH_SIZE, "5", changeReason);
            success &= EmailTranslationConfig.setConfigValue(EmailTranslationConfig.TIMEOUT_SECONDS, "60", changeReason);

            if (success) {
                renderJsonSuccess("配置已重置为默认值");
            } else {
                renderJsonFail("重置配置失败");
            }

        } catch (Exception e) {
            renderJsonFail("重置配置失败: " + e.getMessage());
        }
    }
}
