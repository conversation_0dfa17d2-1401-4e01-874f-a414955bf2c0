package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.Kv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Gemini适配器使用演示
 * 展示各种翻译和图片分析场景的实际用法
 */
public class GeminiAdapterDemo {
    
    private final GeminiAdapter adapter;
    
    public GeminiAdapterDemo() {
        this.adapter = new GeminiAdapter();
    }
    
    /**
     * 演示文本翻译功能
     */
    public void demonstrateTextTranslation() {
        System.out.println("=== 文本翻译演示 ===\n");
        
        // 英文翻译
        translateText("英文翻译", 
            "Please translate this to Chinese: 'Good morning! How can I help you today?'",
            "早上好！今天我能为您做些什么？");
        
        // 日文翻译
        translateText("日文翻译",
            "请翻译：「すみません、駅はどこですか？」",
            "不好意思，车站在哪里？");
        
        // 韩文翻译
        translateText("韩文翻译",
            "请翻译：\"죄송합니다. 길을 잃었어요.\"",
            "对不起，我迷路了。");
        
        // 法文翻译
        translateText("法文翻译",
            "Traduisez en chinois: 'Bonjour, comment allez-vous?'",
            "你好，你好吗？");
    }
    
    /**
     * 演示图片分析功能
     */
    public void demonstrateImageAnalysis() {
        System.out.println("\n=== 图片分析演示 ===\n");
        
        // 单图片菜单分析
        analyzeImage("餐厅菜单分析",
            Arrays.asList("restaurant-menu.jpg"),
            "请分析这张餐厅菜单，翻译所有英文菜名，并描述菜单设计风格",
            "这是一张高档餐厅的菜单。主要菜品包括：\n" +
            "开胃菜：Caesar Salad (凯撒沙拉)、Bruschetta (意式烤面包)\n" +
            "主菜：Grilled Salmon (烤三文鱼)、Ribeye Steak (肋眼牛排)\n" +
            "甜点：Tiramisu (提拉米苏)、Crème Brûlée (焦糖布丁)\n" +
            "菜单采用黑金配色，字体优雅，整体呈现奢华风格。");
        
        // 路标识别
        analyzeImage("路标识别",
            Arrays.asList("street-sign.jpg"),
            "识别并翻译这个路标上的所有英文文字",
            "路标内容翻译：\n" +
            "- 'Downtown' → 市中心\n" +
            "- 'Airport 15km' → 机场 15公里\n" +
            "- 'Hospital 2km' → 医院 2公里\n" +
            "这是一个蓝底白字的方向指示牌，箭头清晰指向不同方向。");
        
        // 商店招牌
        analyzeImage("商店招牌分析",
            Arrays.asList("shop-sign.jpg"),
            "分析这个商店招牌，翻译英文内容并描述设计特点",
            "商店招牌分析：\n" +
            "店名：'Coffee & More' → 咖啡及更多\n" +
            "标语：'Fresh Daily' → 每日新鲜\n" +
            "营业时间：'Open 7AM-10PM' → 营业时间 上午7点-晚上10点\n" +
            "设计特点：温暖的棕色背景，白色字体，简约现代风格。");
    }
    
    /**
     * 演示多图片对比分析
     */
    public void demonstrateMultiImageAnalysis() {
        System.out.println("\n=== 多图片对比分析演示 ===\n");
        
        // 多个菜单对比
        analyzeMultipleImages("餐厅菜单对比",
            Arrays.asList("menu1.jpg", "menu2.jpg", "menu3.jpg"),
            "对比这三家餐厅的菜单，翻译菜名并分析价格定位和风格差异",
            "三家餐厅菜单对比分析：\n\n" +
            "餐厅1 - 高档西餐厅：\n" +
            "- Lobster Thermidor (热汤龙虾) $45\n" +
            "- Wagyu Beef (和牛) $65\n" +
            "风格：奢华，价格昂贵\n\n" +
            "餐厅2 - 休闲咖啡厅：\n" +
            "- Club Sandwich (俱乐部三明治) $12\n" +
            "- Caesar Salad (凯撒沙拉) $8\n" +
            "风格：轻松，价格适中\n\n" +
            "餐厅3 - 快餐店：\n" +
            "- Burger Combo (汉堡套餐) $6\n" +
            "- Chicken Wings (鸡翅) $4\n" +
            "风格：快捷，价格便宜");
        
        // 多个标识对比
        analyzeMultipleImages("公共标识对比",
            Arrays.asList("sign1.jpg", "sign2.jpg", "sign3.jpg"),
            "对比这些公共场所标识，翻译文字内容并分析设计规范",
            "公共标识对比分析：\n\n" +
            "标识1 - 地铁站：\n" +
            "- 'Subway Station' → 地铁站\n" +
            "- 'Line 1, 2' → 1号线，2号线\n" +
            "设计：蓝白配色，符合交通标识规范\n\n" +
            "标识2 - 医院：\n" +
            "- 'Emergency' → 急诊\n" +
            "- '24 Hours' → 24小时\n" +
            "设计：红白配色，国际医疗标准\n\n" +
            "标识3 - 旅游景点：\n" +
            "- 'Tourist Center' → 游客中心\n" +
            "- 'Information' → 信息咨询\n" +
            "设计：绿白配色，友好亲和");
    }
    
    /**
     * 翻译文本的辅助方法
     */
    private void translateText(String title, String input, String expectedOutput) {
        System.out.println("--- " + title + " ---");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", input));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("输入: " + input);
        System.out.println("预期输出: " + expectedOutput);
        System.out.println("请求格式正确: " + isValidJson(request));
        System.out.println();
    }
    
    /**
     * 分析单张图片的辅助方法
     */
    private void analyzeImage(String title, List<String> imagePaths, String prompt, String expectedOutput) {
        System.out.println("--- " + title + " ---");
        
        Object parts = adapter.processImages(imagePaths, prompt);
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        
        System.out.println("图片路径: " + imagePaths);
        System.out.println("分析提示: " + prompt);
        System.out.println("预期输出: " + expectedOutput);
        System.out.println("请求格式正确: " + isValidJson(request));
        System.out.println("包含图片数据: " + request.contains("inline_data"));
        System.out.println();
    }
    
    /**
     * 分析多张图片的辅助方法
     */
    private void analyzeMultipleImages(String title, List<String> imagePaths, String prompt, String expectedOutput) {
        System.out.println("--- " + title + " ---");
        
        Object parts = adapter.processImages(imagePaths, prompt);
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        
        System.out.println("图片数量: " + imagePaths.size());
        System.out.println("图片路径: " + imagePaths);
        System.out.println("分析提示: " + prompt);
        System.out.println("预期输出: " + expectedOutput);
        System.out.println("请求格式正确: " + isValidJson(request));
        System.out.println("包含多个图片: " + (request.split("inline_data").length - 1) + " 个");
        System.out.println();
    }
    
    /**
     * 检查JSON格式是否正确
     */
    private boolean isValidJson(String json) {
        try {
            JSON.parseObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 主方法 - 运行所有演示
     */
    public static void main(String[] args) {
        GeminiAdapterDemo demo = new GeminiAdapterDemo();
        
        System.out.println("Gemini适配器功能演示");
        System.out.println("===================\n");
        
        // 演示文本翻译
        demo.demonstrateTextTranslation();
        
        // 演示图片分析
        demo.demonstrateImageAnalysis();
        
        // 演示多图片对比
        demo.demonstrateMultiImageAnalysis();
        
        System.out.println("演示完成！");
        System.out.println("\n使用说明：");
        System.out.println("1. 文本翻译：支持英文、日文、韩文、法文等多种语言翻译成中文");
        System.out.println("2. 图片分析：可以识别图片中的文字并翻译，描述图片内容");
        System.out.println("3. 多图对比：支持同时分析多张图片，进行对比分析");
        System.out.println("4. 所有功能都转换为OpenAI兼容格式，便于集成");
    }
}