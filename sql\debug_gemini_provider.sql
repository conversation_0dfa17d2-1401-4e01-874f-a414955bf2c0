-- 调试Gemini提供商配置

-- 1. 检查gemini提供商的详细信息
SELECT 
    id,
    name,
    api_type,
    api_base_url,
    api_key,
    api_secret,
    status,
    priority,
    rate_limit_per_minute,
    default_model,
    adapter_class,
    create_time,
    update_time
FROM llm_provider 
WHERE name = 'gemini';

-- 2. 检查api_secret字段是否为NULL
SELECT 
    name,
    CASE 
        WHEN api_secret IS NULL THEN 'NULL'
        WHEN api_secret = '' THEN 'EMPTY'
        ELSE CONCAT('有值(长度:', LENGTH(api_secret), ')')
    END as api_secret_status,
    api_secret
FROM llm_provider 
WHERE name = 'gemini';

-- 3. 检查所有启用的提供商
SELECT 
    id,
    name,
    CASE 
        WHEN api_key IS NULL THEN 'NULL'
        WHEN api_key = '' THEN 'EMPTY'
        ELSE CONCAT('有值(', LENGTH(api_key) - LENGTH(REPLACE(api_key, ',', '')) + 1, '个密钥)')
    END as api_key_status,
    CASE 
        WHEN api_secret IS NULL THEN 'NULL'
        WHEN api_secret = '' THEN 'EMPTY'
        ELSE '有值'
    END as api_secret_status,
    status,
    rate_limit_per_minute
FROM llm_provider 
WHERE status = 1
ORDER BY name;

-- 4. 模拟API密钥分割逻辑
SELECT 
    name,
    api_key,
    api_secret,
    CASE 
        WHEN api_secret IS NULL THEN '需要设置为空字符串'
        ELSE '正常'
    END as fix_needed
FROM llm_provider 
WHERE name = 'gemini';
