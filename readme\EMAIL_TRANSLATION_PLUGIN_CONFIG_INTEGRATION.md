# EmailTranslationPlugin配置化翻译集成

## 修改概述

将EmailTranslationPlugin中硬编码的LlmService调用替换为配置化的翻译服务，实现统一的翻译提供商管理和自动故障切换。

## 问题分析

### 原有问题

1. **硬编码提供商**：所有翻译调用都硬编码使用"gemini"和"gemini-2.0-flash-exp"
2. **无法配置**：无法通过配置界面更改翻译提供商和模型
3. **无故障切换**：提供商不可用时无法自动切换到备用提供商
4. **管理分散**：翻译配置分散在多个地方，难以统一管理

### 硬编码调用位置

在`EmailTranslationPlugin.java`中发现3处硬编码调用：

1. **无附件邮件翻译**（第1111行）：
   ```java
   String result = LlmService.me().callLlm("gemini", "gemini-2.0-flash-exp", prompt);
   ```

2. **第一批附件翻译**（第1123行）：
   ```java
   String firstResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", firstPrompt, firstBatch);
   ```

3. **后续批次附件翻译**（第1138行）：
   ```java
   String batchResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", batchPrompt, currentBatch);
   ```

## 修改方案

### 1. 集成ConfigurableTranslationService

**添加依赖**：
```java
import cn.jbolt.mail.gpt.service.ConfigurableTranslationService;
import java.util.Arrays;
```

**替换调用方式**：
- 文本翻译：使用`ConfigurableTranslationService.getInstance().translateText()`
- 图片翻译：使用`ConfigurableTranslationService.getInstance().translateImage()`

### 2. 具体修改内容

#### 修改1：无附件邮件翻译
```java
// 修改前
String result = LlmService.me().callLlm("gemini", "gemini-2.0-flash-exp",
    aiPrompt.getSystemContent() + "\n邮件主题：" + subject + "\n邮件内容:" + latestContent);

// 修改后
String content = "\n邮件主题：" + subject + "\n邮件内容:" + latestContent;
String result = ConfigurableTranslationService.getInstance().translateText(content, 
    ConfigurableTranslationService.TranslationType.CONTENT);
```

#### 修改2：第一批附件翻译
```java
// 修改前
String firstPrompt = aiPrompt.getSystemContent() + "\n邮件主题：" + subject + "\n邮件内容:" + latestContent + "\n图片都是附件。";
String firstResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", firstPrompt, firstBatch);

// 修改后
String firstResult = ConfigurableTranslationService.getInstance().translateImage(firstBatch);
```

#### 修改3：后续批次附件翻译
```java
// 修改前
String batchPrompt = aiPrompt.getSystemContent();
String batchResult = LlmService.me().callLlmWithImages("gemini", "gemini-2.0-flash-exp", batchPrompt, currentBatch);

// 修改后
String batchResult = ConfigurableTranslationService.getInstance().translateImage(currentBatch);
```

#### 修改4：单独翻译功能
```java
// 修改前
String translatedSubject = translateTextWithModel(subject, aiPrompt, providerId, modelId);
String translatedContent = translateTextWithModel(originalContent, aiPrompt, providerId, modelId);
String imageTranslation = translateSingleImageWithModel(aiPrompt, imagePath, i + 1, providerId, modelId);

// 修改后
String translatedSubject = ConfigurableTranslationService.getInstance().translateText(subject, 
    ConfigurableTranslationService.TranslationType.SUBJECT);
String translatedContent = ConfigurableTranslationService.getInstance().translateText(originalContent, 
    ConfigurableTranslationService.TranslationType.CONTENT);
List<String> singleImageList = Arrays.asList(imagePath);
String imageTranslation = ConfigurableTranslationService.getInstance().translateImage(singleImageList);
```

### 3. 清理工作

**移除不再使用的方法**：
- `translateTextWithModel()` - 已标记为未使用
- `translateSingleImageWithModel()` - 已标记为未使用
- `processEmailWithSeparateTranslations()` - 已标记为@Deprecated
- `processEmailWithBatchImages()` - 已标记为@Deprecated

**移除不再使用的import**：
- `cn.jbolt.common.model.EmailTranslationConfig` - 现在通过ConfigurableTranslationService访问

## 修改后的优势

### 1. 统一配置管理
- 所有翻译提供商配置在一个地方管理
- 支持通过Web界面动态配置
- 配置变更无需重启应用

### 2. 自动故障切换
- 主要提供商不可用时自动切换到备用提供商
- 支持多次重试机制
- 提供详细的错误日志和状态反馈

### 3. 灵活的提示词管理
- 使用统一的AI提示词管理系统
- 支持提示词版本控制和历史记录
- 可以为不同场景配置不同的提示词

### 4. 更好的可维护性
- 减少硬编码，提高代码灵活性
- 统一的翻译接口，便于扩展和维护
- 清晰的配置层次结构

## 配置流程

### 1. 配置AI提示词
访问：`/admin/aiPrompt`
1. 新增或编辑提示词
2. 设置Key为：`email_monument_translate`
3. 配置系统内容和用户内容
4. 启用提示词

### 2. 配置翻译提供商
访问：`/admin/email/translation/config`
1. 设置主要翻译提供商和模型
2. 设置备用翻译提供商和模型
3. 配置重试参数和功能开关
4. 测试翻译功能

### 3. 验证配置
1. 运行测试：`EmailTranslationPluginConfigTest.runAllTests()`
2. 检查配置摘要和状态
3. 执行实际的邮件翻译测试

## 兼容性说明

### 向后兼容
- 保持了原有的翻译功能和接口
- 翻译结果格式和数据结构不变
- 现有的邮件翻译记录不受影响

### 配置迁移
- 如果没有配置翻译提供商，会使用默认配置
- 如果没有AI提示词，会使用内置的默认提示词
- 渐进式升级，不会影响现有功能

## 测试验证

### 单元测试
```java
// 运行配置化翻译测试
EmailTranslationPluginConfigTest test = new EmailTranslationPluginConfigTest();
test.runAllTests();
```

### 功能测试
1. **配置测试**：验证提供商和模型配置
2. **翻译测试**：测试文本和图片翻译功能
3. **故障切换测试**：模拟主要提供商故障
4. **性能测试**：验证翻译响应时间

### 集成测试
1. 发送包含文本和图片的测试邮件
2. 触发邮件翻译功能
3. 检查翻译结果和数据库记录
4. 验证配置变更的实时生效

## 监控和日志

### 翻译状态监控
- 翻译成功/失败统计
- 提供商使用情况统计
- 响应时间监控

### 详细日志记录
- 翻译请求和响应日志
- 提供商切换日志
- 配置变更日志
- 错误和异常日志

## 总结

通过这次修改，EmailTranslationPlugin实现了：

1. ✅ **配置化管理**：移除硬编码，支持动态配置
2. ✅ **故障切换**：主备提供商自动切换
3. ✅ **统一管理**：集成现有的配置和提示词管理系统
4. ✅ **向后兼容**：保持原有功能和接口不变
5. ✅ **可维护性**：清理冗余代码，提高代码质量

现在EmailTranslationPlugin可以根据配置动态选择翻译提供商，支持故障自动切换，并且所有翻译相关的配置都可以通过Web界面统一管理。
