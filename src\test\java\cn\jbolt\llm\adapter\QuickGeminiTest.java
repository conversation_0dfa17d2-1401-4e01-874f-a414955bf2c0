package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * 快速Gemini API测试
 * 用于快速验证API调用和响应格式
 */
public class QuickGeminiTest {
    
    // 请替换为你的实际API Key
    private static final String API_KEY = "AIzaSyBPnUFFm1nTZ-MDIuPITO-TGSV3-4t79f8";
    
    public static void main(String[] args) {
        if (API_KEY.equals("YOUR_API_KEY_HERE")) {
            System.out.println("请先设置API Key!");
            System.out.println("1. 访问 https://makersuite.google.com/app/apikey 获取API Key");
            System.out.println("2. 将API Key替换到代码中的API_KEY变量");
            return;
        }
        
        // 测试简单的文本生成
        testSimpleText();
        
        // 测试翻译功能
        testTranslation();
    }
    
    /**
     * 测试简单文本生成
     */
    private static void testSimpleText() {
        System.out.println("=== 测试简单文本生成 ===");
        
        String requestBody = "{\n" +
            "  \"contents\": [{\n" +
            "    \"role\": \"user\",\n" +
            "    \"parts\": [{\n" +
            "      \"text\": \"你好，请介绍一下自己\"\n" +
            "    }]\n" +
            "  }],\n" +
            "  \"generationConfig\": {\n" +
            "    \"temperature\": 0.7,\n" +
            "    \"maxOutputTokens\": 1000\n" +
            "  }\n" +
            "}";
        
        try {
            String response = callGeminiApi("gemini-2.5-pro", requestBody);
            System.out.println("请求体:");
            System.out.println(formatJson(requestBody));
            System.out.println("\n原始响应:");
            System.out.println(formatJson(response));
            
            // 解析响应内容
            parseAndDisplayResponse(response);
            
        } catch (Exception e) {
            System.err.println("调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试翻译功能
     */
    private static void testTranslation() {
        System.out.println("\n=== 测试翻译功能 ===");
        
        String requestBody = "{\n" +
            "  \"contents\": [{\n" +
            "    \"role\": \"user\",\n" +
            "    \"parts\": [{\n" +
            "      \"text\": \"请将以下英文翻译成中文：Good morning! How can I help you today?\"\n" +
            "    }]\n" +
            "  }]\n" +
            "}";
        
        try {
            String response = callGeminiApi("gemini-2.5-pro", requestBody);
            System.out.println("请求体:");
            System.out.println(formatJson(requestBody));
            System.out.println("\n原始响应:");
            System.out.println(formatJson(response));
            
            // 解析响应内容
            parseAndDisplayResponse(response);
            
        } catch (Exception e) {
            System.err.println("调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 调用Gemini API
     */
    private static String callGeminiApi(String model, String requestBody) throws Exception {
        String urlString = "https://generativelanguage.googleapis.com/v1beta/models/" + 
                          model + ":generateContent?key=" + API_KEY;
        
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // 设置请求方法和头部
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // 发送请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        int responseCode = connection.getResponseCode();
        System.out.println("HTTP响应码: " + responseCode);
        
        BufferedReader reader;
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        
        return response.toString();
    }
    
    /**
     * 解析并显示响应内容
     */
    private static void parseAndDisplayResponse(String response) {
        try {
            JSONObject jsonResponse = JSON.parseObject(response);
            
            System.out.println("\n=== 响应解析 ===");
            
            // 检查是否有错误
            if (jsonResponse.containsKey("error")) {
                JSONObject error = jsonResponse.getJSONObject("error");
                System.out.println("错误信息: " + error.getString("message"));
                System.out.println("错误代码: " + error.getIntValue("code"));
                return;
            }
            
            // 解析正常响应
            if (jsonResponse.containsKey("candidates")) {
                var candidates = jsonResponse.getJSONArray("candidates");
                if (candidates != null && candidates.size() > 0) {
                    JSONObject candidate = candidates.getJSONObject(0);
                    
                    if (candidate.containsKey("content")) {
                        JSONObject content = candidate.getJSONObject("content");
                        if (content.containsKey("parts")) {
                            var parts = content.getJSONArray("parts");
                            if (parts != null && parts.size() > 0) {
                                JSONObject part = parts.getJSONObject(0);
                                if (part.containsKey("text")) {
                                    System.out.println("生成的文本: " + part.getString("text"));
                                }
                            }
                        }
                    }
                    
                    if (candidate.containsKey("finishReason")) {
                        System.out.println("完成原因: " + candidate.getString("finishReason"));
                    }
                }
            }
            
            // 显示用量信息
            if (jsonResponse.containsKey("usageMetadata")) {
                JSONObject usage = jsonResponse.getJSONObject("usageMetadata");
                System.out.println("\n用量信息:");
                if (usage.containsKey("promptTokenCount")) {
                    System.out.println("  输入Token数: " + usage.getIntValue("promptTokenCount"));
                }
                if (usage.containsKey("candidatesTokenCount")) {
                    System.out.println("  输出Token数: " + usage.getIntValue("candidatesTokenCount"));
                }
                if (usage.containsKey("totalTokenCount")) {
                    System.out.println("  总Token数: " + usage.getIntValue("totalTokenCount"));
                }
            }
            
        } catch (Exception e) {
            System.err.println("解析响应失败: " + e.getMessage());
        }
    }
    
    /**
     * 格式化JSON
     */
    private static String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}