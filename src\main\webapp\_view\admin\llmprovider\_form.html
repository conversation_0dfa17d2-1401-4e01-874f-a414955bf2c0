<form onsubmit="return false;" id="LlmProvider_Form" action="#(action)" method="post">
	#if(llmProvider.id??)
	<input type="hidden" name="llmProvider.id" value="#(llmProvider.id??)" />
	#end
<div class="row">
<div class="col" >
<div class="form-group row" >
<label class="col-sm-2 col-form-label">厂商名称</label>
<div class="col-10">
    <input type="text"    data-rule="required" data-notnull="true" data-tips="请输入厂商名称"  data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入厂商名称" maxlength="100" name="llmProvider.name" value="#(llmProvider.name?? )"/>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">API基础URL</label>
<div class="col-10">
    <textarea style="height:100px;"  data-rule="required"  data-notnull="true" data-tips="请输入API基础URL" data-with-clearbtn="true" autocomplete="off"  class="form-control"  placeholder="请输入API基础URL" maxlength="255" name="llmProvider.apiBaseUrl">#(llmProvider.apiBaseUrl??)</textarea>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">API密钥</label>
<div class="col-10">
    <textarea style="height:100px;"  data-rule="required"  data-notnull="true" data-tips="请输入API密钥" data-with-clearbtn="true" autocomplete="off"  class="form-control"  placeholder="请输入API密钥" maxlength="255" name="llmProvider.apiKey">#(llmProvider.apiKey??)</textarea>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">API密钥(可选)</label>
<div class="col-10">
    <textarea style="height:100px;" data-with-clearbtn="true" autocomplete="off"  class="form-control"  placeholder="请输入API密钥(可选)" maxlength="255" name="llmProvider.apiSecret">#(llmProvider.apiSecret??)</textarea>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">API类型(openai/custom)</label>
<div class="col-10">
    <input type="text"    data-rule="required" data-notnull="true" data-tips="请输入API类型(openai/custom)"  data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入API类型(openai/custom)" maxlength="50" name="llmProvider.apiType" value="#(llmProvider.apiType?? openai)"/>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">适配器类名(非OpenAI兼容时需要)</label>
<div class="col-10">
    <textarea style="height:100px;" data-with-clearbtn="true" autocomplete="off"  class="form-control"  placeholder="请输入适配器类名(非OpenAI兼容时需要)" maxlength="255" name="llmProvider.adapterClass">#(llmProvider.adapterClass??)</textarea>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">请求超时时间(秒)</label>
<div class="col-10">
    <input type="number"   data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入请求超时时间(秒)" maxlength="10" name="llmProvider.requestTimeout" value="#(llmProvider.requestTimeout?? 30)"/>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">默认最大token数</label>
<div class="col-10">
    <input type="number"   data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入默认最大token数" maxlength="10" name="llmProvider.maxTokens" value="#(llmProvider.maxTokens?? 4096)"/>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">默认模型标识</label>
<div class="col-10">
    <input type="text"   data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入默认模型标识" maxlength="100" name="llmProvider.defaultModel" value="#(llmProvider.defaultModel?? )"/>
</div>
</div>
<div class="form-group row" 
data-radio
data-name="llmProvider.status"
data-value-attr="sn"
data-default="true"
data-url="admin/dictionary/options?key=options_enable"
data-label="状态(0-禁用,1-启用)"
data-width="col-sm-2,col-10"
data-value="#(llmProvider.status??)"
data-rule="radio"
data-notnull="false"
data-tips="请选择状态(0-禁用,1-启用)"
data-inline="true"
>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">优先级(数字越小优先级越高)</label>
<div class="col-10">
    <input type="number"   data-with-clearbtn="true" autocomplete="off"  class="form-control" placeholder="请输入优先级(数字越小优先级越高)" maxlength="10" name="llmProvider.priority" value="#(llmProvider.priority?? 10)"/>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">每分钟请求限制</label>
<div class="col-10">
    <input type="number" data-with-clearbtn="true" autocomplete="off" class="form-control" placeholder="请输入每分钟请求限制数量" maxlength="10" name="llmProvider.rateLimitPerMinute" value="#(llmProvider.rateLimitPerMinute?? 10)"/>
    <small class="form-text text-muted">设置该提供商每分钟最大请求数，用于频率控制和负载均衡。多个API密钥时，每个密钥都有此限制。</small>
</div>
</div>
<div class="form-group row" >
<label class="col-sm-2 col-form-label">备注</label>
<div class="col-10">
    <textarea style="height:100px;" data-with-clearbtn="true" autocomplete="off"  class="form-control"  placeholder="请输入备注" maxlength="500" name="llmProvider.remark">#(llmProvider.remark??)</textarea>
</div>
</div>
</div>
</div>
</form>
#define js()
#@jqueryvalidatejs?()
<script>
function beforeFormSubmit() {
    return $("#LlmProvider_Form").valid();
}

$(function() {
    $("#LlmProvider_Form").validate();
});
</script>
#include("/_view/_admin/common/_formjs.html",formId="LlmProvider_Form")
#end
