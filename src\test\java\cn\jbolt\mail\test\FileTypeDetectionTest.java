package cn.jbolt.mail.test;

import cn.jbolt.mail.gpt.parser.EmailParserJakarta;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Method;

/**
 * File type detection test
 */
public class FileTypeDetectionTest {

    public static void main(String[] args) {
        testFileTypeDetection();
    }

    /**
     * Test file type detection
     */
    private static void testFileTypeDetection() {
        System.out.println("=== File Type Detection Test ===\n");

        EmailParserJakarta parser = new EmailParserJakarta();
        
        try {
            // Use reflection to access private methods
            Method detectMethod = EmailParserJakarta.class.getDeclaredMethod("detectFileTypeFromHeader", byte[].class, int.class);
            detectMethod.setAccessible(true);

            Method detectAndFixMethod = EmailParserJakarta.class.getDeclaredMethod("detectAndFixFileExtension", String.class, InputStream.class);
            detectAndFixMethod.setAccessible(true);

            // Test PDF detection
            testPdfDetection(detectMethod, parser);

            // Test ZIP detection
            testZipDetection(detectMethod, parser);

            // Test PNG detection
            testPngDetection(detectMethod, parser);

            // Test JPEG detection
            testJpegDetection(detectMethod, parser);

            // Test complete filename correction
            testFileNameCorrection(detectAndFixMethod, parser);

        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testPdfDetection(Method detectMethod, EmailParserJakarta parser) throws Exception {
        System.out.println("1. Test PDF detection:");

        // PDF header
        String pdfHeader = "%PDF-1.5\n%¡³Å×\n6 0 obj\n<</Nums[ 0 7 0 R ]>>";
        byte[] pdfBytes = pdfHeader.getBytes("ISO-8859-1");

        String result = (String) detectMethod.invoke(parser, pdfBytes, pdfBytes.length);
        System.out.println("   PDF header detection result: " + result);
        System.out.println("   Expected: .pdf");
        System.out.println("   Detection " + (".pdf".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    private static void testZipDetection(Method detectMethod, EmailParserJakarta parser) throws Exception {
        System.out.println("2. Test ZIP detection:");

        // ZIP header (PK)
        byte[] zipBytes = {0x50, 0x4B, 0x03, 0x04, 0x14, 0x00, 0x00, 0x00};

        String result = (String) detectMethod.invoke(parser, zipBytes, zipBytes.length);
        System.out.println("   ZIP header detection result: " + result);
        System.out.println("   Expected: .zip");
        System.out.println("   Detection " + (".zip".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }

    private static void testPngDetection(Method detectMethod, EmailParserJakarta parser) throws Exception {
        System.out.println("3. Test PNG detection:");

        // PNG header
        byte[] pngBytes = {(byte)0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A};

        String result = (String) detectMethod.invoke(parser, pngBytes, pngBytes.length);
        System.out.println("   PNG header detection result: " + result);
        System.out.println("   Expected: .png");
        System.out.println("   Detection " + (".png".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }

    private static void testJpegDetection(Method detectMethod, EmailParserJakarta parser) throws Exception {
        System.out.println("4. Test JPEG detection:");

        // JPEG header
        byte[] jpegBytes = {(byte)0xFF, (byte)0xD8, (byte)0xFF, (byte)0xE0, 0x00, 0x10, 0x4A, 0x46};

        String result = (String) detectMethod.invoke(parser, jpegBytes, jpegBytes.length);
        System.out.println("   JPEG header detection result: " + result);
        System.out.println("   Expected: .jpg");
        System.out.println("   Detection " + (".jpg".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    private static void testFileNameCorrection(Method detectAndFixMethod, EmailParserJakarta parser) throws Exception {
        System.out.println("5. Test filename correction:");

        // Create PDF content stream
        String pdfContent = "%PDF-1.5\n%¡³Å×\n6 0 obj\n<</Nums[ 0 7 0 R ]>>";
        InputStream pdfStream = new ByteArrayInputStream(pdfContent.getBytes("ISO-8859-1"));

        // Test .unknown filename correction
        String result1 = (String) detectAndFixMethod.invoke(parser, "1253 Tanis.unknown", pdfStream);
        System.out.println("   Original filename: 1253 Tanis.unknown");
        System.out.println("   Corrected to: " + result1);
        System.out.println("   Expected: 1253 Tanis.pdf");
        System.out.println("   Correction " + ("1253 Tanis.pdf".equals(result1) ? "SUCCESS" : "FAILED"));

        // Recreate stream (because the above stream was already read)
        pdfStream = new ByteArrayInputStream(pdfContent.getBytes("ISO-8859-1"));

        // Test filename without extension correction
        String result2 = (String) detectAndFixMethod.invoke(parser, "1253 Tanis", pdfStream);
        System.out.println("\n   Original filename: 1253 Tanis");
        System.out.println("   Corrected to: " + result2);
        System.out.println("   Expected: 1253 Tanis.pdf");
        System.out.println("   Correction " + ("1253 Tanis.pdf".equals(result2) ? "SUCCESS" : "FAILED"));

        System.out.println("\n=== Test Complete ===");
    }
}
