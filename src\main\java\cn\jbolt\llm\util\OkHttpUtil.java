package cn.jbolt.llm.util;

import com.jfinal.kit.LogKit;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp工具类
 * 提供HTTP请求功能，替代JFinal的HttpKit
 */
public class OkHttpUtil {

    // 移除静态CLIENT，改为动态创建
    // private static final OkHttpClient CLIENT = ...

    /**
     * 发送POST请求
     *
     * @param url      请求URL
     * @param jsonBody JSON请求体
     * @param headers  请求头
     * @return 响应内容
     * @throws IOException 如果请求失败
     */
    public static String post(String url, String jsonBody, Map<String, String> headers) throws IOException {
        // 打印详细的请求信息
        LogKit.info("OkHttp发送POST请求到: " + url);
        LogKit.info("OkHttp请求头: " + headers);
        LogKit.info("OkHttp请求体: " + jsonBody);

        // 检查必要的请求头
        if (headers == null || !headers.containsKey("Content-Type")) {
            LogKit.warn("OkHttp请求缺少Content-Type头部，添加默认值");
            if (headers == null) {
                headers = new java.util.HashMap<>();
            }
            headers.put("Content-Type", "application/json");
        }

        // 创建请求体
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, mediaType);

        // 创建请求构建器
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        // 添加请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }

        // 构建请求
        Request request = requestBuilder.build();

        // 打印完整请求信息
        LogKit.info("OkHttp完整请求: " + request.toString());

        // 使用支持代理的客户端执行请求
        OkHttpClient client = ProxyConfigUtil.createHttpClientWithProxy();
        try (Response response = client.newCall(request).execute()) {
            int code = response.code();
            String message = response.message();

            LogKit.info("OkHttp响应状态码: " + code);
            LogKit.info("OkHttp响应消息: " + message);

            // 打印响应头
            Headers responseHeaders = response.headers();
            for (String name : responseHeaders.names()) {
                LogKit.info("OkHttp响应头 " + name + ": " + responseHeaders.get(name));
            }

            if (!response.isSuccessful()) {
                // 尝试获取错误响应体
                String errorBody = "";
                ResponseBody responseErrorBody = response.body();
                if (responseErrorBody != null) {
                    try {
                        errorBody = responseErrorBody.string();
                        LogKit.error("OkHttp错误响应体: " + errorBody);
                    } catch (Exception e) {
                        LogKit.error("OkHttp无法读取错误响应体", e);
                    }
                }

                throw new IOException("Unexpected code " + code + ": " + message + ", Error body: " + errorBody);
            }

            // 返回响应体
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                LogKit.warn("OkHttp响应体为空");
                return "";
            }

            String responseString = responseBody.string();
            LogKit.info("OkHttp响应体: " + responseString);
            return responseString;
        } catch (IOException e) {
            LogKit.error("OkHttp请求异常: " + e.getMessage(), e);
            throw e;
        }
    }

    // 移除了静态超时设置方法，现在超时时间在ProxyConfigUtil中配置
}
