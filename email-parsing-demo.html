<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件内容解析演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .demo-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        
        /* 邮件内容解析样式 */
        .email-section {
            margin-bottom: 15px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }
        .current-message {
            background-color: #ffffff !important;
            border: 1px solid #e9ecef;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .different-sender-quoted {
            background-color: #e3f2fd !important;
            border-left: 4px solid #2196f3 !important;
            padding: 15px;
            margin-top: 20px;
            position: relative;
            box-shadow: 0 1px 3px rgba(33, 150, 243, 0.1);
        }
        .different-sender-quoted::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, #e3f2fd);
            border-radius: 6px 6px 0 0;
        }
        .same-sender-quoted {
            background-color: #f5f5f5 !important;
            border-left: 4px solid #ccc !important;
            padding: 15px;
            margin-top: 15px;
            color: #666 !important;
            font-style: italic;
        }
        .quoted-sender-info {
            font-size: 0.9em !important;
            color: #1976d2 !important;
            font-weight: 500 !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #e3f2fd !important;
            padding-bottom: 5px !important;
            display: flex;
            align-items: center;
        }
        .quoted-sender-info i {
            margin-right: 5px;
            font-size: 0.8em;
        }
        .email-content-text {
            line-height: 1.6;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
</head>
<body>
    <div class="container">
        <h1>邮件内容解析和视觉区分演示</h1>
        <p>此演示展示了如何解析邮件内容，识别引用和历史内容，并应用不同的视觉样式来区分不同发件人的内容。</p>
        
        <div class="demo-section">
            <div class="demo-title">示例1：当前邮件内容</div>
            <div class="email-section current-message">
                <div class="email-content-text">
                    这是当前邮件的主要内容。用户可以清楚地看到这是新的邮件内容，与引用的历史内容区分开来。
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">示例2：来自不同发件人的引用内容</div>
            <div class="email-section different-sender-quoted">
                <div class="quoted-sender-info">
                    <i class="fa fa-reply"></i>来自: <EMAIL>
                </div>
                <div class="email-content-text">
                    这是来自不同发件人的引用内容。注意它有浅蓝色背景和蓝色左边框，清楚地表明这是来自其他人的邮件内容。
                    <br><br>
                    From: <EMAIL><br>
                    Sent: Monday, January 15, 2024 10:30 AM<br>
                    To: <EMAIL><br>
                    Subject: Re: 项目讨论
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">示例3：来自同一发件人的引用内容</div>
            <div class="email-section same-sender-quoted">
                <div class="email-content-text">
                    这是来自同一发件人的引用内容（例如之前的邮件）。它使用灰色背景和斜体文字，表明这是历史内容但来自同一个人。
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">功能特点</div>
            <ul>
                <li><strong>智能解析：</strong>自动识别邮件中的引用分隔符和历史内容</li>
                <li><strong>发件人识别：</strong>区分来自不同发件人的内容</li>
                <li><strong>视觉区分：</strong>使用不同的背景色和边框样式</li>
                <li><strong>响应式设计：</strong>在不同屏幕尺寸下都能良好显示</li>
                <li><strong>夜间模式支持：</strong>自动适配深色主题</li>
                <li><strong>打印友好：</strong>打印时使用适合的样式</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">支持的邮件格式</div>
            <ul>
                <li>纯文本邮件</li>
                <li>HTML邮件</li>
                <li>混合格式邮件</li>
                <li>多种引用分隔符格式（中英文）</li>
                <li>Gmail、Outlook、Apple Mail等主流邮件客户端格式</li>
            </ul>
        </div>
    </div>
</body>
</html>
