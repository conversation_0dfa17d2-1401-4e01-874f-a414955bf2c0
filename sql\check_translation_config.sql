-- 邮件翻译配置检查SQL脚本

-- 1. 检查LLM提供商配置
SELECT '=== LLM提供商配置 ===' as section;
SELECT 
    id,
    name,
    api_type,
    api_base_url,
    CASE WHEN status = 1 THEN '启用' ELSE '禁用' END as status,
    priority,
    default_model,
    adapter_class
FROM llm_provider 
ORDER BY priority;

-- 检查gemini提供商
SELECT '=== Gemini提供商检查 ===' as section;
SELECT 
    id,
    name,
    CASE WHEN status = 1 THEN '启用' ELSE '禁用' END as status,
    api_type,
    api_base_url,
    adapter_class
FROM llm_provider 
WHERE name = 'gemini';

-- 2. 检查LLM模型配置
SELECT '=== LLM模型配置 ===' as section;
SELECT 
    m.id,
    p.name as provider_name,
    m.model_identifier,
    m.model_name,
    CASE WHEN m.status = 1 THEN '启用' ELSE '禁用' END as status,
    m.max_tokens,
    CASE WHEN m.supports_images = 1 THEN '支持' ELSE '不支持' END as supports_images
FROM llm_model m
JOIN llm_provider p ON m.provider_id = p.id
ORDER BY p.name, m.id;

-- 检查gemini模型
SELECT '=== Gemini模型检查 ===' as section;
SELECT 
    m.id,
    m.model_identifier,
    m.model_name,
    CASE WHEN m.status = 1 THEN '启用' ELSE '禁用' END as status,
    m.max_tokens,
    CASE WHEN m.supports_images = 1 THEN '支持' ELSE '不支持' END as supports_images
FROM llm_model m
JOIN llm_provider p ON m.provider_id = p.id
WHERE p.name = 'gemini'
ORDER BY m.id;

-- 检查gemini-2.5-pro模型
SELECT '=== Gemini-2.5-Pro模型检查 ===' as section;
SELECT 
    m.id,
    p.name as provider_name,
    m.model_identifier,
    m.model_name,
    CASE WHEN m.status = 1 THEN '启用' ELSE '禁用' END as status
FROM llm_model m
JOIN llm_provider p ON m.provider_id = p.id
WHERE p.name = 'gemini' AND m.model_identifier = 'gemini-2.5-pro';

-- 3. 检查AI提示词配置
SELECT '=== AI提示词配置 ===' as section;
SELECT 
    id,
    `key`,
    CASE WHEN enable = 1 THEN '启用' ELSE '禁用' END as enable,
    LENGTH(system_content) as system_content_length,
    LENGTH(user_content) as user_content_length,
    remark,
    create_time
FROM ai_prompt 
WHERE `key` = 'email_monument_translate';

-- 4. 检查API密钥配置
SELECT '=== API密钥配置 ===' as section;
SELECT 
    ak.id,
    p.name as provider_name,
    CONCAT(LEFT(ak.api_key, 10), '...') as api_key_preview,
    CASE WHEN ak.status = 1 THEN '启用' ELSE '禁用' END as status,
    ak.rate_limit_per_minute,
    ak.create_time
FROM llm_api_key ak
JOIN llm_provider p ON ak.provider_id = p.id
ORDER BY p.name;

-- 检查gemini API密钥
SELECT '=== Gemini API密钥检查 ===' as section;
SELECT 
    ak.id,
    CONCAT(LEFT(ak.api_key, 10), '...') as api_key_preview,
    CASE WHEN ak.status = 1 THEN '启用' ELSE '禁用' END as status,
    ak.rate_limit_per_minute,
    ak.create_time
FROM llm_api_key ak
JOIN llm_provider p ON ak.provider_id = p.id
WHERE p.name = 'gemini';

-- 5. 检查邮件翻译配置
SELECT '=== 邮件翻译配置 ===' as section;
SELECT 
    config_key,
    config_value,
    value_type,
    description,
    CASE WHEN is_enabled = 1 THEN '启用' ELSE '禁用' END as is_enabled
FROM email_translation_config 
WHERE config_key LIKE 'translation.%'
ORDER BY config_key;

-- 6. 完整性检查
SELECT '=== 完整性检查结果 ===' as section;

-- 检查gemini提供商是否存在且启用
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ gemini提供商配置正常'
        ELSE '✗ gemini提供商配置缺失或未启用'
    END as gemini_provider_check
FROM llm_provider 
WHERE name = 'gemini' AND status = 1;

-- 检查gemini-2.5-pro模型是否存在且启用
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ gemini-2.5-pro模型配置正常'
        ELSE '✗ gemini-2.5-pro模型配置缺失或未启用'
    END as gemini_model_check
FROM llm_model m
JOIN llm_provider p ON m.provider_id = p.id
WHERE p.name = 'gemini' AND m.model_identifier = 'gemini-2.5-pro' AND m.status = 1;

-- 检查翻译提示词是否存在且启用
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 翻译提示词配置正常'
        ELSE '✗ 翻译提示词配置缺失或未启用'
    END as prompt_check
FROM ai_prompt 
WHERE `key` = 'email_monument_translate' AND enable = 1;

-- 检查gemini API密钥是否存在且启用
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ gemini API密钥配置正常'
        ELSE '✗ gemini API密钥配置缺失或未启用'
    END as api_key_check
FROM llm_api_key ak
JOIN llm_provider p ON ak.provider_id = p.id
WHERE p.name = 'gemini' AND ak.status = 1;

-- 7. 修复建议
SELECT '=== 修复建议 ===' as section;

-- 如果gemini提供商不存在，提供创建SQL
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM llm_provider WHERE name = 'gemini' AND status = 1) = 0 
        THEN 'INSERT INTO llm_provider (name, api_type, api_base_url, status, priority, default_model, adapter_class) VALUES (''gemini'', ''gemini'', ''https://generativelanguage.googleapis.com/v1beta/models/'', 1, 1, ''gemini-2.5-pro'', ''cn.jbolt.llm.adapter.GeminiAdapter'');'
        ELSE '-- gemini提供商已存在'
    END as create_gemini_provider;

-- 如果gemini-2.5-pro模型不存在，提供创建SQL
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM llm_model m JOIN llm_provider p ON m.provider_id = p.id WHERE p.name = 'gemini' AND m.model_identifier = 'gemini-2.5-pro' AND m.status = 1) = 0 
        THEN 'INSERT INTO llm_model (provider_id, model_identifier, model_name, status, max_tokens, supports_images) SELECT id, ''gemini-2.5-pro'', ''Gemini 2.5 Pro'', 1, 8192, 1 FROM llm_provider WHERE name = ''gemini'';'
        ELSE '-- gemini-2.5-pro模型已存在'
    END as create_gemini_model;

-- 如果翻译提示词不存在，提供创建SQL
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM ai_prompt WHERE `key` = 'email_monument_translate' AND enable = 1) = 0 
        THEN 'INSERT INTO ai_prompt (`key`, system_content, user_content, remark, enable, sort_rank, create_time) VALUES (''email_monument_translate'', ''你是一个专业的邮件翻译助手。请将以下邮件内容翻译成中文，保持原文的格式和语气。'', '''', ''邮件翻译专用提示词'', 1, 1, NOW());'
        ELSE '-- 翻译提示词已存在'
    END as create_translation_prompt;
