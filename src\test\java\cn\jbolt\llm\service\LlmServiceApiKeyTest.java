package cn.jbolt.llm.service;

import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmApiKey;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * LLM服务API密钥测试
 */
public class LlmServiceApiKeyTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查API密钥配置
     */
    @Test
    public void testApiKeyConfiguration() {
        System.out.println("=== 测试1：检查API密钥配置 ===");
        
        try {
            // 查询gemini提供商
            LlmProvider geminiProvider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1");
            
            if (geminiProvider == null) {
                System.out.println("✗ 未找到启用的gemini提供商");
                return;
            }
            
            System.out.println("✓ 找到gemini提供商: " + geminiProvider.getName());
            System.out.println("  ID: " + geminiProvider.getId());
            System.out.println("  API类型: " + geminiProvider.getApiType());
            System.out.println("  API地址: " + geminiProvider.getApiBaseUrl());
            
            // 查询API密钥
            List<LlmApiKey> apiKeys = new LlmApiKey().dao()
                .find("SELECT * FROM llm_api_key WHERE provider_id = ? ORDER BY id", geminiProvider.getId());
            
            System.out.println("\nAPI密钥配置 (" + apiKeys.size() + " 个):");
            
            if (apiKeys.isEmpty()) {
                System.out.println("✗ 未找到任何API密钥");
                System.out.println("请添加gemini的API密钥:");
                System.out.println("INSERT INTO llm_api_key (provider_id, api_key, status, rate_limit_per_minute, create_time)");
                System.out.println("VALUES (" + geminiProvider.getId() + ", 'YOUR_GEMINI_API_KEY_HERE', 1, 60, NOW());");
            } else {
                for (LlmApiKey apiKey : apiKeys) {
                    String keyPreview = apiKey.getApiKey() != null ? 
                        apiKey.getApiKey().substring(0, Math.min(10, apiKey.getApiKey().length())) + "..." : "null";
                    System.out.println("  " + (apiKey.getStatus() == 1 ? "✓" : "✗") + " ID: " + apiKey.getId() + 
                                     ", 密钥: " + keyPreview + 
                                     ", 状态: " + (apiKey.getStatus() == 1 ? "启用" : "禁用") +
                                     ", 限制: " + apiKey.getRateLimitPerMinute() + "/分钟");
                }
                
                // 检查是否有启用的API密钥
                long enabledCount = apiKeys.stream().filter(key -> key.getStatus() == 1).count();
                if (enabledCount == 0) {
                    System.out.println("⚠ 所有API密钥都被禁用，请启用至少一个API密钥");
                } else {
                    System.out.println("✓ 找到 " + enabledCount + " 个启用的API密钥");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查API密钥配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：检查模型配置
     */
    @Test
    public void testModelConfiguration() {
        System.out.println("=== 测试2：检查模型配置 ===");
        
        try {
            // 查询gemini提供商
            LlmProvider geminiProvider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1");
            
            if (geminiProvider == null) {
                System.out.println("✗ 未找到启用的gemini提供商");
                return;
            }
            
            // 查询gemini-2.5-pro模型
            LlmModel gemini25Pro = new LlmModel().dao()
                .findFirst("SELECT * FROM llm_model WHERE provider_id = ? AND model_identifier = ? AND status = 1", 
                          geminiProvider.getId(), "gemini-2.5-pro");
            
            if (gemini25Pro == null) {
                System.out.println("✗ 未找到启用的gemini-2.5-pro模型");
                
                // 查看所有gemini模型
                List<LlmModel> allGeminiModels = new LlmModel().dao()
                    .find("SELECT * FROM llm_model WHERE provider_id = ? ORDER BY id", geminiProvider.getId());
                
                System.out.println("gemini提供商的所有模型 (" + allGeminiModels.size() + " 个):");
                for (LlmModel model : allGeminiModels) {
                    System.out.println("  " + (model.getStatus() == 1 ? "✓" : "✗") + " " + 
                                     model.getModelIdentifier() + " - " + model.getModelName() +
                                     " (状态: " + (model.getStatus() == 1 ? "启用" : "禁用") + ")");
                }
                
                System.out.println("请添加或启用gemini-2.5-pro模型:");
                System.out.println("INSERT INTO llm_model (provider_id, model_identifier, model_name, status, max_tokens, supports_images)");
                System.out.println("VALUES (" + geminiProvider.getId() + ", 'gemini-2.5-pro', 'Gemini 2.5 Pro', 1, 8192, 1);");
            } else {
                System.out.println("✓ 找到gemini-2.5-pro模型:");
                System.out.println("  ID: " + gemini25Pro.getId());
                System.out.println("  模型标识: " + gemini25Pro.getModelIdentifier());
                System.out.println("  模型名称: " + gemini25Pro.getModelName());
                System.out.println("  最大令牌: " + gemini25Pro.getMaxTokens());
                System.out.println("  支持图片: " + (gemini25Pro.getSupportsImages() == 1 ? "是" : "否"));
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查模型配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：模拟LLM调用
     */
    @Test
    public void testLlmCall() {
        System.out.println("=== 测试3：模拟LLM调用 ===");
        
        try {
            String providerName = "gemini";
            String modelName = "gemini-2.5-pro";
            String testPrompt = "请将以下英文翻译成中文：Hello, this is a test message.";
            
            System.out.println("测试参数:");
            System.out.println("  提供商: " + providerName);
            System.out.println("  模型: " + modelName);
            System.out.println("  提示词: " + testPrompt);
            
            // 检查提供商和模型是否存在
            LlmProvider provider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE name = ? AND status = 1", providerName);
            
            if (provider == null) {
                System.out.println("✗ 提供商不存在或未启用: " + providerName);
                return;
            }
            
            LlmModel model = new LlmModel().dao()
                .findFirst("SELECT * FROM llm_model WHERE provider_id = ? AND model_identifier = ? AND status = 1", 
                          provider.getId(), modelName);
            
            if (model == null) {
                System.out.println("✗ 模型不存在或未启用: " + modelName);
                return;
            }
            
            // 检查API密钥
            List<LlmApiKey> enabledKeys = new LlmApiKey().dao()
                .find("SELECT * FROM llm_api_key WHERE provider_id = ? AND status = 1", provider.getId());
            
            if (enabledKeys.isEmpty()) {
                System.out.println("✗ 没有启用的API密钥");
                return;
            }
            
            System.out.println("✓ 配置检查通过，准备调用LLM服务");
            System.out.println("  提供商ID: " + provider.getId());
            System.out.println("  模型ID: " + model.getId());
            System.out.println("  可用API密钥: " + enabledKeys.size() + " 个");
            
            // 实际调用LLM服务（注意：这会产生实际的API调用和费用）
            System.out.println("\n⚠ 注意：以下调用会产生实际的API请求和费用");
            System.out.println("如果要进行实际测试，请取消注释以下代码:");
            System.out.println("/*");
            System.out.println("LlmService llmService = LlmService.me();");
            System.out.println("String result = llmService.callLlm(providerName, modelName, testPrompt);");
            System.out.println("System.out.println(\"翻译结果: \" + result);");
            System.out.println("*/");
            
            // 取消注释以下代码进行实际测试
            /*
            LlmService llmService = LlmService.me();
            long startTime = System.currentTimeMillis();
            String result = llmService.callLlm(providerName, modelName, testPrompt);
            long endTime = System.currentTimeMillis();
            
            System.out.println("翻译结果: " + result);
            System.out.println("响应时间: " + (endTime - startTime) + "ms");
            System.out.println("调用状态: " + (result != null && !result.trim().isEmpty() ? "成功" : "失败"));
            */
            
        } catch (Exception e) {
            System.err.println("✗ 模拟LLM调用失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("LLM服务API密钥测试");
        System.out.println("========================================");
        
        testApiKeyConfiguration();
        testModelConfiguration();
        testLlmCall();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n问题排查清单:");
        System.out.println("1. ✓ 检查gemini提供商是否存在且启用");
        System.out.println("2. ✓ 检查gemini-2.5-pro模型是否存在且启用");
        System.out.println("3. ✓ 检查API密钥是否配置且启用");
        System.out.println("4. ⚠ 检查API密钥是否有效（需要实际调用验证）");
        System.out.println("5. ⚠ 检查网络连接是否正常");
        System.out.println("6. ⚠ 检查API配额是否充足");
        
        System.out.println("\n如果所有配置都正确但仍然失败，可能的原因:");
        System.out.println("- API密钥无效或已过期");
        System.out.println("- API配额不足或被限制");
        System.out.println("- 网络连接问题或防火墙阻止");
        System.out.println("- Gemini API服务暂时不可用");
    }
}
