-- 修复Gemini API密钥配置 - 解决NullPointerException

-- 1. 检查当前配置
SELECT 
    id,
    name,
    api_key,
    api_secret,
    status
FROM llm_provider 
WHERE name = 'gemini';

-- 2. 修复api_secret为NULL的问题
-- 将api_secret设置为空字符串，避免NullPointerException
UPDATE llm_provider 
SET 
    api_key = 'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    api_secret = '',  -- 设置为空字符串，不是NULL
    status = 1,
    rate_limit_per_minute = 15
WHERE name = 'gemini';

-- 3. 如果gemini提供商不存在，创建完整配置
INSERT IGNORE INTO llm_provider (
    name, 
    api_type, 
    api_base_url, 
    api_key,
    api_secret,  -- 明确设置为空字符串
    status, 
    priority, 
    default_model, 
    adapter_class,
    rate_limit_per_minute
) VALUES (
    'gemini', 
    'gemini', 
    'https://generativelanguage.googleapis.com/v1beta/models/', 
    'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    '',  -- 空字符串，不是NULL
    1, 
    1, 
    'gemini-2.5-pro', 
    'cn.jbolt.llm.adapter.GeminiAdapter',
    15
);

-- 4. 验证修复结果
SELECT 
    id,
    name,
    CASE 
        WHEN api_key IS NOT NULL AND api_key != '' THEN CONCAT('✓ 已配置(', LENGTH(api_key) - LENGTH(REPLACE(api_key, ',', '')) + 1, '个密钥)')
        ELSE '✗ 未配置'
    END as api_key_status,
    CASE 
        WHEN api_secret IS NULL THEN '✗ NULL (会导致错误)'
        WHEN api_secret = '' THEN '✓ 空字符串 (正常)'
        ELSE '✓ 有值'
    END as api_secret_status,
    CASE 
        WHEN status = 1 THEN '✓ 启用'
        ELSE '✗ 禁用'
    END as status_info,
    rate_limit_per_minute
FROM llm_provider 
WHERE name = 'gemini';

-- 5. 检查所有提供商的api_secret字段，确保没有NULL值
SELECT 
    name,
    CASE 
        WHEN api_secret IS NULL THEN '需要修复'
        ELSE '正常'
    END as api_secret_check
FROM llm_provider 
WHERE status = 1;

-- 6. 如果有其他提供商的api_secret也是NULL，一并修复
UPDATE llm_provider 
SET api_secret = '' 
WHERE api_secret IS NULL AND status = 1;
