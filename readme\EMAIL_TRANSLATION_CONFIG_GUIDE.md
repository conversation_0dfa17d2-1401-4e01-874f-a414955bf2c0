# 邮件翻译配置化改造指南

## 概述

为了解决EmailTranslationPlugin硬编码翻译提供商的问题，我们实现了配置化的翻译系统，支持：
- 主用和备用翻译提供商自动切换
- 灵活的重试机制
- 可配置的提示词
- 功能开关控制
- 管理界面配置

## 架构设计

### 1. 数据库表结构

**email_translation_config** - 翻译配置表
```sql
- id: 主键
- config_key: 配置键（如 translation.primary.provider）
- config_value: 配置值
- config_type: 配置类型（STRING, INTEGER, BOOLEAN, JSON）
- description: 配置描述
- is_active: 是否启用
- create_time/update_time: 时间戳
```

**email_translation_config_history** - 配置变更历史表
```sql
- config_id: 配置ID
- old_value/new_value: 变更前后的值
- change_reason: 变更原因
- changed_by: 变更人
- change_time: 变更时间
```

### 2. 核心组件

**EmailTranslationConfig** - 配置模型类
- 提供静态方法获取/设置配置
- 支持类型转换（String/Integer/Boolean）
- 记录配置变更历史

**ConfigurableTranslationService** - 配置化翻译服务
- 单例模式，提供统一的翻译接口
- 支持主用/备用提供商自动切换
- 内置重试机制和错误处理
- 支持文本和图片翻译

**EmailTranslationConfigController** - 配置管理控制器
- 提供Web界面管理配置
- 支持批量更新配置
- 提供翻译测试功能
- 配置变更历史查询

## 配置项说明

### 核心配置

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| translation.primary.provider | gemini | 主要翻译提供商 |
| translation.primary.model | gemini-2.0-flash-exp | 主要翻译模型 |
| translation.backup.provider | openai | 备用翻译提供商 |
| translation.backup.model | gpt-3.5-turbo | 备用翻译模型 |

### 重试配置

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| translation.retry.max_attempts | 3 | 最大重试次数 |
| translation.retry.delay_seconds | 5 | 重试延迟秒数 |

### 功能开关

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| translation.enable_image_translation | true | 是否启用图片翻译 |
| translation.enable_batch_processing | true | 是否启用批量处理 |

### 性能配置

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| translation.batch_size | 5 | 批量处理大小 |
| translation.timeout_seconds | 60 | 翻译超时时间 |

### 提示词配置

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| translation.subject_prompt | 请翻译以下邮件标题... | 标题翻译提示词 |
| translation.content_prompt | 请翻译以下邮件内容... | 内容翻译提示词 |
| translation.image_prompt | 请翻译这张图片中的内容... | 图片翻译提示词 |

## 使用方法

### 1. 数据库初始化

执行SQL脚本创建配置表：
```bash
mysql -u username -p database_name < src/main/resources/sql/email_translation_config.sql
```

### 2. 访问配置管理界面

访问：`/admin/email/translation/config`

功能包括：
- 配置主用和备用翻译提供商
- 设置重试参数
- 配置功能开关
- 自定义提示词
- 测试翻译功能
- 查看配置变更历史

### 3. 程序化配置

```java
// 设置主用提供商
EmailTranslationConfig.setConfigValue("translation.primary.provider", "gemini");
EmailTranslationConfig.setConfigValue("translation.primary.model", "gemini-2.0-flash-exp");

// 设置备用提供商
EmailTranslationConfig.setConfigValue("translation.backup.provider", "openai");
EmailTranslationConfig.setConfigValue("translation.backup.model", "gpt-4");

// 获取配置
String primaryProvider = EmailTranslationConfig.getConfigValue("translation.primary.provider");
Integer maxRetries = EmailTranslationConfig.getIntConfigValue("translation.retry.max_attempts", 3);
Boolean imageEnabled = EmailTranslationConfig.getBooleanConfigValue("translation.enable_image_translation", true);
```

### 4. 使用翻译服务

```java
ConfigurableTranslationService service = ConfigurableTranslationService.getInstance();

// 翻译文本
String translatedSubject = service.translateText(subject, TranslationType.SUBJECT);
String translatedContent = service.translateText(content, TranslationType.CONTENT);

// 翻译图片
List<String> imagePaths = Arrays.asList("path/to/image.jpg");
String imageTranslation = service.translateImage(imagePaths);

// 检查功能状态
boolean imageEnabled = service.isImageTranslationEnabled();
boolean batchEnabled = service.isBatchProcessingEnabled();
int batchSize = service.getBatchSize();
```

## 故障转移机制

### 自动切换逻辑

1. **主用提供商优先**：首先尝试使用主用提供商进行翻译
2. **重试机制**：如果失败，按配置的重试次数和延迟进行重试
3. **备用提供商**：主用提供商完全失败后，自动切换到备用提供商
4. **备用重试**：备用提供商也支持重试机制
5. **失败处理**：所有尝试都失败后，记录错误并返回空结果

### 错误处理

- **网络错误**：自动重试，记录详细错误信息
- **API限制**：等待延迟后重试，或切换到备用提供商
- **配置错误**：记录错误，使用默认配置
- **模型不支持**：自动切换到备用提供商

## 监控和维护

### 1. 日志监控

关键日志信息：
```
[INFO] 当前翻译配置: 主用: gemini/gemini-2.0-flash-exp, 备用: openai/gpt-3.5-turbo, 重试: 3次
[INFO] 使用主用提供商 gemini 翻译成功
[WARN] 主用提供商 gemini 翻译失败，尝试备用提供商
[ERROR] 主用和备用提供商都翻译失败
```

### 2. 配置变更追踪

通过配置历史表可以追踪：
- 谁在什么时候修改了配置
- 修改了什么内容
- 修改的原因

### 3. 性能监控

- 翻译成功率统计
- 响应时间监控
- 提供商使用情况
- 故障转移频率

## 最佳实践

### 1. 提供商选择

**主用提供商推荐**：
- Gemini 2.0 Flash Exp：性价比高，多模态支持
- GPT-4o：质量最高，成本较高
- Claude 3.5 Sonnet：平衡性能和成本

**备用提供商推荐**：
- 选择与主用不同的提供商，避免单点故障
- 考虑成本因素，可选择较便宜的模型
- 确保备用提供商支持所需功能

### 2. 重试配置

- **最大重试次数**：建议3-5次，避免过度重试
- **重试延迟**：建议5-10秒，给API服务恢复时间
- **超时时间**：根据内容长度调整，建议60-120秒

### 3. 提示词优化

- **标题翻译**：简洁明确，专注于标题特点
- **内容翻译**：考虑上下文，保持格式
- **图片翻译**：明确要求识别和翻译文字内容

### 4. 功能开关

- **图片翻译**：根据需求和成本考虑是否启用
- **批量处理**：大量邮件时启用，提高效率
- **批量大小**：根据API限制和内存情况调整

## 升级指南

### 从硬编码到配置化

1. **执行数据库脚本**：创建配置表
2. **部署新代码**：包含配置化组件
3. **初始化配置**：设置默认翻译提供商
4. **测试验证**：确保翻译功能正常
5. **监控运行**：观察故障转移是否正常

### 配置迁移

如果之前有自定义配置，需要：
1. 备份现有配置
2. 通过管理界面重新设置
3. 测试验证功能
4. 清理旧配置代码

## 总结

配置化改造带来的优势：
- **灵活性**：可随时调整翻译提供商和参数
- **可靠性**：主备切换机制提高系统稳定性
- **可维护性**：统一的配置管理和变更追踪
- **可扩展性**：易于添加新的翻译提供商和功能
- **用户友好**：Web界面配置，无需修改代码

通过这套配置化系统，邮件翻译功能将更加稳定、灵活和易于维护。
