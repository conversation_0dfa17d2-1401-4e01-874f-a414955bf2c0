-- 邮件翻译配置表
CREATE TABLE IF NOT EXISTS email_translation_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) DEFAULT 'STRING' COMMENT '配置类型：STRING, INTEGER, BOOLEAN, JSON',
    description VARCHAR(500) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_active (is_active)
) COMMENT='邮件翻译配置表';

-- 插入默认配置
INSERT INTO email_translation_config (config_key, config_value, config_type, description, is_active) VALUES
('translation.primary.provider', 'gemini', 'STRING', '主要翻译提供商', TRUE),
('translation.primary.model', 'gemini-2.0-flash-exp', 'STRING', '主要翻译模型', TRUE),
('translation.backup.provider', 'openai', 'STRING', '备用翻译提供商', TRUE),
('translation.backup.model', 'gpt-3.5-turbo', 'STRING', '备用翻译模型', TRUE),
('translation.retry.max_attempts', '3', 'INTEGER', '最大重试次数', TRUE),
('translation.retry.delay_seconds', '5', 'INTEGER', '重试延迟秒数', TRUE),
('translation.enable_image_translation', 'true', 'BOOLEAN', '是否启用图片翻译', TRUE),
('translation.enable_batch_processing', 'true', 'BOOLEAN', '是否启用批量处理', TRUE),
('translation.batch_size', '5', 'INTEGER', '批量处理大小', TRUE),
('translation.timeout_seconds', '60', 'INTEGER', '翻译超时时间（秒）', TRUE),
('translation.subject_prompt', '请翻译以下邮件标题,把非中文的翻译成中文：', 'STRING', '标题翻译提示词', TRUE),
('translation.content_prompt', '请翻译以下邮件内容,把非中文的翻译成中文：', 'STRING', '内容翻译提示词', TRUE),
('translation.image_prompt', '请翻译这张图片中的内容,把非中文的翻译成中文：', 'STRING', '图片翻译提示词', TRUE);

-- 创建配置历史表（可选，用于记录配置变更）
CREATE TABLE IF NOT EXISTS email_translation_config_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_id BIGINT NOT NULL COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    old_value TEXT COMMENT '旧值',
    new_value TEXT COMMENT '新值',
    change_reason VARCHAR(500) COMMENT '变更原因',
    changed_by VARCHAR(100) COMMENT '变更人',
    change_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_config_id (config_id),
    INDEX idx_config_key (config_key),
    INDEX idx_change_time (change_time)
) COMMENT='邮件翻译配置变更历史表';

-- 创建视图，方便查询配置
CREATE OR REPLACE VIEW v_email_translation_config AS
SELECT 
    config_key,
    config_value,
    config_type,
    description,
    is_active,
    CASE 
        WHEN config_type = 'BOOLEAN' THEN 
            CASE WHEN LOWER(config_value) IN ('true', '1', 'yes', 'on') THEN '是' ELSE '否' END
        WHEN config_type = 'INTEGER' THEN CONCAT(config_value, ' ')
        ELSE config_value
    END as display_value,
    create_time,
    update_time
FROM email_translation_config
WHERE is_active = TRUE
ORDER BY 
    CASE config_key
        WHEN 'translation.primary.provider' THEN 1
        WHEN 'translation.primary.model' THEN 2
        WHEN 'translation.backup.provider' THEN 3
        WHEN 'translation.backup.model' THEN 4
        WHEN 'translation.retry.max_attempts' THEN 5
        WHEN 'translation.retry.delay_seconds' THEN 6
        WHEN 'translation.enable_image_translation' THEN 7
        WHEN 'translation.enable_batch_processing' THEN 8
        WHEN 'translation.batch_size' THEN 9
        WHEN 'translation.timeout_seconds' THEN 10
        ELSE 99
    END;

-- 完成提示
SELECT '✅ 邮件翻译配置表创建完成！' as message,
       '现在可以通过管理界面灵活配置翻译参数了。' as description;
