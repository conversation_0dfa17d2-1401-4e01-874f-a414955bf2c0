package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Gemini适配器全面测试
 * 测试各种响应场景、多模态功能和错误处理
 */
public class GeminiComprehensiveTest {
    
    private final GeminiAdapter adapter;
    
    public GeminiComprehensiveTest() {
        this.adapter = new GeminiAdapter();
    }
    
    public static void main(String[] args) {
        GeminiComprehensiveTest test = new GeminiComprehensiveTest();
        
        System.out.println("Gemini适配器全面测试");
        System.out.println("==================\n");
        
        // 1. 测试有实际文本内容的响应
        test.testNormalTextResponse();
        
        // 2. 测试空响应处理
        test.testEmptyResponse();
        
        // 3. 测试安全过滤响应
        test.testSafetyFilteredResponse();
        
        // 4. 测试版权过滤响应
        test.testRecitationFilteredResponse();
        
        // 5. 测试长度限制响应
        test.testMaxTokensResponse();
        
        // 6. 测试多模态图片处理
        test.testImageProcessing();
        
        // 7. 测试多图片处理
        test.testMultipleImagesProcessing();
        
        // 8. 测试视频处理
        test.testVideoProcessing();
        
        // 9. 测试音频处理
        test.testAudioProcessing();
        
        // 10. 测试错误响应
        test.testErrorResponses();
        
        // 11. 测试多轮对话
        test.testMultiTurnConversation();
        
        // 12. 测试系统提示词
        test.testSystemPrompt();
        
        System.out.println("所有测试完成！");
    }
    
    /**
     * 测试1: 正常文本响应
     */
    public void testNormalTextResponse() {
        System.out.println("=== 测试1: 正常文本响应 ===");
        
        String normalResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"你好！我是Gemini，一个由Google开发的大型语言模型。我可以帮助你进行翻译、对话、创作等多种任务。有什么我可以为你做的吗？\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 15,\n" +
            "    \"candidatesTokenCount\": 45,\n" +
            "    \"totalTokenCount\": 60\n" +
            "  },\n" +
            "  \"responseId\": \"normal-response-123\"\n" +
            "}";
        
        String converted = adapter.convertResponse(normalResponse);
        System.out.println("转换结果:");
        System.out.println(formatJson(converted));
        
        // 验证结果
        JSONObject result = JSON.parseObject(converted);
        String content = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
        System.out.println("✓ 文本内容正确提取: " + (content.contains("Gemini") ? "是" : "否"));
        System.out.println("✓ 使用真实响应ID: " + result.getString("id"));
        System.out.println("✓ 使用真实模型版本: " + result.getString("model"));
        System.out.println();
    }
    
    /**
     * 测试2: 空响应处理（你的真实响应）
     */
    public void testEmptyResponse() {
        System.out.println("=== 测试2: 空响应处理 ===");
        
        String emptyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokensDetails\": [{\n" +
            "      \"modality\": \"TEXT\",\n" +
            "      \"tokenCount\": 19\n" +
            "    }],\n" +
            "    \"thoughtsTokenCount\": 214,\n" +
            "    \"totalTokenCount\": 233,\n" +
            "    \"promptTokenCount\": 19\n" +
            "  },\n" +
            "  \"responseId\": \"XjGtaPzjEv2BmtkPsaCSkA4\"\n" +
            "}";
        
        String converted = adapter.convertResponse(emptyResponse);
        System.out.println("转换结果:");
        System.out.println(formatJson(converted));
        
        // 验证结果
        JSONObject result = JSON.parseObject(converted);
        JSONObject usage = result.getJSONObject("usage");
        System.out.println("✓ 正确处理空响应: " + (result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content").contains("响应为空") ? "是" : "否"));
        System.out.println("✓ 正确计算completion_tokens: " + usage.getIntValue("completion_tokens"));
        System.out.println("✓ 包含thoughts_tokens: " + usage.getIntValue("thoughts_tokens"));
        System.out.println();
    }
    
    /**
     * 测试3: 安全过滤响应
     */
    public void testSafetyFilteredResponse() {
        System.out.println("=== 测试3: 安全过滤响应 ===");
        
        String safetyResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"SAFETY\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 10,\n" +
            "    \"totalTokenCount\": 10\n" +
            "  }\n" +
            "}";
        
        String converted = adapter.convertResponse(safetyResponse);
        System.out.println("转换结果:");
        System.out.println(formatJson(converted));
        
        JSONObject result = JSON.parseObject(converted);
        String finishReason = result.getJSONArray("choices").getJSONObject(0).getString("finish_reason");
        String content = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
        System.out.println("✓ 正确映射finish_reason: " + finishReason);
        System.out.println("✓ 正确处理安全过滤: " + (content.contains("安全策略") ? "是" : "否"));
        System.out.println();
    }
    
    /**
     * 测试4: 版权过滤响应
     */
    public void testRecitationFilteredResponse() {
        System.out.println("=== 测试4: 版权过滤响应 ===");
        
        String recitationResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"RECITATION\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\"\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\"\n" +
            "}";
        
        String converted = adapter.convertResponse(recitationResponse);
        System.out.println("转换结果:");
        System.out.println(formatJson(converted));
        
        JSONObject result = JSON.parseObject(converted);
        String content = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
        System.out.println("✓ 正确处理版权过滤: " + (content.contains("版权问题") ? "是" : "否"));
        System.out.println();
    }
    
    /**
     * 测试5: 长度限制响应
     */
    public void testMaxTokensResponse() {
        System.out.println("=== 测试5: 长度限制响应 ===");
        
        String maxTokensResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"MAX_TOKENS\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"这是一个被截断的响应，因为达到了最大token限制...\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-2.5-pro\",\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 20,\n" +
            "    \"candidatesTokenCount\": 4096,\n" +
            "    \"totalTokenCount\": 4116\n" +
            "  }\n" +
            "}";
        
        String converted = adapter.convertResponse(maxTokensResponse);
        System.out.println("转换结果:");
        System.out.println(formatJson(converted));
        
        JSONObject result = JSON.parseObject(converted);
        String finishReason = result.getJSONArray("choices").getJSONObject(0).getString("finish_reason");
        System.out.println("✓ 正确映射MAX_TOKENS: " + finishReason);
        System.out.println();
    }
    
    /**
     * 测试6: 图片处理
     */
    public void testImageProcessing() {
        System.out.println("=== 测试6: 图片处理 ===");
        
        List<String> imagePaths = Arrays.asList("test-image.jpg");
        String prompt = "请分析这张图片中的内容，并将任何英文文字翻译成中文";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("图片处理结果:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        // 构建完整请求
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-pro-vision", messages);
        System.out.println("完整请求:");
        System.out.println(formatJson(request));
        
        // 模拟图片分析响应
        String imageResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"finishReason\": \"STOP\",\n" +
            "    \"index\": 0,\n" +
            "    \"content\": {\n" +
            "      \"role\": \"model\",\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"这张图片显示了一个餐厅菜单。我可以看到以下英文内容：\\n- 'Appetizers' → 开胃菜\\n- 'Main Course' → 主菜\\n- 'Desserts' → 甜点\\n菜单设计简洁优雅，采用黑白配色方案。\"\n" +
            "      }]\n" +
            "    }\n" +
            "  }],\n" +
            "  \"modelVersion\": \"gemini-pro-vision\"\n" +
            "}";
        
        String converted = adapter.convertResponse(imageResponse);
        System.out.println("图片分析响应:");
        System.out.println(formatJson(converted));
        System.out.println();
    }
    
    /**
     * 测试7: 多图片处理
     */
    public void testMultipleImagesProcessing() {
        System.out.println("=== 测试7: 多图片处理 ===");
        
        List<String> imagePaths = Arrays.asList("image1.jpg", "image2.jpg", "image3.jpg");
        String prompt = "请对比分析这三张图片，翻译其中的英文内容";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("多图片处理结果:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        
        // 验证包含多个图片
        String partsJson = JSON.toJSONString(parts);
        int imageCount = partsJson.split("inline_data").length - 1;
        System.out.println("✓ 处理图片数量: " + imageCount);
        System.out.println();
    }
    
    /**
     * 测试8: 视频处理
     */
    public void testVideoProcessing() {
        System.out.println("=== 测试8: 视频处理 ===");
        
        List<String> videoPaths = Arrays.asList("test-video.mp4");
        String prompt = "请分析这个视频内容，翻译其中的英文字幕";
        
        Object parts = adapter.processVideos(videoPaths, prompt);
        System.out.println("视频处理结果:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        System.out.println();
    }
    
    /**
     * 测试9: 音频处理
     */
    public void testAudioProcessing() {
        System.out.println("=== 测试9: 音频处理 ===");
        
        List<String> audioPaths = Arrays.asList("test-audio.mp3");
        String prompt = "请转录这个音频内容，并翻译成中文";
        
        Object parts = adapter.processAudios(audioPaths, prompt);
        System.out.println("音频处理结果:");
        System.out.println(formatJson(JSON.toJSONString(parts)));
        System.out.println();
    }
    
    /**
     * 测试10: 错误响应
     */
    public void testErrorResponses() {
        System.out.println("=== 测试10: 错误响应 ===");
        
        // API Key错误
        String apiKeyError = "{\n" +
            "  \"error\": {\n" +
            "    \"code\": 400,\n" +
            "    \"message\": \"API key not valid. Please pass a valid API key.\",\n" +
            "    \"status\": \"INVALID_ARGUMENT\"\n" +
            "  }\n" +
            "}";
        
        String converted1 = adapter.convertResponse(apiKeyError);
        System.out.println("API Key错误响应:");
        System.out.println(formatJson(converted1));
        
        // 配额超限错误
        String quotaError = "{\n" +
            "  \"error\": {\n" +
            "    \"code\": 429,\n" +
            "    \"message\": \"Quota exceeded for quota metric 'Generate Content API requests per minute'\",\n" +
            "    \"status\": \"RESOURCE_EXHAUSTED\"\n" +
            "  }\n" +
            "}";
        
        String converted2 = adapter.convertResponse(quotaError);
        System.out.println("配额超限错误响应:");
        System.out.println(formatJson(converted2));
        
        // 模型不存在错误
        String modelError = "{\n" +
            "  \"error\": {\n" +
            "    \"code\": 404,\n" +
            "    \"message\": \"Model not found\",\n" +
            "    \"status\": \"NOT_FOUND\"\n" +
            "  }\n" +
            "}";
        
        String converted3 = adapter.convertResponse(modelError);
        System.out.println("模型不存在错误响应:");
        System.out.println(formatJson(converted3));
        System.out.println();
    }
    
    /**
     * 测试11: 多轮对话
     */
    public void testMultiTurnConversation() {
        System.out.println("=== 测试11: 多轮对话 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", "你好"));
        messages.add(Kv.by("role", "assistant").set("content", "你好！很高兴见到你。"));
        messages.add(Kv.by("role", "user").set("content", "请帮我翻译一句英文：How are you?"));
        
        String request = adapter.convertRequest("gemini-pro", messages);
        System.out.println("多轮对话请求:");
        System.out.println(formatJson(request));
        
        // 验证角色映射
        JSONObject requestObj = JSON.parseObject(request);
        JSONArray contents = requestObj.getJSONArray("contents");
        System.out.println("✓ 角色映射正确:");
        for (int i = 0; i < contents.size(); i++) {
            JSONObject content = contents.getJSONObject(i);
            System.out.println("  消息" + (i+1) + ": " + content.getString("role"));
        }
        System.out.println();
    }
    
    /**
     * 测试12: 系统提示词
     */
    public void testSystemPrompt() {
        System.out.println("=== 测试12: 系统提示词 ===");
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "system").set("content", "你是一个专业的翻译助手，请准确翻译用户提供的文本。"));
        messages.add(Kv.by("role", "user").set("content", "请翻译：Good morning!"));
        
        String request = adapter.convertRequest("gemini-pro", messages);
        System.out.println("系统提示词请求:");
        System.out.println(formatJson(request));
        
        // 验证系统消息被转换为user角色
        JSONObject requestObj = JSON.parseObject(request);
        JSONArray contents = requestObj.getJSONArray("contents");
        String firstRole = contents.getJSONObject(0).getString("role");
        System.out.println("✓ 系统消息转换为user角色: " + firstRole);
        System.out.println();
    }
    
    /**
     * 格式化JSON输出
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}