<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件解析功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        .test-input {
            width: 100%;
            height: 150px;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        /* 邮件内容解析样式 */
        .email-section {
            margin-bottom: 15px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }
        .current-message {
            background-color: #ffffff !important;
            border: 1px solid #e9ecef;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .different-sender-quoted {
            background-color: #e3f2fd !important;
            border-left: 4px solid #2196f3 !important;
            padding: 15px;
            margin-top: 20px;
            position: relative;
            box-shadow: 0 1px 3px rgba(33, 150, 243, 0.1);
        }
        .different-sender-quoted::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, #e3f2fd);
            border-radius: 6px 6px 0 0;
        }
        .same-sender-quoted {
            background-color: #f5f5f5 !important;
            border-left: 4px solid #ccc !important;
            padding: 15px;
            margin-top: 15px;
            color: #666 !important;
            font-style: italic;
        }
        .quoted-sender-info {
            font-size: 0.9em !important;
            color: #1976d2 !important;
            font-weight: 500 !important;
            margin-bottom: 10px !important;
            border-bottom: 1px solid #e3f2fd !important;
            padding-bottom: 5px !important;
            display: flex;
            align-items: center;
        }
        .quoted-sender-info i {
            margin-right: 5px;
            font-size: 0.8em;
        }
        .email-content-text {
            line-height: 1.6;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
</head>
<body>
    <div class="container">
        <h1>邮件解析功能测试</h1>
        <p>此页面用于测试邮件内容解析和视觉区分功能。</p>
        
        <div class="test-section">
            <div class="test-title">测试1：纯文本邮件解析</div>
            <textarea class="test-input" id="testInput1" placeholder="输入邮件内容...">这是当前邮件的内容。

-----Original Message-----
From: <EMAIL>
Sent: Monday, January 15, 2024 10:30 AM
To: <EMAIL>
Subject: Re: 项目讨论

这是来自John的原始邮件内容。
我们需要讨论项目的进展情况。

> 这是更早的引用内容
> 来自之前的邮件对话</textarea>
            <button class="btn" onclick="testParsing(1)">解析邮件</button>
            <button class="btn btn-secondary" onclick="clearTest(1)">清空</button>
            <div class="test-output" id="testOutput1"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试2：中文邮件解析</div>
            <textarea class="test-input" id="testInput2" placeholder="输入邮件内容...">感谢您的回复。

-----原始邮件-----
发件人: 张三 <<EMAIL>>
发送时间: 2024年1月15日 上午10:30
收件人: 李四 <<EMAIL>>
主题: 关于项目进展

您好，

关于项目的最新进展，我想和您讨论一下。

在2024年1月14日，王五 <<EMAIL>> 写道：
> 项目目前进展顺利
> 预计下周可以完成第一阶段</textarea>
            <button class="btn" onclick="testParsing(2)">解析邮件</button>
            <button class="btn btn-secondary" onclick="clearTest(2)">清空</button>
            <div class="test-output" id="testOutput2"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">功能控制</div>
            <label>
                <input type="checkbox" id="enableParsing" checked> 启用邮件解析功能
            </label>
            <p><small>取消勾选将使用简单格式化，勾选将使用完整的邮件解析功能。</small></p>
        </div>
    </div>

    <script>
        // 模拟邮件解析函数（简化版本）
        function parseEmailContent(content, currentSender) {
            if (!content) return { current: '', quoted: [] };
            
            const result = { current: '', quoted: [] };
            
            // 简单的分隔符检测
            const separators = [
                /-----Original Message-----/i,
                /-----原始邮件-----/i,
                /From:\s*.+/i,
                /发件人:\s*.+/i
            ];
            
            let firstQuoteIndex = -1;
            for (const separator of separators) {
                const match = content.match(separator);
                if (match) {
                    const index = content.indexOf(match[0]);
                    if (firstQuoteIndex === -1 || index < firstQuoteIndex) {
                        firstQuoteIndex = index;
                    }
                }
            }
            
            if (firstQuoteIndex !== -1) {
                result.current = content.substring(0, firstQuoteIndex).trim();
                const quotedContent = content.substring(firstQuoteIndex).trim();
                
                // 简单的发件人提取
                const senderMatch = quotedContent.match(/(?:From:|发件人:)\s*([^<\n]+)(?:<([^>]+)>)?/i);
                const sender = senderMatch ? (senderMatch[2] || senderMatch[1]) : 'unknown';
                
                result.quoted.push({
                    content: quotedContent,
                    sender: sender,
                    type: 'quoted'
                });
            } else {
                result.current = content;
            }
            
            return result;
        }
        
        function formatEmailSection(content, type, metadata = {}) {
            if (!content) return '';
            
            let escapedContent = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
            
            escapedContent = escapedContent.replace(/\n/g, '<br>');
            
            let className = 'email-section';
            let style = 'font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; line-height: 1.5; margin-bottom: 15px;';
            
            switch (type) {
                case 'current':
                    className += ' current-message';
                    break;
                case 'different-sender':
                    className += ' different-sender-quoted';
                    break;
                case 'quoted':
                    className += ' same-sender-quoted';
                    break;
            }
            
            let senderInfo = '';
            if (metadata.sender && metadata.sender !== 'unknown' && type === 'different-sender') {
                senderInfo = `<div class="quoted-sender-info">
                    <i class="fa fa-reply"></i>来自: ${metadata.sender}
                </div>`;
            }
            
            return `<div class="${className}" style="${style}">
                ${senderInfo}
                <div class="email-content-text">${escapedContent}</div>
            </div>`;
        }
        
        function testParsing(testNum) {
            const input = document.getElementById(`testInput${testNum}`).value;
            const output = document.getElementById(`testOutput${testNum}`);
            const enableParsing = document.getElementById('enableParsing').checked;
            
            if (!enableParsing) {
                // 简单格式化
                const escaped = input
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/\n/g, '<br>');
                output.innerHTML = `<div style="font-family: monospace; line-height: 1.3;">${escaped}</div>`;
                return;
            }
            
            const currentSender = '<EMAIL>';
            const parsed = parseEmailContent(input, currentSender);
            
            let formattedContent = '';
            
            if (parsed.current) {
                formattedContent += formatEmailSection(parsed.current, 'current');
            }
            
            if (parsed.quoted && parsed.quoted.length > 0) {
                for (const quotedSection of parsed.quoted) {
                    const isDifferentSender = quotedSection.sender !== 'unknown' && 
                                            quotedSection.sender !== currentSender;
                    const sectionType = isDifferentSender ? 'different-sender' : 'quoted';
                    formattedContent += formatEmailSection(quotedSection.content, sectionType, quotedSection);
                }
            }
            
            output.innerHTML = formattedContent;
        }
        
        function clearTest(testNum) {
            document.getElementById(`testInput${testNum}`).value = '';
            document.getElementById(`testOutput${testNum}`).innerHTML = '';
        }
    </script>
</body>
</html>
