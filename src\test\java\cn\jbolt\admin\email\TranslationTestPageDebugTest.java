package cn.jbolt.admin.email;

import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * 翻译测试页面调试测试
 */
public class TranslationTestPageDebugTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查提供商配置
     */
    @Test
    public void testProviderConfiguration() {
        System.out.println("=== 测试1：检查提供商配置 ===");
        
        try {
            List<LlmProvider> providers = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority"
            );
            
            System.out.println("启用的提供商数量: " + providers.size());
            
            boolean hasGemini = false;
            for (LlmProvider provider : providers) {
                System.out.println("\n提供商: " + provider.getName());
                System.out.println("  ID: " + provider.getId());
                System.out.println("  API类型: " + provider.getApiType());
                System.out.println("  状态: " + (provider.getStatus() == 1 ? "启用" : "禁用"));
                System.out.println("  优先级: " + provider.getPriority());
                
                if ("gemini".equals(provider.getName())) {
                    hasGemini = true;
                    System.out.println("  ✓ 找到Gemini提供商");
                }
            }
            
            if (!hasGemini) {
                System.out.println("⚠ 未找到Gemini提供商，这可能是问题的原因");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查提供商配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：检查Gemini模型配置
     */
    @Test
    public void testGeminiModelConfiguration() {
        System.out.println("=== 测试2：检查Gemini模型配置 ===");
        
        try {
            // 查找Gemini提供商
            LlmProvider geminiProvider = new LlmProvider().dao().findFirst(
                "SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1"
            );
            
            if (geminiProvider == null) {
                System.out.println("✗ 未找到启用的Gemini提供商");
                return;
            }
            
            System.out.println("✓ 找到Gemini提供商，ID: " + geminiProvider.getId());
            
            // 查找Gemini的模型
            List<LlmModel> models = new LlmModel().dao().find(
                "SELECT * FROM llm_model WHERE provider_id = ? AND status = 1 ORDER BY id",
                geminiProvider.getId()
            );
            
            System.out.println("Gemini模型数量: " + models.size());
            
            boolean hasGemini25Pro = false;
            for (LlmModel model : models) {
                System.out.println("\n模型: " + model.getModelName());
                System.out.println("  标识符: " + model.getModelIdentifier());
                System.out.println("  状态: " + (model.getStatus() == 1 ? "启用" : "禁用"));
                System.out.println("  能力: " + model.getCapabilities());
                
                if ("gemini-2.5-pro".equals(model.getModelIdentifier())) {
                    hasGemini25Pro = true;
                    System.out.println("  ✓ 找到gemini-2.5-pro模型");
                }
            }
            
            if (!hasGemini25Pro) {
                System.out.println("⚠ 未找到gemini-2.5-pro模型，这可能是问题的原因");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查Gemini模型配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：模拟API请求
     */
    @Test
    public void testApiEndpoints() {
        System.out.println("=== 测试3：模拟API请求 ===");
        
        try {
            // 模拟获取提供商列表的请求
            System.out.println("模拟 GET /admin/email/translation/config/providers");
            
            List<LlmProvider> providers = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority"
            );
            
            System.out.println("返回提供商数量: " + providers.size());
            for (LlmProvider provider : providers) {
                System.out.println("  - " + provider.getName());
            }
            
            // 模拟获取Gemini模型列表的请求
            System.out.println("\n模拟 GET /admin/email/translation/config/models?provider=gemini");
            
            LlmProvider geminiProvider = new LlmProvider().dao().findFirst(
                "SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1"
            );
            
            if (geminiProvider != null) {
                List<LlmModel> models = new LlmModel().dao().find(
                    "SELECT * FROM llm_model WHERE provider_id = ? AND status = 1",
                    geminiProvider.getId()
                );
                
                System.out.println("返回Gemini模型数量: " + models.size());
                for (LlmModel model : models) {
                    System.out.println("  - " + model.getModelIdentifier() + " (" + model.getModelName() + ")");
                }
            } else {
                System.out.println("✗ Gemini提供商不存在");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 模拟API请求失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：检查API密钥配置
     */
    @Test
    public void testApiKeyConfiguration() {
        System.out.println("=== 测试4：检查API密钥配置 ===");
        
        try {
            LlmProvider geminiProvider = new LlmProvider().dao().findFirst(
                "SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1"
            );
            
            if (geminiProvider == null) {
                System.out.println("✗ Gemini提供商不存在");
                return;
            }
            
            String apiKey = geminiProvider.getApiKey();
            String apiSecret = geminiProvider.getApiSecret();
            
            System.out.println("Gemini提供商配置:");
            System.out.println("  API密钥: " + (apiKey != null && !apiKey.trim().isEmpty() ? "已配置" : "未配置"));
            System.out.println("  API密钥长度: " + (apiKey != null ? apiKey.length() : 0));
            
            if (apiKey != null && apiKey.contains(",")) {
                String[] keys = apiKey.split(",");
                System.out.println("  多密钥数量: " + keys.length);
            }
            
            System.out.println("  API密钥(可选): " + (apiSecret != null && !apiSecret.trim().isEmpty() ? "已配置" : "未配置"));
            System.out.println("  频率限制: " + geminiProvider.getRateLimitPerMinute() + "/分钟");
            
            // 检查代理配置
            Boolean useProxy = geminiProvider.getBoolean("use_proxy");
            String proxyHost = geminiProvider.getStr("proxy_host");
            Integer proxyPort = geminiProvider.getInt("proxy_port");
            
            System.out.println("  使用代理: " + (useProxy != null && useProxy ? "是" : "否"));
            if (useProxy != null && useProxy) {
                System.out.println("  代理配置: " + proxyHost + ":" + proxyPort);
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查API密钥配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("翻译测试页面调试测试");
        System.out.println("========================================");
        
        testProviderConfiguration();
        testGeminiModelConfiguration();
        testApiEndpoints();
        testApiKeyConfiguration();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n问题排查清单:");
        System.out.println("1. ✓ 检查Gemini提供商是否存在且启用");
        System.out.println("2. ✓ 检查gemini-2.5-pro模型是否存在且启用");
        System.out.println("3. ✓ 检查API密钥是否正确配置");
        System.out.println("4. ✓ 检查代理配置是否正确");
        
        System.out.println("\n修复建议:");
        System.out.println("1. 如果提供商或模型不存在，执行初始化SQL脚本");
        System.out.println("2. 如果API密钥未配置，设置有效的Gemini API密钥");
        System.out.println("3. 如果网络有问题，配置代理设置");
        System.out.println("4. 检查浏览器控制台是否有JavaScript错误");
        System.out.println("5. 确保测试页面正确加载了提供商和模型数据");
    }
}
