package cn.jbolt.common.util;

import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 文件名处理工具类
 * 用于处理文件名长度限制、特殊字符清理等
 */
public class FileNameUtil {
    
    /**
     * 默认最大文件名长度（不包括扩展名）
     */
    public static final int DEFAULT_MAX_FILENAME_LENGTH = 80;
    
    /**
     * 最大基础名称长度（为哈希和扩展名预留空间）
     */
    public static final int DEFAULT_MAX_BASENAME_LENGTH = 60;
    
    /**
     * 非法字符正则表达式
     */
    private static final Pattern ILLEGAL_CHARS = Pattern.compile("[\\\\/:*?\"<>|\\r\\n\\t]");
    
    /**
     * 连续空格正则表达式
     */
    private static final Pattern MULTIPLE_SPACES = Pattern.compile("\\s+");
    
    /**
     * 日期格式化器
     */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss");
    
    /**
     * 清理并缩短文件名
     * 
     * @param originalFileName 原始文件名
     * @return 清理后的文件名
     */
    public static String cleanAndShortenFileName(String originalFileName) {
        return cleanAndShortenFileName(originalFileName, DEFAULT_MAX_FILENAME_LENGTH);
    }
    
    /**
     * 清理并缩短文件名
     * 
     * @param originalFileName 原始文件名
     * @param maxLength 最大长度
     * @return 清理后的文件名
     */
    public static String cleanAndShortenFileName(String originalFileName, int maxLength) {
        if (StringUtils.isEmpty(originalFileName)) {
            return "unnamed_" + System.currentTimeMillis();
        }
        
        // 1. 清理非法字符
        String cleanedName = cleanIllegalChars(originalFileName);
        
        // 2. 如果长度在限制内，直接返回
        if (cleanedName.length() <= maxLength) {
            return cleanedName;
        }
        
        // 3. 缩短文件名
        return shortenFileName(cleanedName, maxLength);
    }
    
    /**
     * 清理文件名中的非法字符
     * 
     * @param fileName 文件名
     * @return 清理后的文件名
     */
    public static String cleanIllegalChars(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return fileName;
        }
        
        // 替换非法字符为下划线
        String cleaned = ILLEGAL_CHARS.matcher(fileName).replaceAll("_");
        
        // 合并多个连续空格为单个空格
        cleaned = MULTIPLE_SPACES.matcher(cleaned).replaceAll(" ");
        
        // 去除首尾空格
        cleaned = cleaned.trim();
        
        // 如果清理后为空，生成默认名称
        if (StringUtils.isEmpty(cleaned)) {
            cleaned = "file_" + System.currentTimeMillis();
        }
        
        return cleaned;
    }
    
    /**
     * 缩短文件名
     * 
     * @param fileName 文件名
     * @param maxLength 最大长度
     * @return 缩短后的文件名
     */
    public static String shortenFileName(String fileName, int maxLength) {
        if (StringUtils.isEmpty(fileName) || fileName.length() <= maxLength) {
            return fileName;
        }
        
        // 分离文件名和扩展名
        String extension = getFileExtension(fileName);
        String baseName = getFileBaseName(fileName);
        
        // 计算基础名称的最大长度
        int maxBaseLength = maxLength - extension.length() - 1; // -1 for the dot
        if (extension.isEmpty()) {
            maxBaseLength = maxLength;
        }
        
        // 如果基础名称长度在限制内
        if (baseName.length() <= maxBaseLength) {
            return fileName;
        }
        
        // 生成哈希值
        String hash = generateShortHash(baseName);
        
        // 计算截取长度（为哈希值预留空间）
        int truncateLength = maxBaseLength - hash.length() - 1; // -1 for underscore
        if (truncateLength < 1) {
            truncateLength = 1;
        }
        
        // 截取基础名称
        String truncatedBase = baseName.length() > truncateLength ? 
            baseName.substring(0, truncateLength) : baseName;
        
        // 构建新文件名
        if (extension.isEmpty()) {
            return truncatedBase + "_" + hash;
        } else {
            return truncatedBase + "_" + hash + "." + extension;
        }
    }
    
    /**
     * 获取文件扩展名（不包括点）
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 获取文件基础名称（不包括扩展名）
     * 
     * @param fileName 文件名
     * @return 基础名称
     */
    public static String getFileBaseName(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return fileName;
        }
        
        return fileName.substring(0, lastDotIndex);
    }
    
    /**
     * 生成短哈希值
     * 
     * @param input 输入字符串
     * @return 8位哈希值
     */
    public static String generateShortHash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes("UTF-8"));
            
            // 转换为16进制字符串并取前8位
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < Math.min(4, hashBytes.length); i++) {
                sb.append(String.format("%02x", hashBytes[i] & 0xff));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            // 如果哈希失败，使用时间戳
            return String.valueOf(System.currentTimeMillis() % 100000000);
        }
    }
    
    /**
     * 生成唯一文件名
     * 
     * @param originalFileName 原始文件名
     * @return 唯一文件名
     */
    public static String generateUniqueFileName(String originalFileName) {
        return generateUniqueFileName(originalFileName, DEFAULT_MAX_FILENAME_LENGTH);
    }
    
    /**
     * 生成唯一文件名
     * 
     * @param originalFileName 原始文件名
     * @param maxLength 最大长度
     * @return 唯一文件名
     */
    public static String generateUniqueFileName(String originalFileName, int maxLength) {
        String extension = getFileExtension(originalFileName);
        String baseName = getFileBaseName(originalFileName);
        
        // 清理基础名称
        baseName = cleanIllegalChars(baseName);
        
        // 添加时间戳
        String timestamp = DATE_FORMAT.format(new Date());
        String uniqueBaseName = baseName + "_" + timestamp;
        
        // 构建完整文件名
        String uniqueFileName = extension.isEmpty() ? 
            uniqueBaseName : uniqueBaseName + "." + extension;
        
        // 如果长度超限，进行缩短
        return cleanAndShortenFileName(uniqueFileName, maxLength);
    }
    
    /**
     * 检查文件名是否过长
     * 
     * @param fileName 文件名
     * @return 是否过长
     */
    public static boolean isFileNameTooLong(String fileName) {
        return isFileNameTooLong(fileName, DEFAULT_MAX_FILENAME_LENGTH);
    }
    
    /**
     * 检查文件名是否过长
     * 
     * @param fileName 文件名
     * @param maxLength 最大长度
     * @return 是否过长
     */
    public static boolean isFileNameTooLong(String fileName, int maxLength) {
        return StringUtils.isNotEmpty(fileName) && fileName.length() > maxLength;
    }
    
    /**
     * 检查文件名是否包含非法字符
     * 
     * @param fileName 文件名
     * @return 是否包含非法字符
     */
    public static boolean hasIllegalChars(String fileName) {
        return StringUtils.isNotEmpty(fileName) && ILLEGAL_CHARS.matcher(fileName).find();
    }
    
    /**
     * 验证文件名是否合法
     * 
     * @param fileName 文件名
     * @return 验证结果
     */
    public static FileNameValidationResult validateFileName(String fileName) {
        return validateFileName(fileName, DEFAULT_MAX_FILENAME_LENGTH);
    }
    
    /**
     * 验证文件名是否合法
     * 
     * @param fileName 文件名
     * @param maxLength 最大长度
     * @return 验证结果
     */
    public static FileNameValidationResult validateFileName(String fileName, int maxLength) {
        FileNameValidationResult result = new FileNameValidationResult();
        result.setOriginalFileName(fileName);
        result.setMaxLength(maxLength);
        
        if (StringUtils.isEmpty(fileName)) {
            result.setValid(false);
            result.addError("文件名不能为空");
            return result;
        }
        
        // 检查长度
        if (fileName.length() > maxLength) {
            result.setValid(false);
            result.addError("文件名过长，最大长度为 " + maxLength + " 字符，当前长度为 " + fileName.length() + " 字符");
        }
        
        // 检查非法字符
        if (hasIllegalChars(fileName)) {
            result.setValid(false);
            result.addError("文件名包含非法字符，不允许使用: \\ / : * ? \" < > | 以及换行符");
        }
        
        // 如果有问题，提供建议的文件名
        if (!result.isValid()) {
            result.setSuggestedFileName(cleanAndShortenFileName(fileName, maxLength));
        }
        
        return result;
    }
    
    /**
     * 文件名验证结果类
     */
    public static class FileNameValidationResult {
        private String originalFileName;
        private String suggestedFileName;
        private boolean valid = true;
        private java.util.List<String> errors = new java.util.ArrayList<>();
        private int maxLength;
        
        public String getOriginalFileName() { return originalFileName; }
        public void setOriginalFileName(String originalFileName) { this.originalFileName = originalFileName; }
        
        public String getSuggestedFileName() { return suggestedFileName; }
        public void setSuggestedFileName(String suggestedFileName) { this.suggestedFileName = suggestedFileName; }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public java.util.List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
        
        public int getMaxLength() { return maxLength; }
        public void setMaxLength(int maxLength) { this.maxLength = maxLength; }
        
        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }
}
