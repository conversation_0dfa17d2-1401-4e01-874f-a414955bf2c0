package cn.jbolt.llm.service;

import cn.jbolt.common.model.AiPrompt;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.llm.adapter.LlmAdapter;
import cn.jbolt.llm.adapter.LlmAdapterFactory;
import cn.jbolt.llm.manager.LlmApiKeyManager;
import cn.jbolt.llm.util.LlmImageUtil;
import cn.jbolt.llm.util.OkHttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;

/**
 * LLM服务
 * 用于调用不同的大模型API
 */
public class LlmService {
    private static final LlmService INSTANCE = new LlmService();

    private LlmService() {
    }

    public static LlmService me() {
        return INSTANCE;
    }

    public String callLlm(String prompt) {
        LlmProvider llmProvider = new LlmProvider().dao()
                .findFirst("select * from llm_provider where status=1 order by priority");
        LlmModel llmModel = new LlmModel().dao()
                .findFirst("select * from llm_model where status=1 and provider_id=? order by id", llmProvider.getId());
        AiPrompt aiPrompt = new AiPrompt().dao()
                .findFirst("select * from ai_prompt where enable='1' and `key`='email_monument_translate' order by id");
        return callLlm(llmProvider, llmModel, aiPrompt.getSystemContent() + "\n" + prompt);
    }

    public String callLlm(String provider, String model, String prompt){
        LlmProvider llmProvider = new LlmProvider().dao()
                .findFirst("select * from llm_provider where status=1 and name=? order by priority", provider);
        LlmModel llmModel = new LlmModel().dao()
                .findFirst("select * from llm_model where status=1 and provider_id=? and model_identifier = ? order by id", llmProvider.getId(), model);
        return callLlm(llmProvider, llmModel, prompt);
    }

    public String callLlm(LlmProvider llmProvider, LlmModel llmModel, String prompt) {
        if (llmProvider == null) {
            LogKit.error("找不到LLM提供商: ");
            return null;
        }
        String model = llmModel.getModelIdentifier();
        // 如果未指定模型，使用默认模型
        if (StringUtils.isEmpty(model)) {
            model = llmProvider.getDefaultModel();
        }

        // 创建消息
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", prompt));

        return callLlmWithMessages(llmProvider, model, messages);
    }

    public String callLlm(LlmProvider llmProvider, String model, String prompt) {
        if (llmProvider == null) {
            LogKit.error("找不到LLM提供商: ");
            return null;
        }
        LlmModel llmModel = new LlmModel().dao().findFirst(
                "select * from llm_model where status=1 and provider_id=? and model_identifier=? order by id",
                llmProvider.getId(), model);

        return callLlm(llmProvider, llmModel, prompt);
    }

    /**
     * 调用LLM API
     *
     * @param providerId 提供商ID
     * @param model      模型标识符
     * @param prompt     提示词
     * @return 响应内容
     */
    public String callLlm(Long providerId, String model, String prompt) {
        LlmProvider provider = new LlmProvider().dao().findById(providerId);
        return callLlm(provider, model, prompt);
    }

    public String callLlmWithImages(LlmProvider llmProvider, String model, String prompt, List<String> imagePaths) {
        if (llmProvider == null) {
            LogKit.error("找不到LLM提供商: ");
            return null;
        }

        // 如果未指定模型，使用默认模型
        if (model == null || model.isEmpty()) {
            model = llmProvider.getDefaultModel();
        }

        String apiBaseUrl = llmProvider.getApiBaseUrl();
        // 创建消息
        List<Kv> messages = Lists.newArrayList();
        // messages.add(Kv.by("role", "system").set("content", ""));

        // 将图片路径添加到参数中
        if (imagePaths != null && !imagePaths.isEmpty()) {
            // 判断是否是Kimi模型
            if (apiBaseUrl != null && apiBaseUrl.contains("moonshot")) {
                // 转换图片为DataURL格式
                List<String> dataUrls = new ArrayList<>();
                for (String imagePath : imagePaths) {
                    String dataUrl = LlmImageUtil.convertToDataUrl(imagePath);
                    if(!StringUtils.isEmpty(dataUrl)) {
                        dataUrls.add(dataUrl);
                    }
                }
                messages.add(Kv.by("role", "user").set("content", LlmImageUtil.buildKimiImageContent(prompt, dataUrls)));
            } else {
                // 非Kimi模型，保持原有逻辑
                messages.add(Kv.by("role", "user").set("content", prompt).set("images", imagePaths));
            }
        } else {
            messages.add(Kv.by("role", "user").set("content", prompt));
        }

        return callLlmWithMessages(llmProvider, model, messages);
    }

    public String callLlmWithImages(String providerName, String model, String prompt, Set<String> imagePaths) {
        if (imagePaths == null || imagePaths.isEmpty()) {
            return callLlm(Long.parseLong(providerName), model, prompt);
        }
        List<String> imageList = Lists.newArrayList();
        imageList.addAll(imagePaths);
        return callLlmWithImages(providerName, model, prompt, imageList);
    }

    public String callLlmWithImages(String providerName, String model, String prompt, List<String> imagePaths) {
        LlmProvider llmProvider = new LlmProvider().dao()
                .findFirst("select * from llm_provider where name=? order by priority", providerName);
        return callLlmWithImages(llmProvider, model, prompt, imagePaths);
    }

    /**
     * 调用LLM API，带图片处理
     *
     * @param providerId 提供商ID
     * @param model      模型标识符
     * @param prompt     提示词
     * @param imagePaths 图片路径列表
     * @return 响应内容
     */
    public String callLlmWithImages(Long providerId, String model, String prompt, List<String> imagePaths) {
        LlmProvider llmProvider = new LlmProvider().dao().findById(providerId);
        return callLlmWithImages(llmProvider, model, prompt, imagePaths);
    }

    /**
     * 调用LLM API，使用Markdown格式的图片引用
     *
     * @param providerId      提供商ID
     * @param model           模型标识符
     * @param markdownContent 包含图片引用的Markdown内容，例如: "![image](path/to/image.jpg)
     *                        请描述这张图片"
     * @return 响应内容
     */
    public String callLlmWithMarkdownImages(Long providerId, String model, String markdownContent) {
        // 获取提供商信息
        LlmProvider provider = new LlmProvider().dao().findById(providerId);
        if (provider == null) {
            LogKit.error("找不到LLM提供商: " + providerId);
            return null;
        }

        // 如果未指定模型，使用默认模型
        if (model == null || model.isEmpty()) {
            model = provider.getDefaultModel();
        }

        // 创建消息
        List<Kv> messages = Lists.newArrayList();
        messages.add(Kv.by("role", "user").set("content", markdownContent));

        return callLlmWithMessages(provider, model, messages);
    }

    /**
     * 调用LLM API（带消息列表）
     *
     * @param provider 提供商信息
     * @param model    模型标识符
     * @param messages 消息列表
     * @return 响应内容
     */
    public String callLlmWithMessages(LlmProvider provider, String model, List<Kv> messages) {
        return callLlmWithMessagesAndRetry(provider, model, messages, 3);
    }

    /**
     * 调用LLM API（带消息列表和重试机制）
     *
     * @param provider 提供商信息
     * @param model    模型标识符
     * @param messages 消息列表
     * @param maxRetries 最大重试次数
     * @return 响应内容
     */
    private String callLlmWithMessagesAndRetry(LlmProvider provider, String model, List<Kv> messages, int maxRetries) {
        String providerName = provider.getName();
        int attempts = 0;

        while (attempts < maxRetries) {
            attempts++;

            // 获取可用的API密钥
            LlmApiKeyManager.ApiKeyInfo keyInfo = LlmApiKeyManager.me().getAvailableApiKey(providerName);
            if (keyInfo == null) {
                LogKit.warn("提供商 " + providerName + " 没有可用的API密钥，尝试次数: " + attempts);
                if (attempts < maxRetries) {
                    // 等待一段时间后重试
                    try {
                        Thread.sleep(1000 * attempts); // 递增等待时间
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    continue;
                } else {
                    LogKit.error("提供商 " + providerName + " 所有API密钥都不可用");
                    return null;
                }
            }

            try {
                String result = callLlmWithSpecificKey(provider, model, messages, keyInfo);
                if (result != null) {
                    return result;
                }
            } catch (Exception e) {
                LogKit.warn("使用API密钥调用失败，尝试次数: " + attempts + ", 错误: " + e.getMessage());

                // 如果是频率限制错误，标记该密钥暂时不可用
                if (isRateLimitError(e)) {
                    LlmApiKeyManager.me().markApiKeyError(keyInfo.getApiKey(), 60 * 1000); // 阻塞1分钟
                }

                if (attempts >= maxRetries) {
                    LogKit.error("调用LLM API失败，已达到最大重试次数", e);
                    throw new RuntimeException("调用LLM API失败: " + e.getMessage(), e);
                }
            }
        }

        return null;
    }

    /**
     * 使用特定API密钥调用LLM API
     */
    private String callLlmWithSpecificKey(LlmProvider provider, String model, List<Kv> messages,
                                         LlmApiKeyManager.ApiKeyInfo keyInfo) throws Exception {
        String apiType = provider.getApiType();
        String apiBaseUrl = provider.getApiBaseUrl();
        String apiKey = keyInfo.getApiKey();
        String apiSecret = keyInfo.getApiSecret();
        int requestTimeout = provider.getRequestTimeout() != null ? provider.getRequestTimeout() : 30;

        // 准备请求体和请求头
        String requestBody;
        Map<String, String> headers = new HashMap<>();

        if ("openai".equalsIgnoreCase(apiType)) {
            // OpenAI兼容API
            JSONObject requestJson = new JSONObject();
            requestJson.put("model", model);
            requestJson.put("messages", messages);

            requestBody = requestJson.toJSONString();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + apiKey);
        } else {
            // 非OpenAI兼容API，需要使用适配器
            String adapterClassName = provider.getAdapterClass();
            LlmAdapter adapter = LlmAdapterFactory.createAdapter(adapterClassName);

            if (adapter == null) {
                LogKit.error("无法创建适配器: " + adapterClassName);
                return null;
            }

            // 使用适配器转换请求
            requestBody = adapter.convertRequest(model, messages);
            headers = adapter.getHeaders(apiKey, apiSecret);
        }

        // 发送请求（超时时间现在在ProxyConfigUtil中统一配置）

        // 添加调试日志
        LogKit.info("LLM API请求URL: " + apiBaseUrl);
        LogKit.info("LLM API请求头: " + JSON.toJSONString(headers));
        LogKit.info("LLM API请求体: " + requestBody);

        // 处理URL路径
        String fullUrl = apiBaseUrl;

        if ("openai".equalsIgnoreCase(apiType) && !fullUrl.endsWith("/chat/completions")
                && !fullUrl.contains("?")) {
            if (fullUrl.endsWith("/")) {
                if (!fullUrl.contains("v1")) {
                    fullUrl += "v1/";
                }
            } else {
                if (!fullUrl.contains("v1")) {
                    fullUrl += "/v1/";
                } else {
                    fullUrl += "/";
                }
            }
            fullUrl += "chat/completions";
        }

        LogKit.info("LLM API最终请求URL: " + fullUrl);

        // 使用OkHttp发送请求
        String response = OkHttpUtil.post(fullUrl, requestBody, headers);
        return processResponse(apiType, provider, response);
    }

    /**
     * 判断是否是频率限制错误
     */
    private boolean isRateLimitError(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        message = message.toLowerCase();
        return message.contains("rate limit") ||
               message.contains("too many requests") ||
               message.contains("429") ||
               message.contains("quota exceeded");
    }

    /**
     * 获取异常的堆栈跟踪信息
     *
     * @param e 异常
     * @return 堆栈跟踪信息字符串
     */
    private String getStackTraceAsString(Exception e) {
        StringBuilder sb = new StringBuilder();
        sb.append(e.toString()).append("\n");
        for (StackTraceElement element : e.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }
        return sb.toString();
    }

    /**
     * 从OpenAI格式的响应中提取内容
     *
     * @param response OpenAI格式的响应
     * @return 提取的内容
     */
    private String extractContentFromOpenAIResponse(String response) {
        // 添加调试日志
        LogKit.info("LLM API原始响应: " + response);

        // 检查响应是否为空
        if (response == null || response.isEmpty()) {
            LogKit.warn("LLM API响应为空");
            return "[警告] 响应为空";
        }

        // 尝试判断响应是否为JSON格式
        boolean isJson;
        try {
            // 检查响应是否以{ 或 [开头，这是JSON的特征
            String trimmed = response.trim();
            isJson = (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                    (trimmed.startsWith("[") && trimmed.endsWith("]"));
        } catch (Exception e) {
            // 忽略异常，默认不是JSON
            isJson = false;
        }

        // 如果不是JSON格式，直接返回原始响应
        if (!isJson) {
            LogKit.info("LLM API响应不是JSON格式，直接返回原始响应");
            return response;
        }

        // 如果是JSON格式，尝试解析
        try {
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("error")) {
                JSONObject error = jsonResponse.getJSONObject("error");
                String errorMsg = error.getString("message");
                LogKit.error("LLM API返回错误: " + errorMsg);
                return "[错误] " + errorMsg;
            }

            // 提取内容
            if (jsonResponse.containsKey("choices")) {
                JSONArray choices = jsonResponse.getJSONArray("choices");
                if (choices != null && !choices.isEmpty()) {
                    JSONObject choice = choices.getJSONObject(0);
                    if (choice.containsKey("message")) {
                        JSONObject message = choice.getJSONObject("message");
                        if (message.containsKey("content")) {
                            return message.getString("content");
                        } else {
                            LogKit.warn("LLM API响应中缺少content字段");
                            return "[警告] 响应中缺少内容";
                        }
                    } else {
                        LogKit.warn("LLM API响应中缺少message字段");
                        return "[警告] 响应中缺少消息";
                    }
                } else {
                    LogKit.warn("LLM API响应中的choices数组为空");
                    return "[警告] 响应中的选项为空";
                }
            } else {
                LogKit.warn("LLM API响应中缺少choices字段");
                return "[警告] 响应中缺少选项";
            }
        } catch (Exception e) {
            LogKit.error("解析LLM响应失败: " + e.getMessage(), e);
            // 如果解析失败，返回原始响应
            LogKit.info("解析失败，返回原始响应");
            return response;
        }
    }

    /**
     * 处理API响应
     *
     * @param apiType  API类型
     * @param provider 提供商信息
     * @param response API响应
     * @return 处理后的响应
     */
    private String processResponse(String apiType, LlmProvider provider, String response) {
        // 添加调试日志
        LogKit.info("LLM API原始响应: " + response);

        if ("openai".equalsIgnoreCase(apiType)) {
            // OpenAI兼容API，直接返回响应
            return extractContentFromOpenAIResponse(response);
        } else {
            // 非OpenAI兼容API，使用适配器转换响应
            String adapterClassName = provider.getAdapterClass();
            LlmAdapter adapter = LlmAdapterFactory.createAdapter(adapterClassName);

            if (adapter == null) {
                LogKit.error("无法创建适配器: " + adapterClassName);
                return null;
            }

            // 使用适配器转换响应
            String convertedResponse = adapter.convertResponse(response);
            return extractContentFromOpenAIResponse(convertedResponse);
        }
    }

    /**
     * 翻译内容
     *
     * @param providerId   提供商ID
     * @param content      要翻译的内容
     * @param customPrompt 自定义提示词
     * @return 翻译结果
     */
    public String translate(Long providerId, String content, String customPrompt) {
        String emailTranslatePrompt = Db.queryStr(
                "select ifnull(user_content, ifnull(system_content, '')) from ai_prompt where `key`='email_translate' limit 1");
        String prompt = customPrompt != null && !customPrompt.isEmpty()
                ? emailTranslatePrompt + "。" + customPrompt + "\n\n" + content
                : emailTranslatePrompt + "\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * 提取要点
     *
     * @param providerId 提供商ID
     * @param content    要提取要点的内容
     * @return 提取的要点
     */
    public String extractKeyPoints(Long providerId, String content) {
        String keyPointsPrompt = Db.queryStr(
                "select ifnull(user_content, ifnull(system_content, '')) from ai_prompt where `key`='key_points' limit 1");
        String prompt = keyPointsPrompt + "\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * 生成回复
     *
     * @param providerId 提供商ID
     * @param content    要回复的内容
     * @return 生成的回复
     */
    public String generateReply(Long providerId, String content) {
        String replyPrompt = Db.queryStr(
                "select ifnull(user_content, ifnull(system_content, '')) from ai_prompt where `key`='reply' limit 1");
        String prompt = replyPrompt + "\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * AI写作
     *
     * @param providerId   提供商ID
     * @param content      内容
     * @param customPrompt 自定义提示词
     * @return 写作结果
     */
    public String write(Long providerId, String content, String customPrompt) {
        String prompt = customPrompt != null && !customPrompt.isEmpty()
                ? customPrompt + "\n\n" + content
                : "请根据以下内容重新写一篇更专业的版本：\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * AI改写内容
     *
     * @param providerId   提供商ID
     * @param content      要改写的内容
     * @param customPrompt 自定义提示词
     * @return 改写结果
     */
    public String rewrite(Long providerId, String content, String customPrompt) {
        String prompt = customPrompt != null && !customPrompt.isEmpty()
                ? customPrompt + "\n\n" + content
                : "请对以下内容进行改写，保持原意的同时使表达更加专业、得体：\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * AI续写内容
     *
     * @param providerId   提供商ID
     * @param content      要续写的内容
     * @param customPrompt 自定义提示词
     * @return 续写结果
     */
    public String continueWrite(Long providerId, String content, String customPrompt) {
        String prompt = customPrompt != null && !customPrompt.isEmpty()
                ? customPrompt + "\n\n" + content
                : "请基于以下内容继续写下去，保持语气和风格的一致性：\n\n" + content;

        return callLlm(providerId, null, prompt);
    }

    /**
     * 使用指定模型翻译文本
     *
     * @param text 要翻译的文本
     * @param aiPrompt AI提示词
     * @param providerId 提供商ID
     * @param modelId 模型ID
     * @return 翻译结果
     */
    public String translateWithModel(String text, AiPrompt aiPrompt, Long providerId, Long modelId) {
        try {
            // 获取提供商和模型信息
            LlmProvider provider = new LlmProvider().dao().findById(providerId);
            LlmModel model = new LlmModel().dao().findById(modelId);

            if (provider == null || model == null) {
                LogKit.error("提供商或模型不存在: providerId={}, modelId={}", providerId, modelId);
                return null;
            }

            // 构建提示词
            String prompt = buildTranslationPrompt(aiPrompt, text);

            // 调用LLM
            return callLlm(provider, model.getModelIdentifier(), prompt);
        } catch (Exception e) {
            LogKit.error("使用指定模型翻译文本失败", e);
            return null;
        }
    }

    /**
     * 使用指定模型翻译图片
     *
     * @param imagePath 图片路径
     * @param aiPrompt AI提示词
     * @param providerId 提供商ID
     * @param modelId 模型ID
     * @return 翻译结果
     */
    public String translateImageWithModel(String imagePath, AiPrompt aiPrompt, Long providerId, Long modelId) {
        try {
            // 获取提供商和模型信息
            LlmProvider provider = new LlmProvider().dao().findById(providerId);
            LlmModel model = new LlmModel().dao().findById(modelId);

            if (provider == null || model == null) {
                LogKit.error("提供商或模型不存在: providerId={}, modelId={}", providerId, modelId);
                return null;
            }

            // 构建提示词
            String prompt = buildImageTranslationPrompt(aiPrompt);

            // 调用LLM处理图片
            List<String> imagePaths = new ArrayList<>();
            imagePaths.add(imagePath);
            return callLlmWithImages(provider, model.getModelIdentifier(), prompt, imagePaths);
        } catch (Exception e) {
            LogKit.error("使用指定模型翻译图片失败", e);
            return null;
        }
    }

    /**
     * 构建翻译提示词
     */
    private String buildTranslationPrompt(AiPrompt aiPrompt, String text) {
        StringBuilder prompt = new StringBuilder();

        if (aiPrompt.getSystemContent() != null && !aiPrompt.getSystemContent().trim().isEmpty()) {
            prompt.append(aiPrompt.getSystemContent()).append("\n\n");
        }

        if (aiPrompt.getUserContent() != null && !aiPrompt.getUserContent().trim().isEmpty()) {
            prompt.append(aiPrompt.getUserContent()).append("\n\n");
        }

        prompt.append("要翻译的内容：\n").append(text);

        return prompt.toString();
    }

    /**
     * 构建图片翻译提示词
     */
    private String buildImageTranslationPrompt(AiPrompt aiPrompt) {
        StringBuilder prompt = new StringBuilder();

        if (aiPrompt.getSystemContent() != null && !aiPrompt.getSystemContent().trim().isEmpty()) {
            prompt.append(aiPrompt.getSystemContent()).append("\n\n");
        }

        if (aiPrompt.getUserContent() != null && !aiPrompt.getUserContent().trim().isEmpty()) {
            prompt.append(aiPrompt.getUserContent());
        } else {
            prompt.append("请翻译图片中的文字内容");
        }

        return prompt.toString();
    }
}
