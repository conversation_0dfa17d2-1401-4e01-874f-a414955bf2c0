package cn.jbolt.mail.test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * Simple file type detection test without dependencies
 */
public class SimpleFileTypeTest {
    
    public static void main(String[] args) {
        testFileTypeDetection();
    }
    
    /**
     * Test file type detection logic
     */
    private static void testFileTypeDetection() {
        System.out.println("=== Simple File Type Detection Test ===\n");
        
        // Test PDF detection
        testPdfDetection();
        
        // Test ZIP detection
        testZipDetection();
        
        // Test PNG detection
        testPngDetection();
        
        // Test JPEG detection
        testJpegDetection();
        
        System.out.println("=== Test Complete ===");
    }
    
    private static void testPdfDetection() {
        System.out.println("1. Test PDF detection:");
        
        // PDF header
        String pdfHeader = "%PDF-1.5\n%¡³Å×\n6 0 obj\n<</Nums[ 0 7 0 R ]>>";
        byte[] pdfBytes = pdfHeader.getBytes(StandardCharsets.ISO_8859_1);
        
        String result = detectFileTypeFromHeader(pdfBytes, pdfBytes.length);
        System.out.println("   PDF header detection result: " + result);
        System.out.println("   Expected: .pdf");
        System.out.println("   Detection " + (".pdf".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    private static void testZipDetection() {
        System.out.println("2. Test ZIP detection:");
        
        // ZIP header (PK)
        byte[] zipBytes = {0x50, 0x4B, 0x03, 0x04, 0x14, 0x00, 0x00, 0x00};
        
        String result = detectFileTypeFromHeader(zipBytes, zipBytes.length);
        System.out.println("   ZIP header detection result: " + result);
        System.out.println("   Expected: .zip");
        System.out.println("   Detection " + (".zip".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    private static void testPngDetection() {
        System.out.println("3. Test PNG detection:");
        
        // PNG header
        byte[] pngBytes = {(byte)0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A};
        
        String result = detectFileTypeFromHeader(pngBytes, pngBytes.length);
        System.out.println("   PNG header detection result: " + result);
        System.out.println("   Expected: .png");
        System.out.println("   Detection " + (".png".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    private static void testJpegDetection() {
        System.out.println("4. Test JPEG detection:");
        
        // JPEG header
        byte[] jpegBytes = {(byte)0xFF, (byte)0xD8, (byte)0xFF, (byte)0xE0, 0x00, 0x10, 0x4A, 0x46};
        
        String result = detectFileTypeFromHeader(jpegBytes, jpegBytes.length);
        System.out.println("   JPEG header detection result: " + result);
        System.out.println("   Expected: .jpg");
        System.out.println("   Detection " + (".jpg".equals(result) ? "SUCCESS" : "FAILED") + "\n");
    }
    
    /**
     * Copy of the detection logic from EmailParserJakarta
     */
    private static String detectFileTypeFromHeader(byte[] header, int length) {
        if (length < 4) {
            return null;
        }
        
        // Convert to string for some detections
        String headerStr = new String(header, 0, Math.min(length, 50), StandardCharsets.ISO_8859_1);
        
        // PDF file detection
        if (headerStr.startsWith("%PDF-")) {
            return ".pdf";
        }
        
        // ZIP file detection (PK)
        if (header[0] == 0x50 && header[1] == 0x4B) {
            return ".zip";
        }
        
        // PNG file detection
        if (length >= 8 && header[0] == (byte)0x89 && header[1] == 0x50 && 
            header[2] == 0x4E && header[3] == 0x47) {
            return ".png";
        }
        
        // JPEG file detection
        if (length >= 3 && header[0] == (byte)0xFF && header[1] == (byte)0xD8 && header[2] == (byte)0xFF) {
            return ".jpg";
        }
        
        // GIF file detection
        if (headerStr.startsWith("GIF8")) {
            return ".gif";
        }
        
        // Microsoft Office document detection (compound document format)
        if (length >= 8 && header[0] == (byte)0xD0 && header[1] == (byte)0xCF && 
            header[2] == 0x11 && header[3] == (byte)0xE0) {
            return ".doc"; // Could be .doc, .xls, .ppt etc, default to .doc
        }
        
        // Office 2007+ document detection (actually ZIP format)
        if (header[0] == 0x50 && header[1] == 0x4B && length > 30) {
            // Further detect if it's an Office document
            String content = new String(header, 0, Math.min(length, 200), StandardCharsets.ISO_8859_1);
            if (content.contains("word/") || content.contains("xl/") || content.contains("ppt/")) {
                if (content.contains("word/")) return ".docx";
                if (content.contains("xl/")) return ".xlsx";
                if (content.contains("ppt/")) return ".pptx";
            }
            return ".zip"; // Default to ZIP
        }
        
        // BMP file detection
        if (length >= 2 && header[0] == 0x42 && header[1] == 0x4D) {
            return ".bmp";
        }
        
        // TIFF file detection
        if (length >= 4 && ((header[0] == 0x49 && header[1] == 0x49 && header[2] == 0x2A && header[3] == 0x00) ||
                           (header[0] == 0x4D && header[1] == 0x4D && header[2] == 0x00 && header[3] == 0x2A))) {
            return ".tiff";
        }
        
        // Unrecognized file type
        return null;
    }
}
