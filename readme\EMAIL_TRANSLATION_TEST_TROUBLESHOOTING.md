# 邮件翻译测试故障排除指南

## 问题现象

翻译测试返回结果：
```
提供商: gemini
模型: gemini-2.5-pro
提示词: 使用AI提示词
翻译结果: undefined
响应时间: 3008ms
状态: 失败
```

## 可能原因分析

### 1. 数据库配置问题

#### 1.1 LLM提供商配置缺失
- 数据库中没有名为"gemini"的提供商记录
- 提供商状态不是1（未启用）

#### 1.2 LLM模型配置缺失
- 没有model_identifier为"gemini-2.5-pro"的模型记录
- 模型状态不是1（未启用）
- 模型与提供商的关联关系错误

#### 1.3 AI提示词配置缺失
- 没有key为"email_monument_translate"的提示词
- 提示词未启用（enable != 1）

### 2. API配置问题

#### 2.1 API密钥问题
- 没有配置有效的API密钥
- API密钥已过期或被禁用
- API密钥配额不足

#### 2.2 网络连接问题
- 无法访问Gemini API服务器
- 防火墙阻止了API请求
- 代理配置问题

## 排查步骤

### 步骤1：运行调试测试

```java
// 运行数据库配置检查
EmailTranslationConfigDebugTest test = new EmailTranslationConfigDebugTest();
test.runAllTests();
```

### 步骤2：检查数据库配置

#### 检查LLM提供商表
```sql
-- 查看所有提供商
SELECT * FROM llm_provider ORDER BY priority;

-- 查看gemini提供商
SELECT * FROM llm_provider WHERE name = 'gemini';

-- 检查启用状态
SELECT * FROM llm_provider WHERE name = 'gemini' AND status = 1;
```

#### 检查LLM模型表
```sql
-- 查看所有模型
SELECT * FROM llm_model ORDER BY provider_id, id;

-- 查看gemini提供商的模型
SELECT m.*, p.name as provider_name 
FROM llm_model m 
JOIN llm_provider p ON m.provider_id = p.id 
WHERE p.name = 'gemini';

-- 检查gemini-2.5-pro模型
SELECT m.*, p.name as provider_name 
FROM llm_model m 
JOIN llm_provider p ON m.provider_id = p.id 
WHERE p.name = 'gemini' AND m.model_identifier = 'gemini-2.5-pro';
```

#### 检查AI提示词表
```sql
-- 查看翻译提示词
SELECT * FROM ai_prompt WHERE `key` = 'email_monument_translate';

-- 检查启用状态
SELECT * FROM ai_prompt WHERE `key` = 'email_monument_translate' AND enable = 1;
```

### 步骤3：修复配置问题

#### 3.1 添加Gemini提供商（如果不存在）
```sql
INSERT INTO llm_provider (name, api_type, api_base_url, status, priority, default_model, adapter_class) 
VALUES ('gemini', 'gemini', 'https://generativelanguage.googleapis.com/v1beta/models/', 1, 1, 'gemini-2.5-pro', 'cn.jbolt.llm.adapter.GeminiAdapter');
```

#### 3.2 添加Gemini模型（如果不存在）
```sql
-- 首先获取gemini提供商的ID
SET @provider_id = (SELECT id FROM llm_provider WHERE name = 'gemini');

-- 添加gemini-2.5-pro模型
INSERT INTO llm_model (provider_id, model_identifier, model_name, status, max_tokens, supports_images) 
VALUES (@provider_id, 'gemini-2.5-pro', 'Gemini 2.5 Pro', 1, 8192, 1);

-- 添加其他常用模型
INSERT INTO llm_model (provider_id, model_identifier, model_name, status, max_tokens, supports_images) 
VALUES (@provider_id, 'gemini-2.0-flash-exp', 'Gemini 2.0 Flash Experimental', 1, 8192, 1);
```

#### 3.3 添加翻译提示词（如果不存在）
```sql
INSERT INTO ai_prompt (`key`, system_content, user_content, remark, enable, sort_rank, create_time) 
VALUES (
    'email_monument_translate', 
    '你是一个专业的邮件翻译助手。请将以下邮件内容翻译成中文，保持原文的格式和语气。对于专业术语，请使用准确的中文表达。如果遇到人名、地名或公司名，请保留原文并在后面用括号标注中文。', 
    '', 
    '邮件翻译专用提示词', 
    1, 
    1, 
    NOW()
);
```

### 步骤4：检查API密钥配置

#### 4.1 查看API密钥表
```sql
-- 查看gemini的API密钥
SELECT ak.*, p.name as provider_name 
FROM llm_api_key ak 
JOIN llm_provider p ON ak.provider_id = p.id 
WHERE p.name = 'gemini';
```

#### 4.2 添加API密钥（如果不存在）
```sql
-- 获取gemini提供商ID
SET @provider_id = (SELECT id FROM llm_provider WHERE name = 'gemini');

-- 添加API密钥（请替换为实际的API密钥）
INSERT INTO llm_api_key (provider_id, api_key, status, rate_limit_per_minute, create_time) 
VALUES (@provider_id, 'YOUR_GEMINI_API_KEY_HERE', 1, 60, NOW());
```

### 步骤5：测试修复结果

#### 5.1 重新运行调试测试
```java
EmailTranslationConfigDebugTest test = new EmailTranslationConfigDebugTest();
test.testTranslationCall();
```

#### 5.2 在配置界面测试
1. 访问 `/admin/email/translation/config`
2. 选择"gemini"提供商
3. 选择"gemini-2.5-pro"模型
4. 输入测试文本
5. 点击"运行测试"

## 常见错误及解决方案

### 错误1：找不到提供商
```
找不到提供商: gemini，请检查提供商配置
```

**解决方案**：
1. 检查llm_provider表中是否存在name='gemini'的记录
2. 检查status字段是否为1
3. 如果不存在，执行步骤3.1的SQL语句

### 错误2：找不到模型
```
找不到模型: gemini-2.5-pro，提供商: gemini，请检查模型配置
```

**解决方案**：
1. 检查llm_model表中是否存在对应的模型记录
2. 检查provider_id是否正确关联到gemini提供商
3. 检查status字段是否为1
4. 如果不存在，执行步骤3.2的SQL语句

### 错误3：API密钥问题
```
提供商 gemini 没有可用的API密钥
```

**解决方案**：
1. 检查llm_api_key表中是否有gemini的API密钥
2. 检查API密钥是否有效
3. 检查API密钥的状态是否为1
4. 如果不存在，执行步骤4.2的SQL语句

### 错误4：网络连接问题
```
调用LLM API失败: Connection timeout
```

**解决方案**：
1. 检查网络连接
2. 检查防火墙设置
3. 检查代理配置
4. 验证API地址是否正确

## 验证清单

在测试翻译功能之前，请确认以下项目：

- [ ] llm_provider表中存在name='gemini'且status=1的记录
- [ ] llm_model表中存在model_identifier='gemini-2.5-pro'且status=1的记录
- [ ] 模型正确关联到gemini提供商（provider_id匹配）
- [ ] ai_prompt表中存在key='email_monument_translate'且enable=1的记录
- [ ] llm_api_key表中存在gemini提供商的有效API密钥
- [ ] API密钥状态为1（启用）
- [ ] 网络连接正常，可以访问Gemini API

## 监控和日志

### 启用详细日志
在application.properties中添加：
```properties
# 启用LLM服务调试日志
log4j.logger.cn.jbolt.llm=DEBUG
log4j.logger.cn.jbolt.mail.gpt=DEBUG
```

### 查看日志文件
检查以下日志文件：
- 应用主日志：查看LLM调用相关的错误信息
- 网络请求日志：查看API调用的详细信息
- 数据库日志：查看SQL查询是否正常执行

## 联系支持

如果按照以上步骤仍无法解决问题，请提供以下信息：

1. 调试测试的完整输出
2. 相关数据库表的查询结果
3. 应用日志中的错误信息
4. 网络环境和代理配置信息
5. API密钥的配置情况（不要包含实际密钥）

通过系统性的排查和修复，应该能够解决翻译测试失败的问题。
