package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Gemini大模型适配器
 * 将Google Gemini API转换为OpenAI兼容格式
 * 支持文本、图片、视频和音频处理
 * 
 * API文档: https://ai.google.dev/docs/gemini_api_overview
 */
public class GeminiAdapter extends AbstractMultimodalAdapter {
    
    public GeminiAdapter() {
        // Gemini支持多种模态
        this.defaultSupportedModalities = Arrays.asList(
            ModalityType.TEXT,
            ModalityType.IMAGE,
            ModalityType.VIDEO,
            ModalityType.AUDIO
        );
    }

    @Override
    public String convertRequest(String model, List<Kv> messages) {
        JSONObject requestBody = new JSONObject();
        
        // Gemini的请求格式
        JSONArray contents = new JSONArray();
        
        for (Kv message : messages) {
            JSONObject content = new JSONObject();
            String role = message.getStr("role");
            
            // Gemini角色映射: user->user, assistant->model, system->user(特殊处理)
            if ("assistant".equals(role)) {
                content.put("role", "model");
            } else if ("system".equals(role)) {
                content.put("role", "user");
            } else {
                content.put("role", "user");
            }
            
            // 构建parts数组
            JSONArray parts = new JSONArray();
            JSONObject part = new JSONObject();
            part.put("text", message.get("content"));
            parts.add(part);
            
            content.put("parts", parts);
            contents.add(content);
        }
        
        requestBody.put("contents", contents);
        
        // 添加生成配置
        JSONObject generationConfig = new JSONObject();
        generationConfig.put("temperature", 0.7);
        generationConfig.put("maxOutputTokens", 4096);
        requestBody.put("generationConfig", generationConfig);
        
        return requestBody.toJSONString();
    }

    @Override
    public String convertResponse(String responseBody) {
        try {
            JSONObject geminiResponse = JSON.parseObject(responseBody);
            
            // 创建OpenAI兼容的响应格式
            JSONObject openaiResponse = new JSONObject();
            
            // 使用Gemini的responseId作为id（如果有的话）
            String responseId = geminiResponse.containsKey("responseId") ? 
                              geminiResponse.getString("responseId") : 
                              "gemini-" + System.currentTimeMillis();
            openaiResponse.put("id", responseId);
            openaiResponse.put("object", "chat.completion");
            openaiResponse.put("created", System.currentTimeMillis() / 1000);
            
            // 使用实际的模型版本（如果有的话）
            String modelName = geminiResponse.containsKey("modelVersion") ? 
                             geminiResponse.getString("modelVersion") : 
                             "gemini-model";
            openaiResponse.put("model", modelName);
            
            // 处理错误情况
            if (geminiResponse.containsKey("error")) {
                openaiResponse.put("error", geminiResponse.getJSONObject("error"));
                return openaiResponse.toJSONString();
            }
            
            // 处理正常响应
            JSONArray choices = new JSONArray();
            JSONObject choice = new JSONObject();
            choice.put("index", 0);
            
            JSONObject message = new JSONObject();
            message.put("role", "assistant");
            
            // 获取Gemini响应中的内容
            if (geminiResponse.containsKey("candidates")) {
                JSONArray candidates = geminiResponse.getJSONArray("candidates");
                if (candidates != null && candidates.size() > 0) {
                    JSONObject candidate = candidates.getJSONObject(0);
                    
                    String responseText = "无法获取响应内容";
                    
                    // 尝试从content.parts中获取文本
                    if (candidate.containsKey("content")) {
                        JSONObject content = candidate.getJSONObject("content");
                        if (content.containsKey("parts")) {
                            JSONArray parts = content.getJSONArray("parts");
                            if (parts != null && parts.size() > 0) {
                                JSONObject part = parts.getJSONObject(0);
                                if (part.containsKey("text")) {
                                    responseText = part.getString("text");
                                }
                            }
                        }
                    }
                    
                    // 如果content中没有parts或text，检查是否有其他文本字段
                    if ("无法获取响应内容".equals(responseText)) {
                        // 检查candidate级别是否有text字段
                        if (candidate.containsKey("text")) {
                            responseText = candidate.getString("text");
                        } else if (candidate.containsKey("output")) {
                            responseText = candidate.getString("output");
                        } else {
                            // 如果都没有，可能是空响应或被过滤
                            if (candidate.containsKey("finishReason")) {
                                String finishReason = candidate.getString("finishReason");
                                if ("SAFETY".equals(finishReason)) {
                                    responseText = "响应因安全策略被过滤";
                                } else if ("RECITATION".equals(finishReason)) {
                                    responseText = "响应因版权问题被过滤";
                                } else {
                                    responseText = "响应为空，完成原因: " + finishReason;
                                }
                            }
                        }
                    }
                    
                    message.put("content", responseText);
                    
                    // 处理finish_reason
                    if (candidate.containsKey("finishReason")) {
                        String finishReason = candidate.getString("finishReason");
                        if ("STOP".equals(finishReason)) {
                            choice.put("finish_reason", "stop");
                        } else if ("MAX_TOKENS".equals(finishReason)) {
                            choice.put("finish_reason", "length");
                        } else if ("SAFETY".equals(finishReason)) {
                            choice.put("finish_reason", "content_filter");
                        } else if ("RECITATION".equals(finishReason)) {
                            choice.put("finish_reason", "content_filter");
                        } else {
                            choice.put("finish_reason", finishReason.toLowerCase());
                        }
                    } else {
                        choice.put("finish_reason", "stop");
                    }
                    
                    // 处理index字段
                    if (candidate.containsKey("index")) {
                        choice.put("index", candidate.getIntValue("index"));
                    } else {
                        choice.put("index", 0);
                    }
                } else {
                    message.put("content", "无法获取响应内容");
                    choice.put("finish_reason", "stop");
                    choice.put("index", 0);
                }
            } else {
                message.put("content", "无法获取响应内容");
                choice.put("finish_reason", "stop");
                choice.put("index", 0);
            }
            
            choice.put("message", message);
            choices.add(choice);
            
            openaiResponse.put("choices", choices);
            
            // 处理用量信息
            if (geminiResponse.containsKey("usageMetadata")) {
                JSONObject geminiUsage = geminiResponse.getJSONObject("usageMetadata");
                JSONObject usage = new JSONObject();
                
                // 处理输入token数
                if (geminiUsage.containsKey("promptTokenCount")) {
                    usage.put("prompt_tokens", geminiUsage.getIntValue("promptTokenCount"));
                }
                
                // 处理输出token数 - 新版本可能没有candidatesTokenCount
                int completionTokens = 0;
                if (geminiUsage.containsKey("candidatesTokenCount")) {
                    completionTokens = geminiUsage.getIntValue("candidatesTokenCount");
                } else {
                    // 如果没有candidatesTokenCount，尝试从totalTokenCount减去promptTokenCount
                    int totalTokens = geminiUsage.getIntValue("totalTokenCount");
                    int promptTokens = geminiUsage.getIntValue("promptTokenCount");
                    // 减去思考token数（如果有的话）
                    int thoughtsTokens = geminiUsage.containsKey("thoughtsTokenCount") ? 
                                       geminiUsage.getIntValue("thoughtsTokenCount") : 0;
                    completionTokens = Math.max(0, totalTokens - promptTokens - thoughtsTokens);
                }
                usage.put("completion_tokens", completionTokens);
                
                // 处理总token数
                if (geminiUsage.containsKey("totalTokenCount")) {
                    usage.put("total_tokens", geminiUsage.getIntValue("totalTokenCount"));
                } else if (usage.containsKey("prompt_tokens") && usage.containsKey("completion_tokens")) {
                    usage.put("total_tokens", 
                            usage.getIntValue("prompt_tokens") + usage.getIntValue("completion_tokens"));
                }
                
                // 添加扩展信息（可选）
                if (geminiUsage.containsKey("thoughtsTokenCount")) {
                    usage.put("thoughts_tokens", geminiUsage.getIntValue("thoughtsTokenCount"));
                }
                
                openaiResponse.put("usage", usage);
            }
            
            return openaiResponse.toJSONString();
        } catch (Exception e) {
            LogKit.error("Gemini响应转换失败", e);
            JSONObject errorResponse = new JSONObject();
            JSONObject error = new JSONObject();
            error.put("message", "Gemini响应转换失败: " + e.getMessage());
            error.put("type", "adapter_error");
            errorResponse.put("error", error);
            return errorResponse.toJSONString();
        }
    }

    @Override
    public Map<String, String> getHeaders(String apiKey, String apiSecret) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        // Gemini API key通常通过URL参数传递，但也可以通过header
        headers.put("x-goog-api-key", apiKey);
        return headers;
    }
    
    @Override
    public Object processImages(List<String> imagePaths, String prompt) {
        // Gemini的多模态内容格式
        JSONArray parts = new JSONArray();
        
        // 添加文本部分
        if (prompt != null && !prompt.isEmpty()) {
            JSONObject textPart = new JSONObject();
            textPart.put("text", prompt);
            parts.add(textPart);
        }
        
        // 添加图片部分
        if (imagePaths != null) {
            for (String imagePath : imagePaths) {
                if (isFileAccessible(imagePath)) {
                    JSONObject imagePart = new JSONObject();
                    
                    JSONObject inlineData = new JSONObject();
                    inlineData.put("mime_type", getMimeType(imagePath));
                    
                    // 获取base64数据（不包含data:前缀）
                    String dataUrl = convertImageToDataUrl(imagePath);
                    if (dataUrl != null && dataUrl.contains(",")) {
                        String base64Data = dataUrl.split(",")[1];
                        inlineData.put("data", base64Data);
                    }
                    
                    imagePart.put("inline_data", inlineData);
                    parts.add(imagePart);
                }
            }
        }
        
        return parts;
    }
    
    @Override
    public Object processVideos(List<String> videoPaths, String prompt) {
        JSONArray parts = new JSONArray();
        
        // 添加文本部分
        if (prompt != null && !prompt.isEmpty()) {
            JSONObject textPart = new JSONObject();
            textPart.put("text", prompt);
            parts.add(textPart);
        }
        
        // 添加视频部分
        if (videoPaths != null) {
            for (String videoPath : videoPaths) {
                if (isFileAccessible(videoPath)) {
                    JSONObject videoPart = new JSONObject();
                    
                    JSONObject inlineData = new JSONObject();
                    inlineData.put("mime_type", getMimeType(videoPath));
                    
                    try {
                        byte[] videoData = Files.readAllBytes(Paths.get(videoPath));
                        String base64Data = Base64.getEncoder().encodeToString(videoData);
                        inlineData.put("data", base64Data);
                        
                        videoPart.put("inline_data", inlineData);
                        parts.add(videoPart);
                    } catch (Exception e) {
                        LogKit.error("处理视频文件失败: " + videoPath, e);
                    }
                }
            }
        }
        
        return parts;
    }
    
    @Override
    public Object processAudios(List<String> audioPaths, String prompt) {
        JSONArray parts = new JSONArray();
        
        // 添加文本部分
        if (prompt != null && !prompt.isEmpty()) {
            JSONObject textPart = new JSONObject();
            textPart.put("text", prompt);
            parts.add(textPart);
        }
        
        // 添加音频部分
        if (audioPaths != null) {
            for (String audioPath : audioPaths) {
                if (isFileAccessible(audioPath)) {
                    JSONObject audioPart = new JSONObject();
                    
                    JSONObject inlineData = new JSONObject();
                    inlineData.put("mime_type", getMimeType(audioPath));
                    
                    try {
                        byte[] audioData = Files.readAllBytes(Paths.get(audioPath));
                        String base64Data = Base64.getEncoder().encodeToString(audioData);
                        inlineData.put("data", base64Data);
                        
                        audioPart.put("inline_data", inlineData);
                        parts.add(audioPart);
                    } catch (Exception e) {
                        LogKit.error("处理音频文件失败: " + audioPath, e);
                    }
                }
            }
        }
        
        return parts;
    }
}