package cn.jbolt.mail.gpt;

import cn.jbolt.common.model.*;
import cn.jbolt.core.base.config.JBoltConfig;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.druid.DruidPlugin;
import com.jfinal.plugin.ehcache.EhCachePlugin;

public class InitEnv {
    private static boolean initialized = false;
    private static DruidPlugin druidPlugin;
    private static ActiveRecordPlugin activeRecordPlugin;
    private static EhCachePlugin ehCachePlugin;

    /**
     * 清理环境和配置
     * 主要用于测试场景，避免多个测试重复初始化导致的冲突
     */
    public static void cleanEnvironment() {
        try {
            // 停止插件
            if (ehCachePlugin != null) {
                ehCachePlugin.stop();
                ehCachePlugin = null;
            }

            if (activeRecordPlugin != null) {
                activeRecordPlugin.stop();
                activeRecordPlugin = null;
            }

            if (druidPlugin != null) {
                druidPlugin.stop();
                druidPlugin = null;
            }

            // 清理PropKit配置
            PropKit.clear();

            // 重置初始化标志
            initialized = false;

            System.out.println("环境清理完成！");
        } catch (Exception e) {
            System.out.println("清理环境失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void initEnvironment() {
        // 如果已经初始化，直接返回
        if (initialized) {
            System.out.println("环境已经初始化，跳过重复初始化");
            return;
        }

        try {
            // 加载配置文件
            PropKit.use("config.properties");
            PropKit.appendIfExists("dbconfig/mysql/config.properties");
            PropKit.appendIfExists("email_fetch_config.properties");

            druidPlugin = JBoltConfig.createDruidPlugin();
            druidPlugin.start();
            activeRecordPlugin = new ActiveRecordPlugin(druidPlugin);

            activeRecordPlugin.addMapping("email_account", EmailAccount.class);
            activeRecordPlugin.addMapping("email_attachment_queue", EmailAttachmentQueue.class);
            activeRecordPlugin.addMapping("email_folders", EmailFolders.class);
            activeRecordPlugin.addMapping("email_attachments", EmailAttachments.class);
            activeRecordPlugin.addMapping("email_fetch_task", EmailFetchTask.class);
            activeRecordPlugin.addMapping("email_sync_log", EmailSyncLog.class);
            activeRecordPlugin.addMapping("email_messages", EmailMessages.class);
            activeRecordPlugin.addMapping("email_sync_state", EmailSyncState.class);
            activeRecordPlugin.addMapping("emails_email", EmailsEmail.class);
            activeRecordPlugin.addMapping("email_translation", EmailTranslation.class);
            activeRecordPlugin.addMapping("email_tracking_records", EmailTrackingRecord.class);
            activeRecordPlugin.addMapping("email_tracking_opens", EmailTrackingOpen.class);
            activeRecordPlugin.addMapping("email_tracking_pixels", EmailTrackingPixel.class);
            activeRecordPlugin.addMapping("email_tracking_stats", EmailTrackingStats.class);
            activeRecordPlugin.addMapping("email_tracking_config", EmailTrackingConfig.class);
            activeRecordPlugin.addMapping("company", Company.class);
            activeRecordPlugin.addMapping("client", Client.class);
            activeRecordPlugin.addMapping("company_client", CompanyClient.class);
            activeRecordPlugin.addMapping("ai_prompt", AiPrompt.class);
            activeRecordPlugin.addMapping("user_company", UserCompany.class);
            activeRecordPlugin.addMapping("user_email", UserEmail.class);
            activeRecordPlugin.start();

            // 启动缓存插件
            ehCachePlugin = new EhCachePlugin();
            ehCachePlugin.start();

            // 设置初始化标志
            initialized = true;

            System.out.println("环境初始化完成！");
        } catch (Exception e) {
            System.out.println("初始化环境失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
