<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>相关文件图片预览功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layer@3.5.1/dist/layer.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .demo-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        
        /* 相关文件样式 */
        .related-files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            align-items: start;
        }
        .related-file-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        .related-file-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
            transform: translateY(-1px);
        }
        
        /* 图片文件特殊样式 */
        .related-file-item.image-file {
            flex-direction: column;
            align-items: stretch;
            padding: 10px;
            min-height: 200px;
        }
        
        .file-thumbnail {
            position: relative;
            width: 100%;
            height: 120px;
            margin-bottom: 10px;
            border-radius: 6px;
            overflow: hidden;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .file-thumbnail:hover img {
            transform: scale(1.05);
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .file-thumbnail:hover .image-overlay {
            opacity: 1;
        }
        
        .image-file .file-info {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .image-file .file-actions {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin: 0;
        }
        
        /* 普通文件样式 */
        .file-icon {
            font-size: 24px;
            margin-right: 15px;
            width: 40px;
            text-align: center;
        }
        .file-info {
            flex: 1;
            min-width: 0;
        }
        .file-name {
            font-weight: 500;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .file-size {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 4px;
        }
        .file-actions {
            margin-left: 10px;
        }
        .badge-sm {
            font-size: 10px;
            padding: 2px 6px;
        }
        
        /* 文件类型颜色 */
        .file-type-image .file-icon { color: #28a745; }
        .file-type-pdf .file-icon { color: #dc3545; }
        .file-type-word .file-icon { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>相关文件图片预览功能演示</h1>
        <p>展示了相关文件中图片的缩略图预览和点击放大查看功能。</p>
        
        <div class="demo-section">
            <div class="demo-title">功能特点</div>
            <ul>
                <li><strong>缩略图显示</strong>：图片文件显示缩略图预览</li>
                <li><strong>悬停效果</strong>：鼠标悬停时显示放大镜图标和缩放效果</li>
                <li><strong>点击预览</strong>：点击图片打开大图预览弹窗</li>
                <li><strong>多种操作</strong>：支持下载、新窗口打开等操作</li>
                <li><strong>错误处理</strong>：图片加载失败时显示备用图标</li>
                <li><strong>键盘支持</strong>：ESC键关闭预览窗口</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">模拟相关文件列表</div>
            <div class="mb-3">
                <span class="badge badge-info">共 4 个文件</span>
            </div>
            <div class="related-files-grid">
                <!-- 图片文件1 -->
                <div class="related-file-item file-type-image image-file" onclick="previewDemoImage('https://picsum.photos/800/600?random=1', 'sample-image-1.jpg', 0)">
                    <div class="file-thumbnail">
                        <img src="https://picsum.photos/300/200?random=1" alt="sample-image-1.jpg">
                        <div class="image-overlay">
                            <i class="fa fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="sample-image-1.jpg">sample-image-1.jpg</div>
                        <div class="file-size">245 KB</div>
                        <span class="badge badge-secondary badge-sm">附件</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); previewDemoImage('https://picsum.photos/800/600?random=1', 'sample-image-1.jpg', 0)" title="预览">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); alert('下载: sample-image-1.jpg')" title="下载">
                            <i class="fa fa-download"></i>
                        </button>
                    </div>
                </div>
                
                <!-- PDF文件 -->
                <div class="related-file-item file-type-pdf" onclick="alert('下载: document.pdf')">
                    <div class="file-icon">
                        <i class="fa fa-file-pdf-o"></i>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="document.pdf">document.pdf</div>
                        <div class="file-size">1.2 MB</div>
                        <span class="badge badge-secondary badge-sm">附件</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); alert('下载: document.pdf')" title="下载">
                            <i class="fa fa-download"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 图片文件2 -->
                <div class="related-file-item file-type-image image-file" onclick="previewDemoImage('https://picsum.photos/600/800?random=2', 'photo-2.png', 1)">
                    <div class="file-thumbnail">
                        <img src="https://picsum.photos/300/200?random=2" alt="photo-2.png">
                        <div class="image-overlay">
                            <i class="fa fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="photo-2.png">photo-2.png</div>
                        <div class="file-size">512 KB</div>
                        <span class="badge badge-warning badge-sm">内嵌</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); previewDemoImage('https://picsum.photos/600/800?random=2', 'photo-2.png', 1)" title="预览">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); alert('下载: photo-2.png')" title="下载">
                            <i class="fa fa-download"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Word文件 -->
                <div class="related-file-item file-type-word" onclick="alert('下载: report.docx')">
                    <div class="file-icon">
                        <i class="fa fa-file-word-o"></i>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="report.docx">report.docx</div>
                        <div class="file-size">856 KB</div>
                        <span class="badge badge-secondary badge-sm">附件</span>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); alert('下载: report.docx')" title="下载">
                            <i class="fa fa-download"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">使用说明</div>
            <ol>
                <li><strong>图片预览</strong>：点击图片文件查看大图</li>
                <li><strong>悬停效果</strong>：鼠标悬停在图片上查看放大镜图标</li>
                <li><strong>操作按钮</strong>：使用预览和下载按钮进行相应操作</li>
                <li><strong>键盘操作</strong>：在预览窗口中按ESC键关闭</li>
                <li><strong>新窗口打开</strong>：在预览窗口中可选择在新标签页打开图片</li>
            </ol>
        </div>
    </div>

    <script>
        // 演示用的图片预览函数
        function previewDemoImage(imageUrl, fileName, index) {
            const previewHtml = `
                <div class="image-preview-container">
                    <div class="image-preview-header">
                        <h6 class="mb-0">${fileName}</h6>
                        <div class="image-preview-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="alert('下载: ${fileName}')" title="下载">
                                <i class="fa fa-download"></i> 下载
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="window.open('${imageUrl}', '_blank')" title="在新标签页打开">
                                <i class="fa fa-external-link"></i> 新窗口
                            </button>
                        </div>
                    </div>
                    <div class="image-preview-body">
                        <img src="${imageUrl}" alt="${fileName}" class="preview-image">
                    </div>
                </div>
                <style>
                    .image-preview-container {
                        padding: 0;
                        max-height: 80vh;
                        display: flex;
                        flex-direction: column;
                    }
                    .image-preview-header {
                        padding: 15px 20px;
                        border-bottom: 1px solid #e0e0e0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        background-color: #f8f9fa;
                    }
                    .image-preview-actions {
                        display: flex;
                        gap: 8px;
                    }
                    .image-preview-body {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 20px;
                        background-color: #000;
                        min-height: 400px;
                    }
                    .preview-image {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                        border-radius: 4px;
                        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
                    }
                </style>
            `;
            
            layer.open({
                type: 1,
                title: false,
                area: ['90%', '90%'],
                maxmin: true,
                content: previewHtml
            });
        }
    </script>
</body>
</html>
