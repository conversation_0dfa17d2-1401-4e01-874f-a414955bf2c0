-- 设置代理配置 - 解决Gemini API网络连接问题

-- 1. 添加代理配置项（如果不存在）
INSERT IGNORE INTO email_translation_config (config_key, config_value, value_type, description, is_enabled) VALUES
('translation.proxy_host', '', 'STRING', '代理主机地址', TRUE),
('translation.proxy_port', '', 'STRING', '代理端口', TRUE);

-- 2. 设置本地代理配置（根据你的代理设置）
-- 常用配置：127.0.0.1:7890 (Clash)
UPDATE email_translation_config 
SET config_value = '127.0.0.1', last_update_time = NOW(), change_reason = '设置本地代理解决网络问题'
WHERE config_key = 'translation.proxy_host';

UPDATE email_translation_config 
SET config_value = '7890', last_update_time = NOW(), change_reason = '设置本地代理解决网络问题'
WHERE config_key = 'translation.proxy_port';

-- 3. 验证代理配置
SELECT 
    config_key,
    config_value,
    description,
    CASE WHEN is_enabled = 1 THEN '启用' ELSE '禁用' END as status,
    last_update_time
FROM email_translation_config 
WHERE config_key IN ('translation.proxy_host', 'translation.proxy_port')
ORDER BY config_key;

-- 4. 检查完整的翻译配置
SELECT 
    config_key,
    config_value,
    description
FROM email_translation_config 
WHERE config_key LIKE 'translation.%'
ORDER BY config_key;

-- 5. 如果需要清除代理配置，执行以下SQL
-- UPDATE email_translation_config SET config_value = '' WHERE config_key = 'translation.proxy_host';
-- UPDATE email_translation_config SET config_value = '' WHERE config_key = 'translation.proxy_port';

-- 6. 其他常用代理配置示例
-- Clash: 127.0.0.1:7890
-- V2Ray: 127.0.0.1:1080
-- Shadowsocks: 127.0.0.1:1080
-- 如果使用其他代理，请相应修改上面的UPDATE语句
