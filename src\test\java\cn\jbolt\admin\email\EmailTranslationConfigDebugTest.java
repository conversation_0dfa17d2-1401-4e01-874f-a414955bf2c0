package cn.jbolt.admin.email;

import cn.jbolt.common.model.AiPrompt;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * 邮件翻译配置调试测试
 */
public class EmailTranslationConfigDebugTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查LLM提供商配置
     */
    @Test
    public void testLlmProviders() {
        System.out.println("=== 测试1：检查LLM提供商配置 ===");
        
        try {
            // 查询所有提供商
            List<LlmProvider> allProviders = new LlmProvider().dao().find("SELECT * FROM llm_provider ORDER BY priority");
            System.out.println("总共找到 " + allProviders.size() + " 个提供商:");
            
            for (LlmProvider provider : allProviders) {
                System.out.println("  ID: " + provider.getId() + 
                                 ", 名称: " + provider.getName() + 
                                 ", 状态: " + (provider.getStatus() ? "启用" : "禁用") +
                                 ", 优先级: " + provider.getPriority() +
                                 ", API类型: " + provider.getApiType());
            }
            
            // 查询启用的提供商
            List<LlmProvider> enabledProviders = new LlmProvider().dao().find("SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority");
            System.out.println("\n启用的提供商 (" + enabledProviders.size() + " 个):");
            
            for (LlmProvider provider : enabledProviders) {
                System.out.println("  ✓ " + provider.getName() + " (ID: " + provider.getId() + ")");
            }
            
            // 检查gemini提供商
            LlmProvider geminiProvider = new LlmProvider().dao().findFirst("SELECT * FROM llm_provider WHERE name = 'gemini'");
            if (geminiProvider != null) {
                System.out.println("\n✓ 找到gemini提供商:");
                System.out.println("  ID: " + geminiProvider.getId());
                System.out.println("  状态: " + (geminiProvider.getStatus() ? "启用" : "禁用"));
                System.out.println("  API类型: " + geminiProvider.getApiType());
                System.out.println("  API地址: " + geminiProvider.getApiBaseUrl());
            } else {
                System.out.println("\n✗ 未找到gemini提供商");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询提供商失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：检查LLM模型配置
     */
    @Test
    public void testLlmModels() {
        System.out.println("=== 测试2：检查LLM模型配置 ===");
        
        try {
            // 查询所有模型
            List<LlmModel> allModels = new LlmModel().dao().find("SELECT * FROM llm_model ORDER BY provider_id, id");
            System.out.println("总共找到 " + allModels.size() + " 个模型:");
            
            for (LlmModel model : allModels) {
                System.out.println("  ID: " + model.getId() + 
                                 ", 提供商ID: " + model.getProviderId() +
                                 ", 模型标识: " + model.getModelIdentifier() +
                                 ", 模型名称: " + model.getModelName() +
                                 ", 状态: " + (model.getStatus() ? "启用" : "禁用"));
            }
            
            // 查询启用的模型
            List<LlmModel> enabledModels = new LlmModel().dao().find("SELECT * FROM llm_model WHERE status = 1 ORDER BY provider_id, id");
            System.out.println("\n启用的模型 (" + enabledModels.size() + " 个):");
            
            for (LlmModel model : enabledModels) {
                System.out.println("  ✓ " + model.getModelIdentifier() + " (提供商ID: " + model.getProviderId() + ")");
            }
            
            // 检查gemini提供商的模型
            LlmProvider geminiProvider = new LlmProvider().dao().findFirst("SELECT * FROM llm_provider WHERE name = 'gemini'");
            if (geminiProvider != null) {
                List<LlmModel> geminiModels = new LlmModel().dao().find("SELECT * FROM llm_model WHERE provider_id = ? ORDER BY id", geminiProvider.getId());
                System.out.println("\ngemini提供商的模型 (" + geminiModels.size() + " 个):");
                
                for (LlmModel model : geminiModels) {
                    System.out.println("  " + (model.getStatus() ? "✓" : "✗") + " " +
                                     model.getModelIdentifier() + " - " + model.getModelName() +
                                     " (状态: " + (model.getStatus() ? "启用" : "禁用") + ")");
                }
                
                // 检查gemini-2.5-pro模型
                LlmModel gemini25Pro = new LlmModel().dao().findFirst(
                    "SELECT * FROM llm_model WHERE provider_id = ? AND model_identifier = ?", 
                    geminiProvider.getId(), "gemini-2.5-pro");
                
                if (gemini25Pro != null) {
                    System.out.println("\n✓ 找到gemini-2.5-pro模型:");
                    System.out.println("  ID: " + gemini25Pro.getId());
                    System.out.println("  状态: " + (gemini25Pro.getStatus() ? "启用" : "禁用"));
                    System.out.println("  模型名称: " + gemini25Pro.getModelName());
                } else {
                    System.out.println("\n✗ 未找到gemini-2.5-pro模型");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询模型失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：检查AI提示词配置
     */
    @Test
    public void testAiPrompts() {
        System.out.println("=== 测试3：检查AI提示词配置 ===");
        
        try {
            // 查询翻译提示词
            AiPrompt translatePrompt = new AiPrompt().dao().findFirst(
                "SELECT * FROM ai_prompt WHERE enable = '1' AND `key` = 'email_monument_translate' ORDER BY id LIMIT 1"
            );
            
            if (translatePrompt != null) {
                System.out.println("✓ 找到翻译提示词:");
                System.out.println("  ID: " + translatePrompt.getId());
                System.out.println("  Key: " + translatePrompt.getKey());
                System.out.println("  系统内容长度: " + (translatePrompt.getSystemContent() != null ? translatePrompt.getSystemContent().length() : 0) + " 字符");
                System.out.println("  用户内容长度: " + (translatePrompt.getUserContent() != null ? translatePrompt.getUserContent().length() : 0) + " 字符");
                System.out.println("  启用状态: " + (translatePrompt.getEnable() ? "启用" : "禁用"));
                
                if (translatePrompt.getSystemContent() != null && translatePrompt.getSystemContent().length() > 0) {
                    System.out.println("  系统内容预览: " + translatePrompt.getSystemContent().substring(0, Math.min(100, translatePrompt.getSystemContent().length())) + "...");
                }
            } else {
                System.out.println("✗ 未找到翻译提示词");
                System.out.println("请在AI提示词管理中添加key为'email_monument_translate'的提示词");
            }
            
            // 查询所有启用的提示词
            List<AiPrompt> allPrompts = new AiPrompt().dao().find("SELECT * FROM ai_prompt WHERE enable = '1' ORDER BY id");
            System.out.println("\n所有启用的提示词 (" + allPrompts.size() + " 个):");
            
            for (AiPrompt prompt : allPrompts) {
                System.out.println("  ✓ " + prompt.getKey() + " (ID: " + prompt.getId() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询提示词失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：模拟翻译测试调用
     */
    @Test
    public void testTranslationCall() {
        System.out.println("=== 测试4：模拟翻译测试调用 ===");
        
        try {
            String providerName = "gemini";
            String modelName = "gemini-2.5-pro";
            String testText = "Hello, this is a test message for translation.";
            
            System.out.println("测试参数:");
            System.out.println("  提供商: " + providerName);
            System.out.println("  模型: " + modelName);
            System.out.println("  测试文本: " + testText);
            
            // 验证提供商
            LlmProvider llmProvider = new LlmProvider().dao()
                .findFirst("SELECT * FROM llm_provider WHERE status = 1 AND name = ? ORDER BY priority", providerName);
            
            if (llmProvider == null) {
                System.out.println("✗ 找不到提供商: " + providerName);
                return;
            } else {
                System.out.println("✓ 找到提供商: " + llmProvider.getName() + " (ID: " + llmProvider.getId() + ")");
            }
            
            // 验证模型
            LlmModel llmModel = new LlmModel().dao()
                .findFirst("SELECT * FROM llm_model WHERE status = 1 AND provider_id = ? AND model_identifier = ? ORDER BY id", 
                          llmProvider.getId(), modelName);
            
            if (llmModel == null) {
                System.out.println("✗ 找不到模型: " + modelName + "，提供商ID: " + llmProvider.getId());
                return;
            } else {
                System.out.println("✓ 找到模型: " + llmModel.getModelIdentifier() + " (ID: " + llmModel.getId() + ")");
            }
            
            // 获取提示词
            AiPrompt aiPrompt = new AiPrompt().dao().findFirst(
                "SELECT * FROM ai_prompt WHERE enable = '1' AND `key` = 'email_monument_translate' ORDER BY id LIMIT 1"
            );
            
            String prompt = testText;
            if (aiPrompt != null && aiPrompt.getSystemContent() != null) {
                prompt = aiPrompt.getSystemContent() + "\n" + testText;
                System.out.println("✓ 使用AI提示词，完整prompt长度: " + prompt.length() + " 字符");
            } else {
                System.out.println("⚠ 使用默认prompt");
            }
            
            System.out.println("✓ 翻译调用准备完成");
            System.out.println("注意：实际的LLM调用需要有效的API密钥和网络连接");
            
        } catch (Exception e) {
            System.err.println("✗ 模拟翻译调用失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("邮件翻译配置调试测试");
        System.out.println("========================================");
        
        testLlmProviders();
        testLlmModels();
        testAiPrompts();
        testTranslationCall();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n问题排查建议:");
        System.out.println("1. 确保llm_provider表中存在name='gemini'且status=1的记录");
        System.out.println("2. 确保llm_model表中存在model_identifier='gemini-2.5-pro'且status=1的记录");
        System.out.println("3. 确保ai_prompt表中存在key='email_monument_translate'且enable=1的记录");
        System.out.println("4. 检查API密钥配置是否正确");
        System.out.println("5. 检查网络连接是否正常");
    }
}
