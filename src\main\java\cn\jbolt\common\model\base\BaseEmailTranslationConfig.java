package cn.jbolt.common.model.base;

import cn.jbolt.core.base.JBoltBaseModel;
import cn.jbolt.core.db.sql.annotation.JBoltField;

/**
 * 邮件翻译配置基础模型
 */
@SuppressWarnings("serial")
public abstract class BaseEmailTranslationConfig<M extends BaseEmailTranslationConfig<M>> extends JBoltBaseModel<M> {

    /**ID*/
    public static final String ID = "id";
    /**配置键*/
    public static final String CONFIG_KEY = "config_key";
    /**配置值*/
    public static final String CONFIG_VALUE = "config_value";
    /**配置类型*/
    public static final String CONFIG_TYPE = "config_type";
    /**配置描述*/
    public static final String DESCRIPTION = "description";
    /**是否启用*/
    public static final String IS_ACTIVE = "is_active";
    /**创建时间*/
    public static final String CREATE_TIME = "create_time";
    /**更新时间*/
    public static final String UPDATE_TIME = "update_time";

    /**
     * ID
     */
    public M setId(java.lang.Long id) {
        set("id", id);
        return (M)this;
    }
    
    /**
     * ID
     */
    @JBoltField(name="id" ,columnName="id",type="Long", remark="ID", required=true, maxLength=19, fixed=0, order=1)
    public java.lang.Long getId() {
        return getLong("id");
    }

    /**
     * 配置键
     */
    public M setConfigKey(java.lang.String configKey) {
        set("config_key", configKey);
        return (M)this;
    }
    
    /**
     * 配置键
     */
    @JBoltField(name="configKey" ,columnName="config_key",type="String", remark="配置键", required=true, maxLength=100, fixed=0, order=2)
    public java.lang.String getConfigKey() {
        return getStr("config_key");
    }

    /**
     * 配置值
     */
    public M setConfigValue(java.lang.String configValue) {
        set("config_value", configValue);
        return (M)this;
    }
    
    /**
     * 配置值
     */
    @JBoltField(name="configValue" ,columnName="config_value",type="String", remark="配置值", required=false, maxLength=65535, fixed=0, order=3)
    public java.lang.String getConfigValue() {
        return getStr("config_value");
    }

    /**
     * 配置类型
     */
    public M setConfigType(java.lang.String configType) {
        set("config_type", configType);
        return (M)this;
    }
    
    /**
     * 配置类型
     */
    @JBoltField(name="configType" ,columnName="config_type",type="String", remark="配置类型", required=false, maxLength=50, fixed=0, order=4)
    public java.lang.String getConfigType() {
        return getStr("config_type");
    }

    /**
     * 配置描述
     */
    public M setDescription(java.lang.String description) {
        set("description", description);
        return (M)this;
    }
    
    /**
     * 配置描述
     */
    @JBoltField(name="description" ,columnName="description",type="String", remark="配置描述", required=false, maxLength=500, fixed=0, order=5)
    public java.lang.String getDescription() {
        return getStr("description");
    }

    /**
     * 是否启用
     */
    public M setIsActive(java.lang.Boolean isActive) {
        set("is_active", isActive);
        return (M)this;
    }
    
    /**
     * 是否启用
     */
    @JBoltField(name="isActive" ,columnName="is_active",type="Boolean", remark="是否启用", required=false, maxLength=1, fixed=0, order=6)
    public java.lang.Boolean getIsActive() {
        return getBoolean("is_active");
    }

    /**
     * 创建时间
     */
    public M setCreateTime(java.util.Date createTime) {
        set("create_time", createTime);
        return (M)this;
    }
    
    /**
     * 创建时间
     */
    @JBoltField(name="createTime" ,columnName="create_time",type="Date", remark="创建时间", required=false, maxLength=19, fixed=0, order=7)
    public java.util.Date getCreateTime() {
        return getDate("create_time");
    }

    /**
     * 更新时间
     */
    public M setUpdateTime(java.util.Date updateTime) {
        set("update_time", updateTime);
        return (M)this;
    }
    
    /**
     * 更新时间
     */
    @JBoltField(name="updateTime" ,columnName="update_time",type="Date", remark="更新时间", required=false, maxLength=19, fixed=0, order=8)
    public java.util.Date getUpdateTime() {
        return getDate("update_time");
    }
}
