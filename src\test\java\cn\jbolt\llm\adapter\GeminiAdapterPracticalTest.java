package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Gemini适配器实用测试类
 * 可以直接运行的测试方法
 */
public class GeminiAdapterPracticalTest {
    
    public static void main(String[] args) {
        GeminiAdapterPracticalTest test = new GeminiAdapterPracticalTest();
        
        System.out.println("开始Gemini适配器测试...\n");
        
        test.testEnglishTranslation();
        test.testJapaneseTranslation();
        test.testKoreanTranslation();
        test.testImageAnalysis();
        test.testMultiImageAnalysis();
        test.testComplexTranslation();
        
        System.out.println("所有测试完成！");
    }
    
    /**
     * 测试英文翻译
     */
    public void testEnglishTranslation() {
        System.out.println("=== 英文翻译测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "system")
                .set("content", "你是一个专业的翻译助手，请将用户提供的英文准确翻译成中文。"));
        messages.add(Kv.by("role", "user")
                .set("content", "Please translate: 'The weather is beautiful today. Would you like to go for a walk in the park?'"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("生成的请求:");
        printFormattedJson(request);
        
        // 测试响应转换
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"今天天气很美。你想去公园散步吗？\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }],\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 25,\n" +
            "    \"candidatesTokenCount\": 15,\n" +
            "    \"totalTokenCount\": 40\n" +
            "  }\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 测试日文翻译
     */
    public void testJapaneseTranslation() {
        System.out.println("=== 日文翻译测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user")
                .set("content", "请翻译这段日文：「おはようございます。今日は会議がありますので、よろしくお願いします。」"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("生成的请求:");
        printFormattedJson(request);
        
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"早上好。今天有会议，请多关照。\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }]\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 测试韩文翻译
     */
    public void testKoreanTranslation() {
        System.out.println("=== 韩文翻译测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user")
                .set("content", "请将这段韩文翻译成中文：\"감사합니다. 오늘 정말 즐거웠어요. 다음에 또 만나요!\""));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("生成的请求:");
        printFormattedJson(request);
        
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"谢谢。今天真的很开心。下次再见！\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }]\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 测试单图片分析
     */
    public void testImageAnalysis() {
        System.out.println("=== 单图片分析测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        // 模拟图片处理
        List<String> imagePaths = Arrays.asList("menu.jpg");
        String prompt = "请分析这张餐厅菜单图片，识别其中的英文菜名并翻译成中文，同时描述菜单的整体布局。";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("图片处理结果:");
        printFormattedJson(JSON.toJSONString(parts));
        
        // 构建包含图片的消息
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("包含图片的请求:");
        printFormattedJson(request);
        
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"这是一张精美的餐厅菜单。菜单包含以下内容：\\n\\n开胃菜 (Appetizers):\\n- Caesar Salad → 凯撒沙拉\\n- Garlic Bread → 蒜蓉面包\\n\\n主菜 (Main Course):\\n- Grilled Salmon → 烤三文鱼\\n- Beef Steak → 牛排\\n\\n甜点 (Desserts):\\n- Chocolate Cake → 巧克力蛋糕\\n- Ice Cream → 冰淇淋\\n\\n菜单采用优雅的设计，黑色背景配金色文字，给人高档的感觉。\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }]\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 测试多图片对比分析
     */
    public void testMultiImageAnalysis() {
        System.out.println("=== 多图片对比分析测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        List<String> imagePaths = Arrays.asList("sign1.jpg", "sign2.jpg", "sign3.jpg");
        String prompt = "请对比这三张路标图片，识别并翻译所有英文标识，分析它们的设计风格差异。";
        
        Object parts = adapter.processImages(imagePaths, prompt);
        System.out.println("多图片处理结果:");
        printFormattedJson(JSON.toJSONString(parts));
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "user").set("content", parts));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("多图片请求:");
        printFormattedJson(request);
        
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"三张路标对比分析：\\n\\n图片1：\\n- 'Main Street' → 主街\\n- 'City Center 2km' → 市中心 2公里\\n- 设计：蓝底白字，现代简约风格\\n\\n图片2：\\n- 'Hospital' → 医院\\n- 'Emergency Exit' → 紧急出口\\n- 设计：绿底白字，国际标准医疗标识\\n\\n图片3：\\n- 'Tourist Information' → 旅游信息\\n- 'Welcome' → 欢迎\\n- 设计：棕底黄字，复古木质风格\\n\\n设计差异：三个标识分别代表交通指引、医疗设施和旅游服务，颜色和风格都符合各自的功能定位。\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }]\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 测试复杂翻译场景
     */
    public void testComplexTranslation() {
        System.out.println("=== 复杂翻译场景测试 ===");
        
        GeminiAdapter adapter = new GeminiAdapter();
        
        List<Kv> messages = new ArrayList<>();
        messages.add(Kv.by("role", "system")
                .set("content", "你是专业翻译，请保持原文的语气和风格。"));
        messages.add(Kv.by("role", "user")
                .set("content", "请翻译这段商务邮件：'Dear Mr. Johnson, I hope this email finds you well. I am writing to follow up on our discussion regarding the upcoming project. Could we schedule a meeting next week to finalize the details? Best regards, Sarah'"));
        
        String request = adapter.convertRequest("gemini-2.5-pro", messages);
        System.out.println("复杂翻译请求:");
        printFormattedJson(request);
        
        String mockResponse = "{\n" +
            "  \"candidates\": [{\n" +
            "    \"content\": {\n" +
            "      \"parts\": [{\n" +
            "        \"text\": \"亲爱的约翰逊先生，\\n\\n希望您一切安好。我写这封邮件是想跟进我们关于即将开展项目的讨论。我们能否安排下周开会来敲定细节？\\n\\n此致\\n敬礼\\n\\n莎拉\"\n" +
            "      }]\n" +
            "    },\n" +
            "    \"finishReason\": \"STOP\"\n" +
            "  }],\n" +
            "  \"usageMetadata\": {\n" +
            "    \"promptTokenCount\": 85,\n" +
            "    \"candidatesTokenCount\": 45,\n" +
            "    \"totalTokenCount\": 130\n" +
            "  }\n" +
            "}";
        
        String convertedResponse = adapter.convertResponse(mockResponse);
        System.out.println("转换后的响应:");
        printFormattedJson(convertedResponse);
        System.out.println();
    }
    
    /**
     * 格式化打印JSON
     */
    private void printFormattedJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            System.out.println(JSON.toJSONString(jsonObject, true));
        } catch (Exception e) {
            System.out.println(json);
        }
    }
}