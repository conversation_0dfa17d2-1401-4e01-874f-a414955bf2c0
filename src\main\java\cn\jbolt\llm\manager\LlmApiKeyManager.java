package cn.jbolt.llm.manager;

import cn.jbolt.common.model.LlmProvider;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * LLM API密钥管理器
 * 实现API密钥的轮询、负载均衡和频率限制
 */
public class LlmApiKeyManager {
    
    private static final LlmApiKeyManager INSTANCE = new LlmApiKeyManager();
    
    // 每个提供商的API密钥池
    private final Map<String, List<ApiKeyInfo>> providerKeyPools = new ConcurrentHashMap<>();
    
    // 每个提供商的轮询索引
    private final Map<String, AtomicInteger> providerIndexes = new ConcurrentHashMap<>();
    
    // 锁，用于同步操作
    private final ReentrantLock lock = new ReentrantLock();
    
    // API密钥使用统计
    private final Map<String, ApiKeyUsage> keyUsageMap = new ConcurrentHashMap<>();
    
    // 默认频率限制配置（每分钟请求数）
    private static final int DEFAULT_RATE_LIMIT_PER_MINUTE = 15;
    
    private LlmApiKeyManager() {
        // 初始化时加载所有API密钥
        loadApiKeys();
    }
    
    public static LlmApiKeyManager me() {
        return INSTANCE;
    }
    
    /**
     * API密钥信息
     */
    public static class ApiKeyInfo {
        private Long providerId;
        private String providerName;
        private String apiKey;
        private String apiSecret;
        private int rateLimitPerMinute;
        private boolean enabled;
        private int priority; // 优先级，数字越小优先级越高
        
        public ApiKeyInfo(Long providerId, String providerName, String apiKey, String apiSecret, 
                         int rateLimitPerMinute, boolean enabled, int priority) {
            this.providerId = providerId;
            this.providerName = providerName;
            this.apiKey = apiKey;
            this.apiSecret = apiSecret;
            this.rateLimitPerMinute = rateLimitPerMinute;
            this.enabled = enabled;
            this.priority = priority;
        }
        
        // Getters
        public Long getProviderId() { return providerId; }
        public String getProviderName() { return providerName; }
        public String getApiKey() { return apiKey; }
        public String getApiSecret() { return apiSecret; }
        public int getRateLimitPerMinute() { return rateLimitPerMinute; }
        public boolean isEnabled() { return enabled; }
        public int getPriority() { return priority; }
    }
    
    /**
     * API密钥使用统计
     */
    public static class ApiKeyUsage {
        private final String apiKey;
        private final Queue<Long> requestTimes; // 请求时间戳队列
        private int totalRequests;
        private long lastRequestTime;
        private boolean isBlocked;
        private long blockUntil;
        
        public ApiKeyUsage(String apiKey) {
            this.apiKey = apiKey;
            this.requestTimes = new LinkedList<>();
            this.totalRequests = 0;
            this.lastRequestTime = 0;
            this.isBlocked = false;
            this.blockUntil = 0;
        }
        
        /**
         * 检查是否可以发起请求
         */
        public synchronized boolean canMakeRequest(int rateLimitPerMinute) {
            long now = System.currentTimeMillis();
            
            // 检查是否在阻塞期
            if (isBlocked && now < blockUntil) {
                return false;
            } else if (isBlocked && now >= blockUntil) {
                isBlocked = false;
            }
            
            // 清理一分钟前的请求记录
            long oneMinuteAgo = now - 60 * 1000;
            while (!requestTimes.isEmpty() && requestTimes.peek() < oneMinuteAgo) {
                requestTimes.poll();
            }
            
            // 检查是否超过频率限制
            return requestTimes.size() < rateLimitPerMinute;
        }
        
        /**
         * 记录请求
         */
        public synchronized void recordRequest() {
            long now = System.currentTimeMillis();
            requestTimes.offer(now);
            totalRequests++;
            lastRequestTime = now;
        }
        
        /**
         * 标记为阻塞状态
         */
        public synchronized void markBlocked(long blockDurationMs) {
            this.isBlocked = true;
            this.blockUntil = System.currentTimeMillis() + blockDurationMs;
        }
        
        // Getters
        public String getApiKey() { return apiKey; }
        public int getCurrentMinuteRequests() { return requestTimes.size(); }
        public int getTotalRequests() { return totalRequests; }
        public long getLastRequestTime() { return lastRequestTime; }
        public boolean isBlocked() { return isBlocked; }
        public long getBlockUntil() { return blockUntil; }
    }
    
    /**
     * 从数据库加载所有API密钥
     */
    public void loadApiKeys() {
        lock.lock();
        try {
            LogKit.info("开始加载API密钥配置...");
            
            // 清空现有配置
            providerKeyPools.clear();
            providerIndexes.clear();
            
            // 从数据库查询所有启用的提供商
            String sql = "SELECT id, name, api_key, api_secret, priority, " +
                        "COALESCE(rate_limit_per_minute, ?) as rate_limit_per_minute " +
                        "FROM llm_provider WHERE status = 1 AND api_key IS NOT NULL AND api_key != '' " +
                        "ORDER BY name, priority";
            
            List<Record> records = Db.find(sql, DEFAULT_RATE_LIMIT_PER_MINUTE);
            
            for (Record record : records) {
                String providerName = record.getStr("name");
                Long providerId = record.getLong("id");
                String apiKey = record.getStr("api_key");
                String apiSecret = record.getStr("api_secret");
                int priority = record.getInt("priority");
                int rateLimitPerMinute = record.getInt("rate_limit_per_minute");
                
                // 检查是否是多个API密钥（用逗号分隔）
                String[] apiKeys = apiKey.split(",");
                String[] apiSecrets = apiSecret != null ? apiSecret.split(",") : new String[apiKeys.length];

                List<ApiKeyInfo> keyInfos = providerKeyPools.computeIfAbsent(providerName, k -> new ArrayList<>());

                for (int i = 0; i < apiKeys.length; i++) {
                    String key = apiKeys[i].trim();
                    String secret = null;
                    if (i < apiSecrets.length && apiSecrets[i] != null) {
                        secret = apiSecrets[i].trim();
                    }
                    
                    if (!key.isEmpty()) {
                        ApiKeyInfo keyInfo = new ApiKeyInfo(
                            providerId, providerName, key, secret, 
                            rateLimitPerMinute, true, priority
                        );
                        keyInfos.add(keyInfo);
                        
                        // 初始化使用统计
                        keyUsageMap.putIfAbsent(key, new ApiKeyUsage(key));
                    }
                }
                
                // 按优先级排序
                keyInfos.sort(Comparator.comparingInt(ApiKeyInfo::getPriority));
                
                // 初始化轮询索引
                providerIndexes.put(providerName, new AtomicInteger(0));
            }
            
            LogKit.info("API密钥加载完成，共加载 " + providerKeyPools.size() + " 个提供商的密钥池");
            for (Map.Entry<String, List<ApiKeyInfo>> entry : providerKeyPools.entrySet()) {
                LogKit.info("提供商 " + entry.getKey() + " 有 " + entry.getValue().size() + " 个API密钥");
            }
            
        } catch (Exception e) {
            LogKit.error("加载API密钥失败", e);
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 获取可用的API密钥（负载均衡）
     */
    public ApiKeyInfo getAvailableApiKey(String providerName) {
        List<ApiKeyInfo> keyInfos = providerKeyPools.get(providerName);
        if (keyInfos == null || keyInfos.isEmpty()) {
            LogKit.warn("提供商 " + providerName + " 没有配置API密钥");
            return null;
        }
        
        AtomicInteger index = providerIndexes.get(providerName);
        if (index == null) {
            index = new AtomicInteger(0);
            providerIndexes.put(providerName, index);
        }
        
        // 尝试找到可用的API密钥
        int attempts = 0;
        int maxAttempts = keyInfos.size() * 2; // 最多尝试两轮
        
        while (attempts < maxAttempts) {
            int currentIndex = index.getAndIncrement() % keyInfos.size();
            ApiKeyInfo keyInfo = keyInfos.get(currentIndex);
            
            if (!keyInfo.isEnabled()) {
                attempts++;
                continue;
            }
            
            ApiKeyUsage usage = keyUsageMap.get(keyInfo.getApiKey());
            if (usage != null && usage.canMakeRequest(keyInfo.getRateLimitPerMinute())) {
                // 记录请求
                usage.recordRequest();
                LogKit.debug("选择API密钥: " + maskApiKey(keyInfo.getApiKey()) + 
                           " (当前分钟请求数: " + usage.getCurrentMinuteRequests() + 
                           "/" + keyInfo.getRateLimitPerMinute() + ")");
                return keyInfo;
            }
            
            attempts++;
        }
        
        LogKit.warn("提供商 " + providerName + " 的所有API密钥都已达到频率限制");
        return null;
    }
    
    /**
     * 标记API密钥出错（临时阻塞）
     */
    public void markApiKeyError(String apiKey, long blockDurationMs) {
        ApiKeyUsage usage = keyUsageMap.get(apiKey);
        if (usage != null) {
            usage.markBlocked(blockDurationMs);
            LogKit.warn("API密钥 " + maskApiKey(apiKey) + " 被标记为阻塞状态，持续 " + blockDurationMs + "ms");
        }
    }
    
    /**
     * 获取API密钥使用统计
     */
    public Map<String, ApiKeyUsage> getUsageStatistics() {
        return new HashMap<>(keyUsageMap);
    }
    
    /**
     * 获取提供商的所有API密钥信息
     */
    public List<ApiKeyInfo> getProviderKeys(String providerName) {
        return providerKeyPools.getOrDefault(providerName, new ArrayList<>());
    }
    
    /**
     * 掩码显示API密钥（用于日志）
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        LogKit.info("重新加载API密钥配置");
        loadApiKeys();
    }
}
