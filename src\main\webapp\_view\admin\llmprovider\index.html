#@jboltLayout()
#define main()
#set(pageId=RandomUtil.random(6))
<div class="jbolt_page" data-key="#(pmkey??)">
<div class="jbolt_page_title">
<div class="row">
	<div class="col-sm-auto"><h1><i class="jbicon2 jbi-appstore"></i>大模型提供商管理</h1></div>
	<div class="col">
	<form class="form-inline" id="LlmProvider_form_#(pageId)">
		<input type="text" autocomplete="off"  class="form-control"  placeholder="输入关键字搜索" name="keywords" value="" />

<select class="form-control"
	name="apiType"
	data-autoload
	data-url="admin/dictionary/options?key=null"
	data-select-type="select"
	data-text="=API类型(openai/custom)="
	data-value=""
	data-value-attr="sn"
	data-refresh="true"
	></select>

<select class="form-control"
	name="status"
	data-autoload
	data-url="admin/dictionary/options?key=options_boolean"
	data-select-type="select"
	data-text="=状态(0-禁用,1-启用)="
	data-value=""
	data-value-attr="sn"
	data-refresh="true"
	></select>
		<div class="btn-group text-center mx-1">
			<button type="submit" class="btn btn-outline-primary" ><i class="fa fa-search"></i> 查询</button>
			<button type="button" onclick="form.reset();refreshJBoltTable(this);" class="btn btn-outline-secondary" ><i class="fa fa-reply-all"></i> 重置</button>
		</div>
	</form>
	</div>
	<div class="col-sm-auto text-right">
	
	<button data-dialogbtn class="btn btn-outline-primary btn-sm" data-url="admin/llmProvider/add" data-handler="jboltTablePageToFirst" data-area="800,600" tooltip data-title="新增大模型提供商"><i class="fa fa-plus"></i></button>
	<button class="btn btn-outline-info btn-sm" onclick="refreshJBoltTable(this)" tooltip data-title="刷新数据"><i class="fa fa-refresh"></i></button>
	
	
	</div>
</div>
</div>
<div class="jbolt_page_content">
<!-- 定义JBoltTable使用的数据渲染模板 -->
<textarea class="jb_tpl_box" id="LlmProvider_tpl_#(pageId)">
{@each datas as data,index}
<tr data-id="${data.id}">
<td>${pageNumber,pageSize,index | rownum}</td>
<td>${data.name}</td>
<td>${data.apiBaseUrl}</td>
<td>${data.apiKey}</td>
<td>${data.apiSecret}</td>
<td>${data.apiType}</td>
<td>${data.adapterClass}</td>
<td>${data.requestTimeout}</td>
<td>${data.maxTokens}</td>
<td>${data.defaultModel}</td>
<td><img data-switchbtn data-confirm="确定切换状态(0-禁用,1-启用)？" data-value="${data.status}"  data-handler="refreshJBoltTable"  data-url="admin/llmProvider/toggleStatus/${data.id}"/></td>
<td>${data.priority}</td>
<td>${data.rateLimitPerMinute}/分钟</td>
<td>${data.remark}</td>
<td>$${data.createTime|date_ymdhms}</td>
<td>$${data.updateTime|date_ymdhms}</td>
<td>
	<a class="jbolt_table_editbtn" href="admin/llmProvider/edit/${data.id}" data-handler="refreshJBoltTable" data-area="800,600" data-title="编辑大模型提供商"><i class="fa fa-edit"></i></a>
	<a class="jbolt_table_delbtn" href="admin/llmProvider/delete/${data.id}" data-handler="refreshJBoltTable" ><i class="fa fa-trash c-danger"></i></a>
	<a class="btn btn-sm btn-outline-primary" onclick="showModels('${data.id}', '${data.name}')" tooltip data-title="管理模型"><i class="fa fa-list"></i></a>
</td>
</tr>
{@/each}
</textarea>
<table class="jbolt_table jbolt_main_table  table-center "
	   data-jbolttable
	   data-width="fill"
	   data-height="fill"
	   data-ajax="true"
	   data-conditions-form="LlmProvider_form_#(pageId)"
	   data-url="admin/llmProvider/datas"
	   data-rowtpl="LlmProvider_tpl_#(pageId)"
	   data-copy-to-excel="false"
	   data-page="LlmProvider_page"
	   data-column-resize="true"
	   data-sortable-columns="create_time,update_time"
	   data-sort="#((sortColumn&&sortType)?(sortColumn+':'+sortType):'')"
	   data-default-sort-column="id"
	   data-fixed-columns-left="1,2"
	   data-fixed-columns-right="-1"
>
<thead class="fw-normal" >
<tr>
<th data-width="60" data-column="index">序号</th>
	<th  data-width="150" data-column="name">厂商名称</th>
	<th  data-width="150" data-column="api_base_url">API基础URL</th>
	<th  data-width="150" data-column="api_key">API密钥</th>
	<th  data-width="150" data-column="api_secret">API密钥(可选)</th>
	<th  data-width="150" data-column="api_type">API类型(openai/custom)</th>
	<th  data-width="150" data-column="adapter_class">适配器类名(非OpenAI兼容时需要)</th>
	<th  data-width="100" data-column="request_timeout">请求超时时间(秒)</th>
	<th  data-width="100" data-column="max_tokens">默认最大token数</th>
	<th  data-width="150" data-column="default_model">默认模型标识</th>
	<th  data-width="100" data-column="status">状态(0-禁用,1-启用)</th>
	<th  data-width="100" data-column="priority">优先级(数字越小优先级越高)</th>
	<th  data-width="120" data-column="rate_limit_per_minute">频率限制</th>
	<th  data-width="150" data-column="remark">备注</th>
	<th  data-width="180" data-column="create_time">创建时间</th>
	<th  data-width="180" data-column="update_time">更新时间</th>
<th data-width="120">操作</th>
</tr>
</thead>
<tbody></tbody>
</table>

<!-- 模型管理对话框 -->
<div id="modelDialog" style="display:none;">
    <div class="container-fluid p-3">
        <div class="row mb-3 align-items-center">
            <div class="col">
                <h4 id="modelDialogTitle" class="m-0 text-primary">模型管理</h4>
            </div>
            <div class="col-auto">
                <button id="addModelBtn" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> 添加模型</button>
            </div>
        </div>
        
        <!-- 模型列表 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0" id="modelTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="40" class="text-center">序号</th>
                                        <th width="150">模型名称</th>
                                        <th width="150">模型标识符</th>
                                        <th width="100" class="text-center">最大Token</th>
                                        <th width="100" class="text-center">输入价格</th>
                                        <th width="100" class="text-center">输出价格</th>
                                        <th width="100" class="text-center">上下文长度</th>
                                        <th width="100" class="text-center">能力</th>
                                        <th width="80" class="text-center">状态</th>
                                        <th width="120" class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 模型数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-muted small">
                    <i class="fa fa-info-circle"></i> 模型配置用于定义大模型提供商支持的各种模型及其参数。
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
#end

#define js()
<script type="text/javascript">
// 当前选中的提供商ID
var currentProviderId = null;
var currentProviderName = null;

// 打开添加模型对话框
function openAddModelDialog(providerId) {
    layer.open({
        type: 2,
        title: '添加模型',
        shadeClose: true,
        shade: 0.4,  // 降低遮罩层透明度
        area: ['800px', '600px'],
        content: 'admin/llmModel/add/' + providerId + '?_jb_rqtype_=dialog',  // 添加对话框模式参数
        end: function() {
            loadModels(providerId);
        }
    });
}

// 打开编辑模型对话框
function openEditModelDialog(modelId) {
    layer.open({
        type: 2,
        title: '编辑模型',
        shadeClose: true,
        shade: 0.4,  // 降低遮罩层透明度
        area: ['800px', '600px'],
        content: 'admin/llmModel/edit/' + modelId + '?_jb_rqtype_=dialog',  // 添加对话框模式参数
        end: function() {
            loadModels(currentProviderId);
        }
    });
}

// 显示模型管理对话框
function showModels(providerId, providerName) {
    currentProviderId = providerId;
    currentProviderName = providerName;

    // 设置对话框标题
    $("#modelDialogTitle").text(providerName + " - 模型管理");
    
    // 设置添加模型按钮的点击事件
    $("#addModelBtn").off('click').on('click', function() {
        openAddModelDialog(providerId);
    });

    // 打开对话框
    layer.open({
        type: 1,
        title: false,
        closeBtn: 1,
        shadeClose: true,
        shade: 0,  // 设置为0，移除遮罩层
        area: ['1000px', '600px'],
        content: $('#modelDialog'),
        success: function() {
            loadModels(providerId);
        }
    });
}

// 加载模型列表
function loadModels(providerId) {
    $.ajax({
        url: 'admin/llmModel/datas',
        type: 'GET',
        data: {
            providerId: providerId,
            pageNumber: 1,
            pageSize: 100
        },
        success: function(response) {
            renderModels(response.data.list);
        },
        error: function() {
            layer.msg('加载模型列表失败', {icon: 2});
        }
    });
}

// 渲染模型列表
function renderModels(models) {
    var html = '';
    
    if (models && models.length > 0) {
        $.each(models, function(index, model) {
            html += '<tr data-id="' + model.id + '">';
            html += '<td class="text-center">' + (index + 1) + '</td>';
            html += '<td><strong>' + model.modelName + '</strong></td>';
            html += '<td><code>' + model.modelIdentifier + '</code></td>';
            html += '<td class="text-center">' + (model.maxTokens ? model.maxTokens.toLocaleString() : '-') + '</td>';
            html += '<td class="text-center">' + (model.inputPrice ? parseFloat(model.inputPrice).toFixed(6) : '0.000000') + '</td>';
            html += '<td class="text-center">' + (model.outputPrice ? parseFloat(model.outputPrice).toFixed(6) : '0.000000') + '</td>';
            html += '<td class="text-center">' + (model.contextLength ? model.contextLength.toLocaleString() : '-') + '</td>';
            
            // 能力标签处理
            var capabilitiesHtml = '';
            if (model.capabilities) {
                var caps = model.capabilities.split(',');
                for (var i = 0; i < caps.length; i++) {
                    var capClass = '';
                    var capIcon = '';
                    
                    switch(caps[i].trim()) {
                        case 'text':
                            capClass = 'badge-info';
                            capIcon = 'fa-file-text-o';
                            break;
                        case 'image':
                            capClass = 'badge-success';
                            capIcon = 'fa-image';
                            break;
                        case 'audio':
                            capClass = 'badge-warning';
                            capIcon = 'fa-volume-up';
                            break;
                        case 'embedding':
                            capClass = 'badge-primary';
                            capIcon = 'fa-code';
                            break;
                        default:
                            capClass = 'badge-secondary';
                            capIcon = 'fa-cog';
                    }
                    
                    capabilitiesHtml += '<span class="badge ' + capClass + ' mr-1"><i class="fa ' + capIcon + '"></i> ' + caps[i].trim() + '</span>';
                }
            } else {
                capabilitiesHtml = '<span class="badge badge-info"><i class="fa fa-file-text-o"></i> text</span>';
            }
            
            html += '<td class="text-center">' + capabilitiesHtml + '</td>';
            html += '<td class="text-center">' + (model.status ? '<span class="badge badge-success">启用</span>' : '<span class="badge badge-secondary">禁用</span>') + '</td>';
            html += '<td class="text-center">';
            html += '<button class="btn btn-sm btn-outline-primary mr-1" onclick="openEditModelDialog(' + model.id + ')" tooltip data-title="编辑"><i class="fa fa-edit"></i></button>';
            html += '<button class="btn btn-sm btn-outline-danger" onclick="deleteModel(' + model.id + ')" tooltip data-title="删除"><i class="fa fa-trash"></i></button>';
            html += '</td>';
            html += '</tr>';
        });
    } else {
        html = '<tr><td colspan="10" class="text-center py-4"><i class="fa fa-info-circle text-muted"></i> 暂无模型数据，请点击"添加模型"按钮添加。</td></tr>';
    }
    
    $('#modelTable tbody').html(html);
}

// 删除模型
function deleteModel(modelId) {
    layer.confirm('确定要删除此模型吗？', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        $.ajax({
            url: 'admin/llmModel/delete/' + modelId,
            type: 'POST',
            success: function(response) {
                if (response.state === 'ok') {
                    layer.msg('删除成功', {icon: 1});
                    loadModels(currentProviderId);
                } else {
                    layer.msg(response.msg || '删除失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('删除请求失败', {icon: 2});
            }
        });
        
        layer.close(index);
    });
}

// 刷新模型表格
function refreshLlmModelTable() {
    if (currentProviderId) {
        loadModels(currentProviderId);
    }
}
</script>
#end
