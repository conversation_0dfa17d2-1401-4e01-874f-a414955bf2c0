# Gemini API密钥快速修复指南

## 问题诊断

根据后台日志：
```
[WARN] 提供商 gemini 没有配置API密钥
[WARN] 提供商 gemini 没有可用的API密钥，尝试次数: 1/2/3
[ERROR] 提供商 gemini 所有API密钥都不可用
```

**根本原因**：API密钥没有正确存储在数据库的`llm_provider`表中。

## 你的API密钥

你有6个有效的Gemini API密钥：
```
AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y
AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo
AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ
AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8
AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU
AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog
```

## 快速修复步骤

### 步骤1：执行SQL修复
在数据库中执行以下SQL：

```sql
-- 检查当前gemini提供商配置
SELECT id, name, api_key, status FROM llm_provider WHERE name = 'gemini';

-- 更新API密钥（如果gemini提供商已存在）
UPDATE llm_provider 
SET 
    api_key = 'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    status = 1,
    rate_limit_per_minute = 15
WHERE name = 'gemini';

-- 如果gemini提供商不存在，创建它
INSERT IGNORE INTO llm_provider (
    name, 
    api_type, 
    api_base_url, 
    api_key,
    status, 
    priority, 
    default_model, 
    adapter_class,
    rate_limit_per_minute
) VALUES (
    'gemini', 
    'gemini', 
    'https://generativelanguage.googleapis.com/v1beta/models/', 
    'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    1, 
    1, 
    'gemini-2.5-pro', 
    'cn.jbolt.llm.adapter.GeminiAdapter',
    15
);

-- 验证配置
SELECT 
    id,
    name,
    CASE 
        WHEN api_key IS NOT NULL AND api_key != '' THEN CONCAT('已配置(', LENGTH(api_key) - LENGTH(REPLACE(api_key, ',', '')) + 1, '个密钥)')
        ELSE '未配置'
    END as api_key_status,
    status,
    rate_limit_per_minute
FROM llm_provider 
WHERE name = 'gemini';
```

### 步骤2：重新加载API密钥配置

有两种方式重新加载配置：

#### 方式1：重启应用（推荐）
重启你的应用服务器，系统会自动加载新的API密钥配置。

#### 方式2：程序内重新加载
如果不想重启，可以通过代码重新加载：

```java
// 在任何Controller或Service中调用
LlmApiKeyManager.me().reload();
```

或者创建一个临时的管理接口：

```java
@Controller
public class TempApiKeyController {
    public void reloadApiKeys() {
        LlmApiKeyManager.me().reload();
        renderJsonSuccess("API密钥配置已重新加载");
    }
}
```

### 步骤3：验证修复结果

1. **检查日志**：
   - 应该看到类似 `API密钥加载完成，共加载 1 个提供商的密钥池` 的日志
   - 应该看到 `提供商 gemini 有 6 个API密钥` 的日志

2. **测试翻译功能**：
   - 访问 `/admin/email/translation/config`
   - 选择 gemini 提供商和 gemini-2.5-pro 模型
   - 输入测试文本并点击"运行测试"

3. **预期结果**：
   ```
   提供商: gemini
   模型: gemini-2.5-pro
   提示词: 使用AI提示词
   翻译结果: 你好，这是一条测试翻译消息。
   响应时间: 1200ms
   状态: 成功
   ```

## 系统工作原理

### API密钥存储位置
- **正确位置**：`llm_provider` 表的 `api_key` 字段
- **错误位置**：`llm_api_key` 表（这个表是用于统计的，不是配置表）

### 多密钥支持
- 系统支持在一个字段中存储多个API密钥
- 使用逗号分隔：`key1,key2,key3`
- 系统会自动解析并实现轮询负载均衡

### 轮询机制
- 每次请求会轮询使用不同的API密钥
- 如果某个密钥达到频率限制，会自动切换到下一个
- 支持优先级设置（priority字段）

## 故障排除

### 如果修复后仍然失败

1. **检查网络连接**：
   ```bash
   curl -I https://generativelanguage.googleapis.com/v1beta/models/
   ```

2. **验证API密钥有效性**：
   - 登录 [Google AI Studio](https://aistudio.google.com/app/apikey)
   - 检查API密钥状态和配额

3. **检查数据库配置**：
   ```sql
   SELECT * FROM llm_provider WHERE name = 'gemini';
   SELECT * FROM llm_model WHERE provider_id = (SELECT id FROM llm_provider WHERE name = 'gemini');
   ```

4. **查看详细日志**：
   - 启用DEBUG级别日志：`log4j.logger.cn.jbolt.llm=DEBUG`
   - 查看具体的API调用错误信息

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|---------|------|----------|
| 没有配置API密钥 | api_key字段为空 | 执行UPDATE SQL设置密钥 |
| 所有API密钥都不可用 | 密钥无效或达到限制 | 检查密钥有效性和配额 |
| 找不到提供商 | gemini记录不存在 | 执行INSERT SQL创建记录 |
| 找不到模型 | gemini-2.5-pro模型不存在 | 检查llm_model表配置 |

## 验证清单

修复完成后，请确认以下项目：

- [ ] `llm_provider` 表中存在 name='gemini' 的记录
- [ ] `api_key` 字段包含6个逗号分隔的密钥
- [ ] `status` 字段为 1（启用）
- [ ] `rate_limit_per_minute` 字段有合理值（如15）
- [ ] 应用已重启或调用了reload()方法
- [ ] 翻译测试返回成功结果
- [ ] 日志中显示"提供商 gemini 有 6 个API密钥"

完成这些步骤后，你的Gemini API密钥应该能正常工作，支持6个密钥的轮询负载均衡！
