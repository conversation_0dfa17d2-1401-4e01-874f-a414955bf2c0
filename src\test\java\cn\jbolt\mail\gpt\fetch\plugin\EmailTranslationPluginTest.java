package cn.jbolt.mail.gpt.fetch.plugin;

import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.common.model.EmailTranslation;
import cn.jbolt.mail.gpt.InitEnv;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import cn.jbolt.mail.gpt.util.HtmlScreenshotUtil;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Scanner;

/**
 * EmailTranslationPlugin 测试类
 * 用于测试邮件翻译插件的功能
 */
public class EmailTranslationPluginTest {

    private EmailTranslationPlugin plugin;
    private boolean isRunning = false;

    @BeforeEach
    public void setup() {
        // 清理环境，避免多个测试重复初始化导致的配置冲突
        InitEnv.initEnvironment();
        
        // 创建插件实例
        plugin = new EmailTranslationPlugin();
    }

    @AfterEach
    public void tearDown() {
        // 如果插件正在运行，停止它
        if (isRunning) {
            plugin.stop();
            isRunning = false;
        }
        
        // 清理环境，确保不影响下一个测试
        InitEnv.cleanEnvironment();
    }

    /**
     * 测试启动和停止插件
     */
    @Test
    public void testStartAndStop() {
        System.out.println("测试启动和停止插件");
        
        // 启动插件
        boolean startResult = plugin.start();
        isRunning = startResult;
        System.out.println("插件启动结果: " + startResult);
        
        // 等待一段时间
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // 停止插件
        boolean stopResult = plugin.stop();
        isRunning = !stopResult;
        System.out.println("插件停止结果: " + stopResult);
    }

    /**
     * 测试手动触发邮件翻译处理
     */
    @Test
    public void testTriggerProcessRecentEmails() {
        System.out.println("测试手动触发邮件翻译处理");
        
        // 启动插件
        boolean startResult = plugin.start();
        isRunning = startResult;
        System.out.println("插件启动结果: " + startResult);
        
        // 手动触发处理
        plugin.triggerProcessRecentEmails();
        
        // 等待处理完成
        try {
            System.out.println("等待处理完成...");
            Thread.sleep(30000); // 等待30秒
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // 停止插件
        boolean stopResult = plugin.stop();
        isRunning = !stopResult;
        System.out.println("插件停止结果: " + stopResult);
    }

    /**
     * 测试查询最近邮件
     */
    @Test
    public void testQueryRecentEmails() {
        System.out.println("测试查询最近邮件");
        
        // 计算最近几天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -2);
        Date startDate = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 查询最近几天的客户邮件，且不在翻译表中的
        String sql = "SELECT m.* FROM email_messages m " +
                "LEFT JOIN email_translation t ON m.id = t.email_id " +
                "WHERE m.sent_date >= ? " +
                "AND m.folder_name = 'INBOX' " +  // 只处理收件箱
                "AND t.id IS NULL " +  // 不在翻译表中
                "AND m.from_address NOT LIKE '%@linstone.cn' " +  // 排除自己公司的邮件
                "ORDER BY m.sent_date DESC " +
                "LIMIT 10";  // 最多显示10封邮件
        
        List<Record> emails = Db.find(sql, sdf.format(startDate));
        
        System.out.println("找到 " + emails.size() + " 封需要翻译的邮件");
        
        // 显示邮件信息
        for (Record email : emails) {
            System.out.println("ID: " + email.get("id") + 
                    ", 主题: " + email.getStr("subject") + 
                    ", 发件人: " + email.getStr("from_address") + 
                    ", 发送时间: " + email.getDate("sent_date") + 
                    ", 获取状态: " + email.getInt("fetch_status"));
        }
    }

    /**
     * 测试HTML截图功能
     */
    @Test
    public void testHtmlScreenshot() {
        System.out.println("测试HTML截图功能");
        
        // 简单的HTML内容
        String htmlContent = "<html><body><h1>测试HTML截图</h1><p>这是一个测试内容，用于验证HTML截图功能是否正常工作。</p></body></html>";
        
        // 截图保存路径
        String screenshotPath = PropKit.get("email_screenshot_folder", "d:/attachments/screenshots/").replace("\\\\", "/").replace("\\", "/").replaceAll("/$", "") + "/";
        
        // 生成截图
        String filePath = HtmlScreenshotUtil.generateScreenshot(htmlContent, screenshotPath, "test.png");
        
        if (filePath != null) {
            System.out.println("截图生成成功: " + filePath);
            System.out.println("文件是否存在: " + new File(filePath).exists());
        } else {
            System.out.println("截图生成失败");
        }
    }

    /**
     * 测试获取完整邮件内容
     */
    @Test
    public void testFetchCompleteEmail() {
        System.out.println("测试获取完整邮件内容");
        
        // 查询一封fetch_status=0的邮件
        EmailMessages email = new EmailMessages().dao().findFirst(
                "SELECT * FROM email_messages WHERE fetch_status = 0 LIMIT 1");
        
        if (email == null) {
            System.out.println("未找到fetch_status=0的邮件，测试结束");
            return;
        }
        
        System.out.println("找到邮件: ID=" + email.getId() + 
                ", 主题=" + email.getSubject() + 
                ", 发件人=" + email.getFromAddress());
        
        // 获取邮箱账号
        String emailAccount = email.getEmailAccount();
        EmailAccount account = new EmailAccount().dao().findFirst(
                "SELECT * FROM email_account WHERE username = ?", emailAccount);
        
        if (account == null) {
            System.out.println("找不到邮箱账号: " + emailAccount + "，测试结束");
            return;
        }
        
        // 获取邮件客户端
        EmailClient client = EmailClientPool.getInstance().getClient(account, EmailClient.Mode.COMPLETE_MODE);
        
        // 连接邮件服务器
        if (!client.connect()) {
            System.out.println("连接邮箱 " + account.getUsername() + " 失败，测试结束");
            return;
        }
        
        // 获取指定邮件的完整内容
        boolean success = client.fetchEmailByEmailMessages(email);
        
        if (success) {
            System.out.println("成功获取邮件 " + email.getId() + " 的完整内容");
            // 重新从数据库获取更新后的邮件内容
            email = new EmailMessages().dao().findById(email.getId());
            System.out.println("更新后的fetch_status: " + email.getFetchStatus());
            System.out.println("HTML内容长度: " + (email.getContentHtml() != null ? email.getContentHtml().length() : 0));
        } else {
            System.out.println("获取邮件 " + email.getId() + " 的完整内容失败");
        }
    }

    /**
     * 测试翻译单封邮件
     */
    @Test
    public void testTranslateSingleEmail() {
        System.out.println("测试翻译单封邮件");
        
        // 查询一封未翻译的邮件
        String sql = "SELECT m.* FROM email_messages m " +
                "LEFT JOIN email_translation t ON m.id = t.email_id " +
                "WHERE t.id IS NULL " +  // 不在翻译表中
                "AND m.fetch_status = 1 " +  // 已获取完整内容
                "AND m.from_address NOT LIKE '%@linstone.cn' " +  // 排除自己公司的邮件
                "LIMIT 1";
        
        EmailMessages email = new EmailMessages().dao().findFirst(sql);
        
        if (email == null) {
            System.out.println("未找到符合条件的邮件，测试结束");
            return;
        }
        
        System.out.println("找到邮件: ID=" + email.getId() + 
                ", 主题=" + email.getSubject() + 
                ", 发件人=" + email.getFromAddress());
        
        // 启动插件
        boolean startResult = plugin.start();
        isRunning = startResult;
        System.out.println("插件启动结果: " + startResult);
        
        // 手动处理该邮件
        try {
            // 使用反射调用私有方法processEmail
            java.lang.reflect.Method processEmailMethod = EmailTranslationPlugin.class.getDeclaredMethod("processEmail", EmailMessages.class);
            processEmailMethod.setAccessible(true);
            processEmailMethod.invoke(plugin, email);
            
            // 查询翻译结果
            EmailTranslation translation = new EmailTranslation().dao().findFirst(
                    "SELECT * FROM email_translation WHERE email_id = ?", email.getId());
            
            if (translation != null) {
                System.out.println("翻译成功: ID=" + translation.getId());
                System.out.println("翻译内容: " + translation.getTranslatedContent());
                System.out.println("截图路径: " + translation.getScreenshotPath());
                System.out.println("截图文件是否存在: " + new File(translation.getScreenshotPath()).exists());
            } else {
                System.out.println("翻译失败，未找到翻译记录");
            }
        } catch (Exception e) {
            System.out.println("处理邮件失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 停止插件
        boolean stopResult = plugin.stop();
        isRunning = !stopResult;
        System.out.println("插件停止结果: " + stopResult);
    }

    /**
     * 测试修改后的Gemini翻译功能
     */
    @Test
    public void testGeminiTranslation() {
        System.out.println("测试修改后的Gemini翻译功能");

        // 查询一封未翻译的邮件
        String sql = "SELECT m.* FROM email_messages m " +
                "LEFT JOIN email_translation t ON m.id = t.email_id " +
                "WHERE t.id IS NULL " +  // 不在翻译表中
                "AND m.fetch_status = 1 " +  // 已获取完整内容
                "AND m.from_address NOT LIKE '%@linstone.cn' " +  // 排除自己公司的邮件
                "LIMIT 1";

        EmailMessages email = new EmailMessages().dao().findFirst(sql);

        if (email == null) {
            System.out.println("未找到符合条件的邮件，创建测试邮件");

            // 创建测试邮件
            email = new EmailMessages();
            email.setMessageId("test-gemini-" + System.currentTimeMillis());
            email.setSubject("Test Email Subject - Hello World from Gemini Test");
            email.setFromAddress("<EMAIL>");
            email.setToAddress("<EMAIL>");
            // 注意：EmailMessages可能没有setContent方法，使用setContentText代替
            email.setContentText("Hello, this is a test email content for Gemini translation. Please translate this message to Chinese. This email contains important business information that needs to be processed.");
            email.setContentHtml("<html><body><p>Hello, this is a test email content for Gemini translation.</p><p>Please translate this message to Chinese.</p><p>This email contains important business information that needs to be processed.</p></body></html>");
            email.setSentDate(new Date());
            email.setReceivedDate(new Date());
            email.setFolderName("INBOX");
            email.setFetchStatus(1); // 已获取完整内容
            email.setCreatedAt(new Date());
            email.save();

            System.out.println("创建测试邮件成功，ID: " + email.getId());
        }

        System.out.println("测试邮件: ID=" + email.getId() +
                ", 主题=" + email.getSubject() +
                ", 发件人=" + email.getFromAddress());

        // 启动插件
        boolean startResult = plugin.start();
        isRunning = startResult;
        System.out.println("插件启动结果: " + startResult);

        if (!startResult) {
            System.out.println("插件启动失败，测试结束");
            return;
        }

        // 直接调用翻译方法
        try {
            System.out.println("开始翻译邮件...");
            String result = plugin.translateEmail(email.getId(), false); // 不翻译图片

            if (result != null && !result.trim().isEmpty()) {
                System.out.println("✓ 翻译成功");
                System.out.println("翻译结果长度: " + result.length() + " 字符");
                System.out.println("翻译结果预览: " + result.substring(0, Math.min(300, result.length())) + "...");

                // 检查数据库中的翻译记录
                EmailTranslation translation = new EmailTranslation().dao().findFirst(
                        "SELECT * FROM email_translation WHERE email_id = ?", email.getId());

                if (translation != null) {
                    System.out.println("✓ 翻译记录已保存到数据库");
                    System.out.println("翻译标题: " + translation.getSubjectTranslated());
                    System.out.println("翻译内容长度: " + (translation.getContentTranslated() != null ?
                                     translation.getContentTranslated().length() : 0) + " 字符");
                    System.out.println("创建时间: " + translation.getCreateTime());
                } else {
                    System.out.println("✗ 翻译记录未保存到数据库");
                }
            } else {
                System.out.println("✗ 翻译失败或返回空结果");
            }

        } catch (Exception e) {
            System.out.println("翻译过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }

        // 停止插件
        boolean stopResult = plugin.stop();
        isRunning = !stopResult;
        System.out.println("插件停止结果: " + stopResult);
    }

    /**
     * 交互式测试
     */
    public static void main(String[] args) {
        EmailTranslationPluginTest test = new EmailTranslationPluginTest();
        test.setup();
        
        Scanner scanner = new Scanner(System.in);
        boolean running = true;
        
        while (running) {
            System.out.println("\n=== 邮件翻译插件测试 ===");
            System.out.println("1. 启动插件");
            System.out.println("2. 停止插件");
            System.out.println("3. 手动触发处理");
            System.out.println("4. 查询最近邮件");
            System.out.println("5. 测试HTML截图");
            System.out.println("6. 测试获取完整邮件");
            System.out.println("7. 测试翻译单封邮件");
            System.out.println("8. 测试Gemini翻译功能");
            System.out.println("0. 退出");
            System.out.print("请选择: ");
            
            String choice = scanner.nextLine();
            
            try {
                switch (choice) {
                    case "1":
                        boolean startResult = test.plugin.start();
                        test.isRunning = startResult;
                        System.out.println("插件启动结果: " + startResult);
                        break;
                    case "2":
                        boolean stopResult = test.plugin.stop();
                        test.isRunning = !stopResult;
                        System.out.println("插件停止结果: " + stopResult);
                        break;
                    case "3":
                        if (!test.isRunning) {
                            System.out.println("插件未启动，请先启动插件");
                            break;
                        }
                        test.plugin.triggerProcessRecentEmails();
                        System.out.println("已触发处理，请等待处理完成");
                        break;
                    case "4":
                        test.testQueryRecentEmails();
                        break;
                    case "5":
                        test.testHtmlScreenshot();
                        break;
                    case "6":
                        test.testFetchCompleteEmail();
                        break;
                    case "7":
                        test.testTranslateSingleEmail();
                        break;
                    case "8":
                        test.testGeminiTranslation();
                        break;
                    case "0":
                        running = false;
                        if (test.isRunning) {
                            test.plugin.stop();
                        }
                        break;
                    default:
                        System.out.println("无效选择，请重新输入");
                }
            } catch (Exception e) {
                System.out.println("操作失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
        System.out.println("测试结束");
    }
}
