package cn.jbolt.admin.emailmessages;

import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.mail.gpt.InitEnv;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

/**
 * 邮件过滤功能增强测试
 * 测试发件人和收件人过滤器是否支持邮箱地址和显示名称
 */
public class EmailFilterEnhancementTest {

    private static EmailMessagesService emailMessagesService;
    private static final Long TEST_USER_ID = 1L;

    public static void main(String[] args) {
        InitEnv.initEnvironment();
        emailMessagesService = new EmailMessagesService();

        System.out.println("=== Email Filter Enhancement Test ===");

        // 设置测试用户
        JBoltUserKit.setUserId(TEST_USER_ID);

        // 准备测试数据
        prepareTestData();

        // 运行测试
        testSenderFilterByEmailAddress();
        testSenderFilterByDisplayName();
        testRecipientFilterByEmailAddress();
        testRecipientFilterByDisplayName();
        testBackwardCompatibility();

        // 清理测试数据
        cleanTestData();

        System.out.println("=== Test Complete ===");
    }

    /**
     * 准备测试数据
     */
    public static void prepareTestData() {
        System.out.println("Preparing test data...");

        // Clean existing test data
        cleanTestData();

        // Insert test email data
        String insertSql = "INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, " +
                          "from_display, from_address, to_display, to_address, folder_name, sent_date, created_at, is_delete) VALUES " +
                          "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 0)";
        
        // Test email 1: Chinese display name sender
        Db.update(insertSql, 9999001L, "<EMAIL>", "test-msg-001", "hash001", "Test Email 1",
                 "YiLin*Lin", "<EMAIL>", "Test Recipient", "<EMAIL>", "INBOX");

        // Test email 2: English display name sender
        Db.update(insertSql, 9999002L, "<EMAIL>", "test-msg-002", "hash002", "Test Email 2",
                 "John Smith", "<EMAIL>", "Test User", "<EMAIL>", "INBOX");

        // Test email 3: No display name, email address only
        Db.update(insertSql, 9999003L, "<EMAIL>", "test-msg-003", "hash003", "Test Email 3",
                 "", "<EMAIL>", "", "<EMAIL>", "INBOX");

        System.out.println("Test data preparation complete");
    }

    /**
     * 清理测试数据
     */
    public static void cleanTestData() {
        Db.update("DELETE FROM email_messages WHERE id BETWEEN 9999001 AND 9999999");
    }

    /**
     * Test sender filter - by email address
     */
    public static void testSenderFilterByEmailAddress() {
        System.out.println("\nTesting sender filter - by email address...");

        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("senderFilter", "<EMAIL>")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);

        Ret result = emailMessagesService.getDashboardEmails(1, 100, params);

        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            boolean found = emails.stream().anyMatch(email ->
                "<EMAIL>".equals(email.getStr("from_address")));

            if (found) {
                System.out.println("PASS - Sender email address filter test passed");
            } else {
                System.out.println("FAIL - Sender email address filter test failed - no matching emails found");
            }
        } else {
            System.out.println("FAIL - Sender email address filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test sender filter - by display name
     */
    public static void testSenderFilterByDisplayName() {
        System.out.println("\nTesting sender filter - by display name...");

        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("senderFilter", "YiLin*Lin")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);

        Ret result = emailMessagesService.getDashboardEmails(1, 100, params);

        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            boolean found = emails.stream().anyMatch(email ->
                "YiLin*Lin".equals(email.getStr("from_display")) ||
                "<EMAIL>".equals(email.getStr("from_address")));

            if (found) {
                System.out.println("PASS - Sender display name filter test passed");
            } else {
                System.out.println("FAIL - Sender display name filter test failed - no matching emails found");
            }
        } else {
            System.out.println("FAIL - Sender display name filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test recipient filter - by email address
     */
    public static void testRecipientFilterByEmailAddress() {
        System.out.println("\nTesting recipient filter - by email address...");

        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("recipientFilter", "<EMAIL>")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);

        Ret result = emailMessagesService.getDashboardEmails(1, 100, params);

        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            boolean found = emails.stream().anyMatch(email -> {
                String toAddress = email.getStr("to_address");
                return toAddress != null && toAddress.contains("<EMAIL>");
            });

            if (found) {
                System.out.println("PASS - Recipient email address filter test passed");
            } else {
                System.out.println("FAIL - Recipient email address filter test failed - no matching emails found");
            }
        } else {
            System.out.println("FAIL - Recipient email address filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test recipient filter - by display name
     */
    public static void testRecipientFilterByDisplayName() {
        System.out.println("\nTesting recipient filter - by display name...");

        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("recipientFilter", "Test User")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);

        Ret result = emailMessagesService.getDashboardEmails(1, 100, params);

        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            boolean found = emails.stream().anyMatch(email -> {
                String toDisplay = email.getStr("to_display");
                String toAddress = email.getStr("to_address");
                return (toDisplay != null && toDisplay.contains("Test User")) ||
                       (toAddress != null && toAddress.contains("<EMAIL>"));
            });

            if (found) {
                System.out.println("PASS - Recipient display name filter test passed");
            } else {
                System.out.println("FAIL - Recipient display name filter test failed - no matching emails found");
            }
        } else {
            System.out.println("FAIL - Recipient display name filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test backward compatibility - ensure original email address filtering still works
     */
    public static void testBackwardCompatibility() {
        System.out.println("\nTesting backward compatibility...");

        // Test simple email address filtering
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("senderFilter", "<EMAIL>")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);

        Ret result = emailMessagesService.getDashboardEmails(1, 100, params);

        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            boolean found = emails.stream().anyMatch(email ->
                "<EMAIL>".equals(email.getStr("from_address")));

            if (found) {
                System.out.println("PASS - Backward compatibility test passed - original email address filtering works");
            } else {
                System.out.println("FAIL - Backward compatibility test failed - original email address filtering not working");
            }
        } else {
            System.out.println("FAIL - Backward compatibility test failed: " + result.getStr("msg"));
        }
    }
}
