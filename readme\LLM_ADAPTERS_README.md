# LLM适配器使用指南

本项目已实现了10个主要大模型API的适配器，支持文本、图片、视频、音频等多种模态的处理。

## 支持的大模型

| 序号 | 大模型 | 适配器类 | 支持模态 | 状态 |
|------|--------|----------|----------|------|
| 1 | OpenAI | 内置支持 | 文本、图片 | ✅ |
| 2 | <PERSON> (Anthropic) | ClaudeAdapter | 文本、图片、文档 | ✅ |
| 3 | Gemini (Google) | GeminiAdapter | 文本、图片、视频、音频 | ✅ |
| 4 | 通义千问 (Qwen) | QwenAdapter | 文本、图片 | ✅ |
| 5 | 智谱AI (GLM) | ZhipuAdapter | 文本、图片 | ✅ |
| 6 | Kimi (月之暗面) | KimiAdapter | 文本、图片 | ✅ |
| 7 | 豆包 (字节跳动) | DoubaoAdapter | 文本、图片 | ✅ |
| 8 | DeepSeek | DeepseekAdapter | 文本、图片 | ✅ |
| 9 | 腾讯元宝 (混元) | TencentAdapter | 文本、图片 | ✅ |
| 10 | 百度文心一言 | BaiduAdapter | 文本、图片 | ✅ |

## 配置说明

### 1. 数据库配置

在 `llm_provider` 表中配置提供商信息：

```sql
INSERT INTO llm_provider (name, api_type, api_base_url, api_key, api_secret, adapter_class, status) VALUES
('openai', 'openai', 'https://api.openai.com/v1/chat/completions', 'your-api-key', '', '', 1),
('claude', 'custom', 'https://api.anthropic.com/v1/messages', 'your-api-key', '', 'cn.jbolt.llm.adapter.ClaudeAdapter', 1),
('gemini', 'custom', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', 'your-api-key', '', 'cn.jbolt.llm.adapter.GeminiAdapter', 1),
('qwen', 'custom', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 'your-api-key', '', 'cn.jbolt.llm.adapter.QwenAdapter', 1),
('zhipu', 'custom', 'https://open.bigmodel.cn/api/paas/v4/chat/completions', 'your-api-key', '', 'cn.jbolt.llm.adapter.ZhipuAdapter', 1),
('kimi', 'custom', 'https://api.moonshot.cn/v1/chat/completions', 'your-api-key', '', 'cn.jbolt.llm.adapter.KimiAdapter', 1),
('doubao', 'custom', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions', 'your-api-key', '', 'cn.jbolt.llm.adapter.DoubaoAdapter', 1),
('deepseek', 'custom', 'https://api.deepseek.com/chat/completions', 'your-api-key', '', 'cn.jbolt.llm.adapter.DeepseekAdapter', 1),
('tencent', 'custom', 'https://hunyuan.tencentcloudapi.com/', 'your-api-key', 'your-secret-key', 'cn.jbolt.llm.adapter.TencentAdapter', 1),
('baidu', 'custom', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions', 'your-api-key', 'your-secret-key', 'cn.jbolt.llm.adapter.BaiduAdapter', 1);
```

### 2. 模型配置

在 `llm_model` 表中配置具体模型：

```sql
INSERT INTO llm_model (provider_id, model_identifier, model_name, status) VALUES
(1, 'gpt-4', 'GPT-4', 1),
(2, 'claude-3-sonnet-20240229', 'Claude 3 Sonnet', 1),
(3, 'gemini-2.5-pro', 'Gemini Pro', 1),
(4, 'qwen-turbo', 'Qwen Turbo', 1),
(5, 'glm-4', 'GLM-4', 1),
(6, 'moonshot-v1-8k', 'Kimi', 1),
(7, 'doubao-pro-4k', 'Doubao Pro', 1),
(8, 'deepseek-chat', 'DeepSeek Chat', 1),
(9, 'hunyuan-lite', 'Hunyuan Lite', 1),
(10, 'ernie-bot-turbo', 'ERNIE Bot Turbo', 1);
```

## 使用示例

### 1. 纯文本对话

```java
// 使用任意模型进行文本对话
String response = LlmService.me().callLlm("openai", "gpt-4", "你好，请介绍一下自己");
```

### 2. 图片理解

```java
// 使用支持图片的模型
List<String> imagePaths = Arrays.asList("/path/to/image1.jpg", "/path/to/image2.png");
String response = LlmService.me().callLlmWithImages("claude", "claude-3-sonnet-20240229", 
    "请描述这些图片的内容", imagePaths);
```

### 3. 多模态内容处理

```java
// 使用Gemini处理视频内容
List<String> videoPaths = Arrays.asList("/path/to/video.mp4");
String response = LlmService.me().callLlmWithImages("gemini", "gemini-2.5-pro", 
    "请分析这个视频的内容", videoPaths);
```

## API密钥获取

### OpenAI
- 官网：https://platform.openai.com/
- 获取API Key：https://platform.openai.com/api-keys

### Claude (Anthropic)
- 官网：https://www.anthropic.com/
- 获取API Key：https://console.anthropic.com/

### Gemini (Google)
- 官网：https://ai.google.dev/
- 获取API Key：https://makersuite.google.com/app/apikey

### 通义千问 (阿里云)
- 官网：https://dashscope.aliyun.com/
- 获取API Key：控制台 -> API-KEY管理

### 智谱AI
- 官网：https://open.bigmodel.cn/
- 获取API Key：控制台 -> API密钥

### Kimi (月之暗面)
- 官网：https://platform.moonshot.cn/
- 获取API Key：控制台 -> API Key

### 豆包 (字节跳动)
- 官网：https://www.volcengine.com/
- 获取API Key：火山引擎控制台

### DeepSeek
- 官网：https://platform.deepseek.com/
- 获取API Key：控制台 -> API Keys

### 腾讯元宝
- 官网：https://cloud.tencent.com/
- 获取密钥：控制台 -> 访问管理 -> API密钥管理

### 百度文心一言
- 官网：https://cloud.baidu.com/
- 获取密钥：控制台 -> 安全认证 -> Access Key

## 特殊说明

### 1. 认证方式差异

- **OpenAI/Claude/DeepSeek/Kimi/豆包/通义千问**：使用 `Authorization: Bearer {api_key}`
- **Gemini**：使用 `x-goog-api-key: {api_key}` 或URL参数
- **智谱AI**：使用JWT token认证
- **腾讯元宝**：使用HMAC-SHA1签名认证
- **百度文心一言**：需要先获取access_token

### 2. 多模态支持

- **Gemini**：支持最全面的多模态（文本、图片、视频、音频）
- **Claude**：支持文本、图片、文档
- **其他模型**：主要支持文本和图片

### 3. 请求格式差异

每个适配器会自动处理不同API的请求格式差异：
- OpenAI格式：标准的messages数组
- Claude格式：分离的system消息和messages
- Gemini格式：contents数组和parts结构
- 通义千问格式：input包装的messages
- 其他格式：各自的特殊要求

## 错误处理

所有适配器都会将错误转换为统一的OpenAI兼容格式：

```json
{
  "error": {
    "message": "错误描述",
    "type": "错误类型",
    "code": "错误代码"
  }
}
```

## 扩展新模型

要添加新的大模型支持：

1. 创建新的适配器类，继承 `AbstractMultimodalAdapter`
2. 实现必要的方法：`convertRequest`、`convertResponse`、`getHeaders`
3. 在 `LlmAdapterFactory` 中注册新适配器
4. 配置数据库中的提供商和模型信息

示例：

```java
public class NewModelAdapter extends AbstractMultimodalAdapter {
    @Override
    public String convertRequest(String model, List<Kv> messages) {
        // 实现请求转换逻辑
    }
    
    @Override
    public String convertResponse(String responseBody) {
        // 实现响应转换逻辑
    }
    
    @Override
    public Map<String, String> getHeaders(String apiKey, String apiSecret) {
        // 实现请求头设置
    }
}
```