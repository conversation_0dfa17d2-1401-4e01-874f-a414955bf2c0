<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载功能修复演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layer@3.5.1/dist/layer.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .demo-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        
        /* 下载全部按钮样式 */
        .download-all-actions {
            display: flex;
            align-items: center;
        }
        
        .download-all-actions .btn {
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .download-all-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }
        
        .download-all-actions .dropdown-menu {
            min-width: 180px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            background-color: white;
            border-radius: 4px;
            padding: 5px 0;
            margin: 2px 0 0 0;
        }
        
        .download-all-actions .dropdown-item {
            padding: 8px 16px;
            font-size: 13px;
            transition: all 0.2s ease;
            display: block;
            width: 100%;
            text-decoration: none;
            color: #333;
        }
        
        .download-all-actions .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }
        
        .download-all-actions .dropdown-item i {
            width: 16px;
            margin-right: 8px;
            text-align: center;
        }
        
        .fix-item {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .fix-item h6 {
            color: #155724;
            margin-bottom: 10px;
        }
        
        .fix-item .fix-before {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .fix-item .fix-after {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
        }
        
        .btn-group.dropdown {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>下载功能修复演示</h1>
        <p>修复了相关文件弹窗下拉菜单无法点击和主界面下载全部按钮只下载部分附件的问题。</p>
        
        <div class="demo-section">
            <div class="demo-title">修复内容总结</div>
            
            <div class="fix-item">
                <h6><i class="fa fa-check-circle text-success"></i> 修复1：下拉菜单无法点击</h6>
                <div class="fix-before">
                    <strong>问题：</strong>使用Bootstrap的data-bs-toggle属性，但可能因为版本不兼容导致无法点击
                </div>
                <div class="fix-after">
                    <strong>解决：</strong>改用JavaScript手动控制下拉菜单显示/隐藏，添加点击外部关闭功能
                </div>
            </div>
            
            <div class="fix-item">
                <h6><i class="fa fa-check-circle text-success"></i> 修复2：默认设置为ZIP压缩下载</h6>
                <div class="fix-before">
                    <strong>问题：</strong>主按钮默认是"下载全部文件"（逐个下载）
                </div>
                <div class="fix-after">
                    <strong>解决：</strong>主按钮改为"打包下载(ZIP)"，提供更好的用户体验
                </div>
            </div>
            
            <div class="fix-item">
                <h6><i class="fa fa-check-circle text-success"></i> 修复3：主界面下载全部按钮问题</h6>
                <div class="fix-before">
                    <strong>问题：</strong>只调用后端接口，可能因为后端问题导致只下载部分附件
                </div>
                <div class="fix-after">
                    <strong>解决：</strong>改用前端批量下载逻辑，确保所有附件都能下载
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">修复后的下载按钮演示</div>
            <div class="mb-3 d-flex justify-content-between align-items-center">
                <span class="badge bg-info">共 6 个文件</span>
                <div class="download-all-actions">
                    <button class="btn btn-primary btn-sm" onclick="demoZipDownload()">
                        <i class="fa fa-file-archive-o"></i> 打包下载(ZIP)
                    </button>
                    <div class="btn-group dropdown" style="margin-left: 5px;">
                        <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" onclick="toggleDemoOptions()" title="更多下载选项">
                            更多选项
                        </button>
                        <ul class="dropdown-menu" id="demoOptionsMenu" style="display: none;">
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoZipDownload(); hideDemoOptions();">
                                <i class="fa fa-file-archive-o"></i> 打包下载(ZIP)
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoIndividualDownload(); hideDemoOptions();">
                                <i class="fa fa-download"></i> 逐个下载
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoImageDownload(); hideDemoOptions();">
                                <i class="fa fa-image"></i> 仅下载图片 (2个)
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoDocDownload(); hideDemoOptions();">
                                <i class="fa fa-file-text-o"></i> 仅下载文档 (4个)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <p class="text-muted small">现在下拉菜单可以正常点击了！主按钮默认为ZIP下载。</p>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">主界面下载全部按钮演示</div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">附件</span>
                <button class="btn btn-sm btn-outline-primary" onclick="demoMainDownloadAll()">
                    <i class="fa fa-download"></i> 下载全部
                </button>
            </div>
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i>
                <strong>改进：</strong>现在主界面的"下载全部"按钮会：
                <ul class="mb-0 mt-2">
                    <li>先获取完整的附件列表</li>
                    <li>显示确认对话框（包含附件数量）</li>
                    <li>使用前端批量下载逻辑</li>
                    <li>确保所有附件都能下载</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">技术改进要点</div>
            <div class="row">
                <div class="col-md-6">
                    <h6>下拉菜单修复：</h6>
                    <ul>
                        <li>移除Bootstrap依赖的data-bs-toggle</li>
                        <li>使用JavaScript手动控制显示/隐藏</li>
                        <li>添加点击外部关闭功能</li>
                        <li>优化CSS定位和层级</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>下载逻辑改进：</h6>
                    <ul>
                        <li>主界面改用前端批量下载</li>
                        <li>ZIP下载增加确认对话框</li>
                        <li>增加30秒超时设置</li>
                        <li>失败时提供备用方案</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 下拉菜单控制
        function toggleDemoOptions() {
            const menu = document.getElementById('demoOptionsMenu');
            if (menu) {
                if (menu.style.display === 'none' || menu.style.display === '') {
                    menu.style.display = 'block';
                } else {
                    menu.style.display = 'none';
                }
            }
        }

        function hideDemoOptions() {
            const menu = document.getElementById('demoOptionsMenu');
            if (menu) {
                menu.style.display = 'none';
            }
        }

        // 点击外部关闭下拉菜单
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.download-all-actions').length) {
                hideDemoOptions();
            }
        });

        // 演示函数
        function demoZipDownload() {
            layer.confirm('确定要打包下载全部 6 个文件吗？', {
                icon: 3,
                title: '确认打包下载',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('正在创建压缩包...', {icon: 16, time: 2000});
                setTimeout(() => {
                    layer.msg('压缩包下载开始！', {icon: 1, time: 2000});
                }, 2000);
            });
        }

        function demoIndividualDownload() {
            layer.confirm('确定要下载全部 6 个文件吗？', {
                icon: 3,
                title: '确认下载',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('开始逐个下载...', {icon: 1, time: 2000});
            });
        }

        function demoImageDownload() {
            layer.confirm('找到 2 个图片文件，确定下载吗？', {
                icon: 3,
                title: '下载图片',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('开始下载图片文件...', {icon: 1, time: 2000});
            });
        }

        function demoDocDownload() {
            layer.confirm('找到 4 个文档文件，确定下载吗？', {
                icon: 3,
                title: '下载文档',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('开始下载文档文件...', {icon: 1, time: 2000});
            });
        }

        function demoMainDownloadAll() {
            layer.msg('正在获取附件列表...', {icon: 16, time: 1000});
            setTimeout(() => {
                layer.confirm('确定要下载全部 8 个附件吗？', {
                    icon: 3,
                    title: '确认下载',
                    btn: ['确定下载', '取消']
                }, function(index) {
                    layer.close(index);
                    layer.msg('开始批量下载附件...', {icon: 1, time: 2000});
                });
            }, 1000);
        }
    </script>
</body>
</html>
