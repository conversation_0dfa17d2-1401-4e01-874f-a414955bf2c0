# Maven打包超长文件名问题解决方案

## 问题描述

在Maven编译打包时出现以下警告：

```
[WARNING] Entry: stonecrm/webapp/upload/email/attachment/20250818/Sélection des Produits Funéraires - OLYMPIA STONE.pdf longer than 100 characters.
[WARNING] Resulting tar file can only be processed successfully by GNU compatible tar commands
```

这些警告是由于邮件附件的文件名过长（超过100个字符）导致的，影响了打包过程的兼容性。

## 解决方案

我们提供了多种解决方案，可以根据需要选择使用：

### 方案1：Maven配置优化（推荐）

#### 1.1 升级Maven Assembly插件版本

已将`maven-assembly-plugin`从3.3.0升级到3.6.0，并添加了长文件名支持配置：

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-assembly-plugin</artifactId>
    <version>3.6.0</version> <!-- 升级到最新版本 -->
    <configuration>
        <!-- 配置tar选项以支持长文件名 -->
        <tarLongFileMode>posix</tarLongFileMode>
        <!-- 其他配置... -->
    </configuration>
</plugin>
```

#### 1.2 在打包时排除长文件名文件

在`package.xml`中添加了排除规则，避免将超长文件名的附件打包：

```xml
<excludes>
    <!-- 排除超长文件名的邮件附件 -->
    <exclude>upload/email/attachment/**/*Sélection des Produits Funéraires*.pdf</exclude>
    <exclude>upload/email/attachment/**/*Prix des granits au M3*.pdf</exclude>
    <exclude>upload/email/attachment/**/*Screenshot_2025-08-26-18-44-57-088_com.tencent.mobileqq.jpg</exclude>
    <!-- 更多排除规则... -->
</excludes>
```

### 方案2：文件名清理脚本

#### 2.1 Linux/Mac脚本

使用`scripts/clean-long-filenames.sh`：

```bash
# 预览模式，查看将要处理的文件
./scripts/clean-long-filenames.sh --dry-run

# 执行清理，设置最大文件名长度为80
./scripts/clean-long-filenames.sh --length 80

# 处理指定目录
./scripts/clean-long-filenames.sh --path src/main/webapp/upload/email/attachment
```

#### 2.2 Windows脚本

使用`scripts/clean-long-filenames.bat`：

```cmd
REM 预览模式
scripts\clean-long-filenames.bat --dry-run

REM 执行清理
scripts\clean-long-filenames.bat --length 80
```

### 方案3：Java工具类（预防性方案）

使用`FileNameUtil`工具类在文件上传时就控制文件名长度：

```java
// 清理并缩短文件名
String cleanedName = FileNameUtil.cleanAndShortenFileName(originalFileName, 80);

// 验证文件名
FileNameUtil.FileNameValidationResult result = FileNameUtil.validateFileName(fileName);
if (!result.isValid()) {
    // 使用建议的文件名
    String suggestedName = result.getSuggestedFileName();
}

// 生成唯一文件名
String uniqueName = FileNameUtil.generateUniqueFileName(originalFileName);
```

## 使用建议

### 立即解决方案

1. **运行清理脚本**（推荐）：
   ```bash
   # Linux/Mac
   chmod +x scripts/clean-long-filenames.sh
   ./scripts/clean-long-filenames.sh --dry-run  # 先预览
   ./scripts/clean-long-filenames.sh            # 执行清理
   
   # Windows
   scripts\clean-long-filenames.bat --dry-run   # 先预览
   scripts\clean-long-filenames.bat             # 执行清理
   ```

2. **重新打包**：
   ```bash
   mvn clean package
   ```

### 长期解决方案

1. **在文件上传时使用FileNameUtil**：
   ```java
   // 在文件上传处理中添加
   String originalFileName = uploadedFile.getOriginalFilename();
   String cleanedFileName = FileNameUtil.cleanAndShortenFileName(originalFileName, 80);
   // 使用cleanedFileName保存文件
   ```

2. **定期运行清理脚本**：
   - 可以将清理脚本加入到CI/CD流程中
   - 或者设置定时任务定期清理

## 脚本功能特性

### clean-long-filenames.sh/bat 功能

- ✅ **预览模式**：`--dry-run`参数可以预览将要处理的文件
- ✅ **可配置长度**：`--length`参数设置最大文件名长度
- ✅ **智能重命名**：保留文件扩展名，添加哈希值避免冲突
- ✅ **备份记录**：自动创建备份目录和重命名日志
- ✅ **统计报告**：显示处理的文件数量和结果
- ✅ **安全检查**：检查目标文件是否已存在，避免覆盖

### FileNameUtil 工具类功能

- ✅ **非法字符清理**：移除或替换文件系统不支持的字符
- ✅ **长度控制**：智能截取文件名并添加哈希值
- ✅ **唯一性保证**：生成带时间戳的唯一文件名
- ✅ **验证功能**：检查文件名是否符合规范
- ✅ **建议修复**：为不合规的文件名提供修复建议

## 配置参数

### 推荐的文件名长度限制

- **保守设置**：60-70字符（兼容性最好）
- **平衡设置**：80字符（推荐，兼容大多数系统）
- **宽松设置**：100字符（可能在某些系统上有问题）

### Maven Assembly插件配置

```xml
<!-- 支持长文件名的tar格式 -->
<tarLongFileMode>posix</tarLongFileMode>

<!-- 其他可选配置 -->
<tarLongFileMode>gnu</tarLongFileMode>     <!-- GNU tar格式 -->
<tarLongFileMode>fail</tarLongFileMode>    <!-- 遇到长文件名时失败 -->
<tarLongFileMode>truncate</tarLongFileMode> <!-- 截断长文件名 -->
<tarLongFileMode>warn</tarLongFileMode>    <!-- 警告但继续 -->
```

## 故障排除

### 常见问题

1. **脚本执行权限问题**：
   ```bash
   chmod +x scripts/clean-long-filenames.sh
   ```

2. **Windows脚本编码问题**：
   - 确保脚本文件使用ANSI编码
   - 或者使用PowerShell版本

3. **Maven插件版本兼容性**：
   - 确保使用Maven 3.6+
   - 检查Java版本兼容性

### 验证解决方案

1. **检查文件名长度**：
   ```bash
   find src/main/webapp/upload/email/attachment -name "*.pdf" -exec basename {} \; | awk '{print length, $0}' | sort -nr
   ```

2. **测试打包**：
   ```bash
   mvn clean package -q | grep -i warning
   ```

3. **验证tar文件**：
   ```bash
   tar -tzf target/stonecrm-release.tar.gz | head -20
   ```

## 总结

通过以上解决方案，可以彻底解决Maven打包时的超长文件名警告问题：

- **立即效果**：清理脚本可以快速处理现有的长文件名
- **长期预防**：FileNameUtil工具类在源头控制文件名长度
- **兼容性提升**：Maven配置优化提高了打包的兼容性

建议采用组合方案：先运行清理脚本解决现有问题，然后在代码中集成FileNameUtil工具类预防未来问题。
