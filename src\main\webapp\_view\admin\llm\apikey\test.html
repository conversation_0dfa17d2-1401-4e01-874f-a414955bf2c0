#@jboltLayout()
#define main()
<div class="jbolt_page" data-key="llm_apikey_test">
<div class="jbolt_page_title">
<div class="row">
    <div class="col-sm-auto"><h1><i class="jbicon2 jbi-wrench"></i>API密钥管理测试页面</h1></div>
</div>
</div>

<div class="jbolt_page_content">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>API接口测试</h5>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-primary btn-sm mb-2" onclick="testProviderOverview()">
                        测试提供商概览
                    </button>
                    <button type="button" class="btn btn-info btn-sm mb-2" onclick="testDetailStats()">
                        测试详细统计
                    </button>
                    <button type="button" class="btn btn-success btn-sm mb-2" onclick="testRealtimeStats()">
                        测试实时状态
                    </button>
                    <button type="button" class="btn btn-warning btn-sm mb-2" onclick="testKeyConfigs()">
                        测试密钥配置
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm mb-2" onclick="testReloadConfig()">
                        测试重载配置
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>数据库检查</h5>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-outline-primary btn-sm mb-2" onclick="checkTables()">
                        检查数据表
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm mb-2" onclick="checkProviders()">
                        检查提供商
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm mb-2" onclick="checkApiKeyManager()">
                        检查密钥管理器
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>测试结果</h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary float-right" onclick="clearResults()">
                        清空结果
                    </button>
                </div>
                <div class="card-body">
                    <pre id="testResults" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;"></pre>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

#define js()
<script>
function log(message) {
    const results = document.getElementById('testResults');
    const timestamp = new Date().toLocaleTimeString();
    results.textContent += `[${timestamp}] ${message}\n`;
    results.scrollTop = results.scrollHeight;
}

function clearResults() {
    document.getElementById('testResults').textContent = '';
}

function testProviderOverview() {
    log('开始测试提供商概览API...');
    
    $.get('/admin/llm/apikey/stats/providerOverview')
        .done(function(data) {
            log('✓ 提供商概览API调用成功');
            log('返回数据: ' + JSON.stringify(data, null, 2));
        })
        .fail(function(xhr, status, error) {
            log('✗ 提供商概览API调用失败: ' + error);
            log('状态码: ' + xhr.status);
            log('响应: ' + xhr.responseText);
        });
}

function testDetailStats() {
    log('开始测试详细统计API...');
    
    $.get('/admin/llm/apikey/stats/datas')
        .done(function(data) {
            log('✓ 详细统计API调用成功');
            log('返回数据: ' + JSON.stringify(data, null, 2));
        })
        .fail(function(xhr, status, error) {
            log('✗ 详细统计API调用失败: ' + error);
            log('状态码: ' + xhr.status);
            log('响应: ' + xhr.responseText);
        });
}

function testRealtimeStats() {
    log('开始测试实时状态API...');
    
    $.get('/admin/llm/apikey/stats/realTimeStats')
        .done(function(data) {
            log('✓ 实时状态API调用成功');
            log('返回数据: ' + JSON.stringify(data, null, 2));
        })
        .fail(function(xhr, status, error) {
            log('✗ 实时状态API调用失败: ' + error);
            log('状态码: ' + xhr.status);
            log('响应: ' + xhr.responseText);
        });
}

function testKeyConfigs() {
    log('开始测试密钥配置API...');
    
    $.get('/admin/llm/apikey/stats/keyConfigs')
        .done(function(data) {
            log('✓ 密钥配置API调用成功');
            log('返回数据: ' + JSON.stringify(data, null, 2));
        })
        .fail(function(xhr, status, error) {
            log('✗ 密钥配置API调用失败: ' + error);
            log('状态码: ' + xhr.status);
            log('响应: ' + xhr.responseText);
        });
}

function testReloadConfig() {
    log('开始测试重载配置API...');
    
    $.post('/admin/llm/apikey/stats/reloadConfig')
        .done(function(data) {
            log('✓ 重载配置API调用成功');
            log('返回数据: ' + JSON.stringify(data, null, 2));
        })
        .fail(function(xhr, status, error) {
            log('✗ 重载配置API调用失败: ' + error);
            log('状态码: ' + xhr.status);
            log('响应: ' + xhr.responseText);
        });
}

function checkTables() {
    log('开始检查数据表...');
    
    // 检查llm_provider表
    $.get('/admin/llmProvider/datas?pageNumber=1&pageSize=5')
        .done(function(data) {
            log('✓ llm_provider表访问正常');
            if (data.data && data.data.list) {
                log('提供商数量: ' + data.data.list.length);
                data.data.list.forEach(function(provider) {
                    log(`- ${provider.name}: ${provider.status ? '启用' : '禁用'}`);
                });
            }
        })
        .fail(function(xhr, status, error) {
            log('✗ llm_provider表访问失败: ' + error);
        });
}

function checkProviders() {
    log('开始检查提供商配置...');
    
    $.get('/admin/llmProvider/datas?pageNumber=1&pageSize=10')
        .done(function(data) {
            if (data.data && data.data.list) {
                log('✓ 找到 ' + data.data.list.length + ' 个提供商');
                
                data.data.list.forEach(function(provider) {
                    log(`提供商: ${provider.name}`);
                    log(`  - API密钥: ${provider.apiKey ? '已配置' : '未配置'}`);
                    log(`  - 频率限制: ${provider.rateLimitPerMinute || '未设置'}/分钟`);
                    log(`  - 状态: ${provider.status ? '启用' : '禁用'}`);
                    log(`  - 优先级: ${provider.priority || '未设置'}`);
                });
            } else {
                log('✗ 未找到提供商数据');
            }
        })
        .fail(function(xhr, status, error) {
            log('✗ 检查提供商失败: ' + error);
        });
}

function checkApiKeyManager() {
    log('开始检查API密钥管理器...');
    
    // 通过实时状态API检查管理器是否正常工作
    $.get('/admin/llm/apikey/stats/realTimeStats')
        .done(function(data) {
            log('✓ API密钥管理器正常工作');
            if (data.data) {
                log('内存中的密钥数量: ' + data.data.length);
                data.data.forEach(function(usage) {
                    log(`- 密钥: ${usage.apiKey}, 当前分钟请求: ${usage.currentMinuteRequests}, 总请求: ${usage.totalRequests}`);
                });
            }
        })
        .fail(function(xhr, status, error) {
            log('✗ API密钥管理器检查失败: ' + error);
        });
}

// 页面加载完成后自动运行基础检查
$(document).ready(function() {
    log('=== API密钥管理测试页面已加载 ===');
    log('点击上方按钮开始测试各项功能');
    log('');
});
</script>
#end
#end
