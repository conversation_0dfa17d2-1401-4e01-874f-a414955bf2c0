package cn.jbolt.mail.gpt.service;

import cn.jbolt.common.model.EmailTranslationConfig;
import cn.jbolt.llm.service.LlmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 可配置的翻译服务
 * 支持主用和备用翻译提供商的自动切换
 */
public class ConfigurableTranslationService {
    
    private static final Logger LOG = LoggerFactory.getLogger(ConfigurableTranslationService.class);
    
    private static ConfigurableTranslationService instance;
    
    private ConfigurableTranslationService() {}
    
    public static ConfigurableTranslationService getInstance() {
        if (instance == null) {
            synchronized (ConfigurableTranslationService.class) {
                if (instance == null) {
                    instance = new ConfigurableTranslationService();
                }
            }
        }
        return instance;
    }
    
    /**
     * 翻译文本，支持主用和备用提供商自动切换
     */
    public String translateText(String text, TranslationType type) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 获取提示词
        String prompt = getPromptByType(type) + text;
        
        // 获取重试配置
        EmailTranslationConfig.RetryConfig retryConfig = EmailTranslationConfig.getRetryConfig();
        
        // 首先尝试主用提供商
        EmailTranslationConfig.TranslationProviderConfig primaryProvider = EmailTranslationConfig.getPrimaryProvider();
        String result = translateWithRetry(prompt, primaryProvider, retryConfig);
        
        if (result != null && !result.trim().isEmpty()) {
            LOG.info("使用主用提供商 {} 翻译成功", primaryProvider.getProvider());
            return result;
        }
        
        // 主用提供商失败，尝试备用提供商
        LOG.warn("主用提供商 {} 翻译失败，尝试备用提供商", primaryProvider.getProvider());
        EmailTranslationConfig.TranslationProviderConfig backupProvider = EmailTranslationConfig.getBackupProvider();
        result = translateWithRetry(prompt, backupProvider, retryConfig);
        
        if (result != null && !result.trim().isEmpty()) {
            LOG.info("使用备用提供商 {} 翻译成功", backupProvider.getProvider());
            return result;
        }
        
        LOG.error("主用和备用提供商都翻译失败");
        return "";
    }
    
    /**
     * 翻译图片，支持主用和备用提供商自动切换
     */
    public String translateImage(List<String> imagePaths) {
        if (imagePaths == null || imagePaths.isEmpty()) {
            return "";
        }
        
        // 获取图片翻译提示词
        String prompt = getPromptByType(TranslationType.IMAGE);
        
        // 获取重试配置
        EmailTranslationConfig.RetryConfig retryConfig = EmailTranslationConfig.getRetryConfig();
        
        // 首先尝试主用提供商
        EmailTranslationConfig.TranslationProviderConfig primaryProvider = EmailTranslationConfig.getPrimaryProvider();
        String result = translateImageWithRetry(prompt, imagePaths, primaryProvider, retryConfig);
        
        if (result != null && !result.trim().isEmpty()) {
            LOG.info("使用主用提供商 {} 翻译图片成功", primaryProvider.getProvider());
            return result;
        }
        
        // 主用提供商失败，尝试备用提供商
        LOG.warn("主用提供商 {} 翻译图片失败，尝试备用提供商", primaryProvider.getProvider());
        EmailTranslationConfig.TranslationProviderConfig backupProvider = EmailTranslationConfig.getBackupProvider();
        result = translateImageWithRetry(prompt, imagePaths, backupProvider, retryConfig);
        
        if (result != null && !result.trim().isEmpty()) {
            LOG.info("使用备用提供商 {} 翻译图片成功", backupProvider.getProvider());
            return result;
        }
        
        LOG.error("主用和备用提供商都翻译图片失败");
        return "";
    }
    
    /**
     * 带重试的文本翻译
     */
    private String translateWithRetry(String prompt, EmailTranslationConfig.TranslationProviderConfig provider, 
                                    EmailTranslationConfig.RetryConfig retryConfig) {
        
        for (int attempt = 1; attempt <= retryConfig.getMaxAttempts(); attempt++) {
            try {
                LOG.debug("尝试使用 {} 翻译，第 {} 次尝试", provider.getProvider(), attempt);
                
                String result = LlmService.me().callLlm(provider.getProvider(), provider.getModel(), prompt);
                
                if (result != null && !result.trim().isEmpty()) {
                    return result;
                }
                
                LOG.warn("第 {} 次翻译尝试返回空结果", attempt);
                
            } catch (Exception e) {
                LOG.error("第 {} 次翻译尝试失败: {}", attempt, e.getMessage());
                
                if (attempt < retryConfig.getMaxAttempts()) {
                    try {
                        Thread.sleep(retryConfig.getDelaySeconds() * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 带重试的图片翻译
     */
    private String translateImageWithRetry(String prompt, List<String> imagePaths, 
                                         EmailTranslationConfig.TranslationProviderConfig provider,
                                         EmailTranslationConfig.RetryConfig retryConfig) {
        
        for (int attempt = 1; attempt <= retryConfig.getMaxAttempts(); attempt++) {
            try {
                LOG.debug("尝试使用 {} 翻译图片，第 {} 次尝试", provider.getProvider(), attempt);
                
                String result = LlmService.me().callLlmWithImages(provider.getProvider(), provider.getModel(), prompt, imagePaths);
                
                if (result != null && !result.trim().isEmpty()) {
                    return result;
                }
                
                LOG.warn("第 {} 次图片翻译尝试返回空结果", attempt);
                
            } catch (Exception e) {
                LOG.error("第 {} 次图片翻译尝试失败: {}", attempt, e.getMessage());
                
                if (attempt < retryConfig.getMaxAttempts()) {
                    try {
                        Thread.sleep(retryConfig.getDelaySeconds() * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 根据翻译类型获取提示词
     */
    private String getPromptByType(TranslationType type) {
        switch (type) {
            case SUBJECT:
                return EmailTranslationConfig.getConfigValue(
                    EmailTranslationConfig.SUBJECT_PROMPT, 
                    "请翻译以下邮件标题,把非中文的翻译成中文："
                );
            case CONTENT:
                return EmailTranslationConfig.getConfigValue(
                    EmailTranslationConfig.CONTENT_PROMPT, 
                    "请翻译以下邮件内容,把非中文的翻译成中文："
                );
            case IMAGE:
                return EmailTranslationConfig.getConfigValue(
                    EmailTranslationConfig.IMAGE_PROMPT, 
                    "请翻译这张图片中的内容,把非中文的翻译成中文："
                );
            default:
                return "请翻译以下内容,把非中文的翻译成中文：";
        }
    }
    
    /**
     * 检查翻译功能是否启用
     */
    public boolean isImageTranslationEnabled() {
        return EmailTranslationConfig.getBooleanConfigValue(
            EmailTranslationConfig.ENABLE_IMAGE_TRANSLATION, true
        );
    }
    
    /**
     * 检查批量处理是否启用
     */
    public boolean isBatchProcessingEnabled() {
        return EmailTranslationConfig.getBooleanConfigValue(
            EmailTranslationConfig.ENABLE_BATCH_PROCESSING, true
        );
    }
    
    /**
     * 获取批量处理大小
     */
    public int getBatchSize() {
        return EmailTranslationConfig.getIntConfigValue(
            EmailTranslationConfig.BATCH_SIZE, 5
        );
    }
    
    /**
     * 获取超时时间
     */
    public int getTimeoutSeconds() {
        return EmailTranslationConfig.getIntConfigValue(
            EmailTranslationConfig.TIMEOUT_SECONDS, 60
        );
    }
    
    /**
     * 翻译类型枚举
     */
    public enum TranslationType {
        SUBJECT,    // 标题翻译
        CONTENT,    // 内容翻译
        IMAGE       // 图片翻译
    }
    
    /**
     * 获取当前配置摘要
     */
    public String getConfigSummary() {
        EmailTranslationConfig.TranslationProviderConfig primary = EmailTranslationConfig.getPrimaryProvider();
        EmailTranslationConfig.TranslationProviderConfig backup = EmailTranslationConfig.getBackupProvider();
        EmailTranslationConfig.RetryConfig retry = EmailTranslationConfig.getRetryConfig();
        
        return String.format(
            "主用: %s/%s, 备用: %s/%s, 重试: %d次, 图片翻译: %s, 批量处理: %s",
            primary.getProvider(), primary.getModel(),
            backup.getProvider(), backup.getModel(),
            retry.getMaxAttempts(),
            isImageTranslationEnabled() ? "启用" : "禁用",
            isBatchProcessingEnabled() ? "启用" : "禁用"
        );
    }
}
