<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载全部文件功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layer@3.5.1/dist/layer.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .demo-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        
        /* 下载全部按钮样式 */
        .download-all-actions {
            display: flex;
            align-items: center;
        }
        
        .download-all-actions .btn {
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .download-all-actions .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }
        
        .download-all-actions .dropdown-menu {
            min-width: 180px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .download-all-actions .dropdown-item {
            padding: 8px 16px;
            font-size: 13px;
            transition: all 0.2s ease;
        }
        
        .download-all-actions .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }
        
        .download-all-actions .dropdown-item i {
            width: 16px;
            margin-right: 8px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-right: 8px;
            width: 16px;
        }
        
        .demo-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>下载全部文件功能演示</h1>
        <p>为相关文件弹窗添加了强大的批量下载功能，支持多种下载方式和文件类型筛选。</p>
        
        <div class="demo-section">
            <div class="demo-title">功能特点</div>
            <ul class="feature-list">
                <li><i class="fa fa-check"></i><strong>一键下载全部</strong>：点击主按钮下载所有文件</li>
                <li><i class="fa fa-check"></i><strong>多种下载方式</strong>：逐个下载或打包ZIP下载</li>
                <li><i class="fa fa-check"></i><strong>按类型筛选</strong>：仅下载图片或仅下载文档</li>
                <li><i class="fa fa-check"></i><strong>下载进度显示</strong>：实时显示下载进度和状态</li>
                <li><i class="fa fa-check"></i><strong>智能间隔</strong>：避免浏览器阻止多个同时下载</li>
                <li><i class="fa fa-check"></i><strong>错误处理</strong>：下载失败时提供重试选项</li>
                <li><i class="fa fa-check"></i><strong>确认对话框</strong>：防止误操作，显示文件数量</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">下载按钮演示</div>
            <div class="mb-3 d-flex justify-content-between align-items-center">
                <span class="badge bg-info">共 8 个文件</span>
                <div class="download-all-actions">
                    <button class="btn btn-primary btn-sm" onclick="demoDownloadAll()">
                        <i class="fa fa-download"></i> 下载全部文件
                    </button>
                    <div class="btn-group" style="margin-left: 5px;">
                        <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false" title="更多下载选项">
                            <span class="visually-hidden">更多选项</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoDownloadAll()">
                                <i class="fa fa-download"></i> 逐个下载
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoDownloadZip()">
                                <i class="fa fa-file-archive-o"></i> 打包下载(ZIP)
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoDownloadImages()">
                                <i class="fa fa-image"></i> 仅下载图片 (3个)
                            </a></li>
                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="demoDownloadDocs()">
                                <i class="fa fa-file-text-o"></i> 仅下载文档 (5个)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <p class="text-muted small">点击按钮或下拉菜单项查看演示效果</p>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">下载流程说明</div>
            <div class="row">
                <div class="col-md-6">
                    <h6>逐个下载流程：</h6>
                    <ol>
                        <li>用户点击"下载全部文件"</li>
                        <li>显示确认对话框（文件数量）</li>
                        <li>显示下载进度提示</li>
                        <li>每500ms下载一个文件</li>
                        <li>实时更新进度显示</li>
                        <li>完成后显示成功提示</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>ZIP打包下载流程：</h6>
                    <ol>
                        <li>用户选择"打包下载(ZIP)"</li>
                        <li>显示"正在创建压缩包"</li>
                        <li>调用后端API创建ZIP文件</li>
                        <li>成功后自动下载ZIP文件</li>
                        <li>失败时回退到逐个下载</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">技术实现要点</div>
            <div class="row">
                <div class="col-md-6">
                    <h6>前端实现：</h6>
                    <ul>
                        <li>使用setTimeout避免浏览器下载限制</li>
                        <li>动态创建&lt;a&gt;标签触发下载</li>
                        <li>Layer.js提供进度提示和确认对话框</li>
                        <li>Bootstrap下拉菜单提供多种选项</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>后端支持（可选）：</h6>
                    <ul>
                        <li>ZIP压缩包创建API</li>
                        <li>文件批量处理接口</li>
                        <li>下载统计和日志记录</li>
                        <li>文件类型和大小验证</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">用户体验优化</div>
            <ul class="feature-list">
                <li><i class="fa fa-user"></i><strong>防误操作</strong>：下载前显示确认对话框</li>
                <li><i class="fa fa-clock-o"></i><strong>进度反馈</strong>：实时显示下载进度和剩余文件数</li>
                <li><i class="fa fa-filter"></i><strong>智能筛选</strong>：按文件类型分类下载</li>
                <li><i class="fa fa-refresh"></i><strong>错误恢复</strong>：下载失败时提供重试选项</li>
                <li><i class="fa fa-mobile"></i><strong>响应式设计</strong>：在移动设备上也能正常使用</li>
            </ul>
        </div>
    </div>

    <script>
        // 演示函数
        function demoDownloadAll() {
            layer.confirm('确定要下载全部 8 个文件吗？', {
                icon: 3,
                title: '确认下载',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                
                // 模拟下载进度
                let progress = 0;
                const total = 8;
                const progressIndex = layer.msg('下载进度: 0/8 (0%)', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });
                
                const timer = setInterval(() => {
                    progress++;
                    const percent = Math.round((progress / total) * 100);
                    layer.msg(`下载进度: ${progress}/${total} (${percent}%)`, {
                        icon: 16,
                        shade: 0.3,
                        time: 0
                    });
                    
                    if (progress >= total) {
                        clearInterval(timer);
                        layer.closeAll('loading');
                        layer.msg('全部 8 个文件下载完成！', {icon: 1, time: 3000});
                    }
                }, 600);
            });
        }
        
        function demoDownloadZip() {
            const loadingIndex = layer.msg('正在创建压缩包...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });
            
            setTimeout(() => {
                layer.close(loadingIndex);
                layer.msg('压缩包下载开始！', {icon: 1, time: 2000});
            }, 2000);
        }
        
        function demoDownloadImages() {
            layer.confirm('找到 3 个图片文件，确定下载吗？', {
                icon: 3,
                title: '下载图片',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('开始下载 3 个图片文件...', {icon: 1, time: 2000});
            });
        }
        
        function demoDownloadDocs() {
            layer.confirm('找到 5 个文档文件，确定下载吗？', {
                icon: 3,
                title: '下载文档',
                btn: ['确定下载', '取消']
            }, function(index) {
                layer.close(index);
                layer.msg('开始下载 5 个文档文件...', {icon: 1, time: 2000});
            });
        }
    </script>
</body>
</html>
