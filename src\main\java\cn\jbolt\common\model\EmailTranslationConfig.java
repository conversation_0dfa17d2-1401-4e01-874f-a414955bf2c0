package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BaseEmailTranslationConfig;

/**
 * 邮件翻译配置模型
 */
public class EmailTranslationConfig extends BaseEmailTranslationConfig<EmailTranslationConfig> {
    
    private static final long serialVersionUID = 1L;
    
    // 配置键常量
    public static final String PRIMARY_PROVIDER = "translation.primary.provider";
    public static final String PRIMARY_MODEL = "translation.primary.model";
    public static final String BACKUP_PROVIDER = "translation.backup.provider";
    public static final String BACKUP_MODEL = "translation.backup.model";
    public static final String MAX_RETRY_ATTEMPTS = "translation.retry.max_attempts";
    public static final String RETRY_DELAY_SECONDS = "translation.retry.delay_seconds";
    public static final String ENABLE_IMAGE_TRANSLATION = "translation.enable_image_translation";
    public static final String ENABLE_BATCH_PROCESSING = "translation.enable_batch_processing";
    public static final String BATCH_SIZE = "translation.batch_size";
    public static final String TIMEOUT_SECONDS = "translation.timeout_seconds";
    public static final String SUBJECT_PROMPT = "translation.subject_prompt";
    public static final String CONTENT_PROMPT = "translation.content_prompt";
    public static final String IMAGE_PROMPT = "translation.image_prompt";
    
    /**
     * 获取配置值
     */
    public static String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }
    
    /**
     * 获取配置值，带默认值
     */
    public static String getConfigValue(String configKey, String defaultValue) {
        EmailTranslationConfig config = new EmailTranslationConfig().dao()
            .findFirst("SELECT * FROM email_translation_config WHERE config_key = ? AND is_active = 1", configKey);
        
        if (config != null && config.getConfigValue() != null) {
            return config.getConfigValue();
        }
        return defaultValue;
    }
    
    /**
     * 获取整数配置值
     */
    public static Integer getIntConfigValue(String configKey, Integer defaultValue) {
        String value = getConfigValue(configKey);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                // 忽略解析错误，返回默认值
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取布尔配置值
     */
    public static Boolean getBooleanConfigValue(String configKey, Boolean defaultValue) {
        String value = getConfigValue(configKey);
        if (value != null) {
            return "true".equalsIgnoreCase(value) || "1".equals(value) || 
                   "yes".equalsIgnoreCase(value) || "on".equalsIgnoreCase(value);
        }
        return defaultValue;
    }
    
    /**
     * 设置配置值
     */
    public static boolean setConfigValue(String configKey, String configValue) {
        return setConfigValue(configKey, configValue, null);
    }
    
    /**
     * 设置配置值，带变更原因
     */
    public static boolean setConfigValue(String configKey, String configValue, String changeReason) {
        try {
            EmailTranslationConfig config = new EmailTranslationConfig().dao()
                .findFirst("SELECT * FROM email_translation_config WHERE config_key = ?", configKey);
            
            if (config != null) {
                // 记录变更历史
                if (changeReason != null) {
                    recordConfigChange(config.getId(), configKey, config.getConfigValue(), configValue, changeReason);
                }
                
                // 更新配置
                config.setConfigValue(configValue);
                return config.update();
            } else {
                // 创建新配置
                config = new EmailTranslationConfig();
                config.setConfigKey(configKey);
                config.setConfigValue(configValue);
                config.setConfigType("STRING");
                config.setIsActive(true);
                return config.save();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 记录配置变更历史
     */
    private static void recordConfigChange(Long configId, String configKey, String oldValue, String newValue, String changeReason) {
        try {
            String sql = "INSERT INTO email_translation_config_history " +
                        "(config_id, config_key, old_value, new_value, change_reason, changed_by) " +
                        "VALUES (?, ?, ?, ?, ?, ?)";
            
            new EmailTranslationConfig().dao().getDb().update(sql, 
                configId, configKey, oldValue, newValue, changeReason, "system");
        } catch (Exception e) {
            // 记录历史失败不影响主流程
            e.printStackTrace();
        }
    }
    
    /**
     * 获取主要翻译提供商配置
     */
    public static TranslationProviderConfig getPrimaryProvider() {
        String provider = getConfigValue(PRIMARY_PROVIDER, "gemini");
        String model = getConfigValue(PRIMARY_MODEL, "gemini-2.0-flash-exp");
        return new TranslationProviderConfig(provider, model, true);
    }
    
    /**
     * 获取备用翻译提供商配置
     */
    public static TranslationProviderConfig getBackupProvider() {
        String provider = getConfigValue(BACKUP_PROVIDER, "openai");
        String model = getConfigValue(BACKUP_MODEL, "gpt-3.5-turbo");
        return new TranslationProviderConfig(provider, model, false);
    }
    
    /**
     * 获取重试配置
     */
    public static RetryConfig getRetryConfig() {
        int maxAttempts = getIntConfigValue(MAX_RETRY_ATTEMPTS, 3);
        int delaySeconds = getIntConfigValue(RETRY_DELAY_SECONDS, 5);
        return new RetryConfig(maxAttempts, delaySeconds);
    }
    
    /**
     * 翻译提供商配置类
     */
    public static class TranslationProviderConfig {
        private String provider;
        private String model;
        private boolean isPrimary;
        
        public TranslationProviderConfig(String provider, String model, boolean isPrimary) {
            this.provider = provider;
            this.model = model;
            this.isPrimary = isPrimary;
        }
        
        public String getProvider() { return provider; }
        public String getModel() { return model; }
        public boolean isPrimary() { return isPrimary; }
        
        @Override
        public String toString() {
            return String.format("%s/%s (%s)", provider, model, isPrimary ? "主用" : "备用");
        }
    }
    
    /**
     * 重试配置类
     */
    public static class RetryConfig {
        private int maxAttempts;
        private int delaySeconds;
        
        public RetryConfig(int maxAttempts, int delaySeconds) {
            this.maxAttempts = maxAttempts;
            this.delaySeconds = delaySeconds;
        }
        
        public int getMaxAttempts() { return maxAttempts; }
        public int getDelaySeconds() { return delaySeconds; }
    }
}
