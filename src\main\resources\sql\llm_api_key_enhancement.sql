-- LLM API密钥增强功能数据库迁移脚本
-- 支持多API密钥轮询和频率限制

-- 1. 为llm_provider表添加频率限制字段
ALTER TABLE llm_provider 
ADD COLUMN rate_limit_per_minute INT DEFAULT 10 COMMENT '每分钟请求限制数量';

-- 2. 为llm_provider表添加多密钥支持说明
ALTER TABLE llm_provider 
MODIFY COLUMN api_key TEXT COMMENT 'API密钥，多个密钥用逗号分隔';

ALTER TABLE llm_provider 
MODIFY COLUMN api_secret TEXT COMMENT 'API密钥(可选)，多个密钥用逗号分隔';

-- 3. 创建API密钥使用统计表
CREATE TABLE IF NOT EXISTS llm_api_key_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_id BIGINT NOT NULL COMMENT '提供商ID',
    api_key_hash VARCHAR(64) NOT NULL COMMENT 'API密钥哈希值（用于标识，不存储原始密钥）',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    success_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    last_request_time DATETIME COMMENT '最后请求时间',
    last_success_time DATETIME COMMENT '最后成功时间',
    last_error_message TEXT COMMENT '最后错误信息',
    is_blocked BOOLEAN DEFAULT FALSE COMMENT '是否被阻塞',
    block_until DATETIME COMMENT '阻塞到什么时候',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_provider_id (provider_id),
    INDEX idx_api_key_hash (api_key_hash),
    INDEX idx_last_request_time (last_request_time),
    UNIQUE KEY uk_provider_key (provider_id, api_key_hash)
) COMMENT='API密钥使用统计表';

-- 4. 创建API密钥请求日志表（可选，用于详细监控）
CREATE TABLE IF NOT EXISTS llm_api_request_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    provider_id BIGINT NOT NULL COMMENT '提供商ID',
    api_key_hash VARCHAR(64) NOT NULL COMMENT 'API密钥哈希值',
    model VARCHAR(100) COMMENT '使用的模型',
    request_tokens INT COMMENT '请求token数',
    response_tokens INT COMMENT '响应token数',
    total_tokens INT COMMENT '总token数',
    request_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    response_time DATETIME COMMENT '响应时间',
    duration_ms INT COMMENT '请求耗时（毫秒）',
    status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '状态：SUCCESS, FAILED, RATE_LIMITED',
    error_message TEXT COMMENT '错误信息',
    INDEX idx_provider_id (provider_id),
    INDEX idx_api_key_hash (api_key_hash),
    INDEX idx_request_time (request_time),
    INDEX idx_status (status)
) COMMENT='API请求日志表';

-- 5. 更新现有提供商的频率限制配置
UPDATE llm_provider SET rate_limit_per_minute = 15 WHERE name = 'gemini';
UPDATE llm_provider SET rate_limit_per_minute = 20 WHERE name = 'openai';
UPDATE llm_provider SET rate_limit_per_minute = 10 WHERE name = 'claude';
UPDATE llm_provider SET rate_limit_per_minute = 30 WHERE name = 'qwen';
UPDATE llm_provider SET rate_limit_per_minute = 20 WHERE name = 'zhipu';

-- 6. 示例：如何配置多个API密钥
-- 假设你有多个Gemini API密钥，可以这样配置：
-- UPDATE llm_provider SET 
--   api_key = 'AIzaXXXXXXXXXXXXXXXX1,AIzaXXXXXXXXXXXXXXXX2,AIzaXXXXXXXXXXXXXXXX3',
--   rate_limit_per_minute = 15
-- WHERE name = 'gemini';

-- 7. 创建视图，方便查看API密钥使用情况
CREATE OR REPLACE VIEW v_llm_api_key_stats AS
SELECT 
    p.id as provider_id,
    p.name as provider_name,
    u.api_key_hash,
    u.total_requests,
    u.success_requests,
    u.failed_requests,
    ROUND(u.success_requests * 100.0 / NULLIF(u.total_requests, 0), 2) as success_rate,
    u.last_request_time,
    u.last_success_time,
    u.is_blocked,
    u.block_until,
    CASE 
        WHEN u.is_blocked AND u.block_until > NOW() THEN '阻塞中'
        WHEN u.last_request_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN '活跃'
        WHEN u.last_request_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '最近使用'
        ELSE '空闲'
    END as status
FROM llm_provider p
LEFT JOIN llm_api_key_usage u ON p.id = u.provider_id
WHERE p.status = 1;

-- 8. 创建存储过程，用于清理过期的请求日志
DELIMITER //
CREATE PROCEDURE CleanupApiRequestLogs(IN days_to_keep INT)
BEGIN
    DECLARE rows_deleted INT DEFAULT 0;
    
    DELETE FROM llm_api_request_log 
    WHERE request_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    SET rows_deleted = ROW_COUNT();
    
    SELECT CONCAT('已删除 ', rows_deleted, ' 条过期日志记录') as result;
END //
DELIMITER ;

-- 9. 创建存储过程，用于重置被阻塞的API密钥
DELIMITER //
CREATE PROCEDURE ResetBlockedApiKeys()
BEGIN
    DECLARE rows_updated INT DEFAULT 0;
    
    UPDATE llm_api_key_usage 
    SET is_blocked = FALSE, block_until = NULL 
    WHERE is_blocked = TRUE AND (block_until IS NULL OR block_until <= NOW());
    
    SET rows_updated = ROW_COUNT();
    
    SELECT CONCAT('已重置 ', rows_updated, ' 个被阻塞的API密钥') as result;
END //
DELIMITER ;

-- 10. 创建定时任务相关的事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- 每小时清理一次过期的阻塞状态
-- CREATE EVENT IF NOT EXISTS reset_blocked_keys
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL ResetBlockedApiKeys();

-- 每天清理30天前的请求日志
-- CREATE EVENT IF NOT EXISTS cleanup_old_logs
-- ON SCHEDULE EVERY 1 DAY
-- STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
-- DO CALL CleanupApiRequestLogs(30);

-- 11. 插入一些示例配置数据
INSERT INTO llm_api_key_usage (provider_id, api_key_hash, total_requests, success_requests, failed_requests)
SELECT 
    id as provider_id,
    MD5(CONCAT(name, '_example')) as api_key_hash,
    0 as total_requests,
    0 as success_requests,
    0 as failed_requests
FROM llm_provider 
WHERE status = 1
ON DUPLICATE KEY UPDATE provider_id = provider_id;

-- 12. 创建索引优化查询性能
CREATE INDEX idx_llm_provider_name_status ON llm_provider(name, status);
CREATE INDEX idx_llm_api_usage_provider_blocked ON llm_api_key_usage(provider_id, is_blocked);

-- 完成提示
SELECT '✅ LLM API密钥增强功能数据库迁移完成！' as message,
       '现在可以使用多API密钥轮询和频率限制功能了。' as description;

-- 使用说明
SELECT '📖 使用说明：' as title,
       '1. 在llm_provider表的api_key字段中用逗号分隔多个密钥' as step1,
       '2. 设置rate_limit_per_minute字段控制每分钟请求限制' as step2,
       '3. 使用LlmApiKeyManager.me().getAvailableApiKey()获取可用密钥' as step3,
       '4. 查看v_llm_api_key_stats视图监控密钥使用情况' as step4;
