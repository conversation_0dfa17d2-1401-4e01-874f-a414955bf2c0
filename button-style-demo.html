<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮样式改进演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .demo-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        .button-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 15px;
        }
        
        /* 相关文件按钮样式优化 */
        .btn-related-files {
            transition: all 0.3s ease;
            border-width: 2px;
            font-weight: 500;
        }

        .btn-related-files.has-files {
            border-color: #17a2b8 !important;
            color: #17a2b8 !important;
            background-color: rgba(23, 162, 184, 0.1) !important;
        }

        .btn-related-files.has-files:hover {
            background-color: #17a2b8 !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }

        .btn-related-files.no-files {
            border-color: #6c757d !important;
            color: #6c757d !important;
            background-color: transparent !important;
        }

        .btn-related-files.no-files:hover {
            background-color: #6c757d !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        /* 调试按钮样式 */
        .btn-outline-secondary {
            border-width: 2px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        /* 确保所有按钮文字清晰可见 */
        .btn-action {
            text-shadow: none !important;
            font-weight: 500 !important;
        }

        .btn-action .btn-text {
            color: inherit !important;
            text-shadow: none !important;
        }

        .btn-action i {
            margin-right: 6px;
            font-size: 14px;
        }

        /* 按钮悬停效果优化 */
        .btn-action:hover {
            text-decoration: none !important;
        }

        .btn-action:hover .btn-text {
            color: inherit !important;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .old-style {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
        }

        .new-style {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>按钮样式改进演示</h1>
        <p>解决了按钮背景色导致文字不清晰的问题，并优化了用户体验。</p>
        
        <div class="demo-section">
            <div class="demo-title">改进前后对比</div>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>改进前</th>
                        <th>改进后</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>有文件时</strong></td>
                        <td class="old-style">
                            <button class="btn btn-info btn-sm">
                                <i class="fa fa-folder"></i> 相关文件
                            </button>
                        </td>
                        <td class="new-style">
                            <button class="btn btn-outline-primary btn-related-files has-files btn-sm">
                                <i class="fa fa-folder"></i> 相关文件
                            </button>
                        </td>
                        <td>使用outline样式，文字更清晰，有悬停效果</td>
                    </tr>
                    <tr>
                        <td><strong>无文件时</strong></td>
                        <td class="old-style">
                            <button class="btn btn-info btn-sm" style="color: #6c757d;">
                                <i class="fa fa-folder-o"></i> 相关文件
                            </button>
                        </td>
                        <td class="new-style">
                            <button class="btn btn-outline-primary btn-related-files no-files btn-sm">
                                <i class="fa fa-folder-o"></i> 相关文件
                            </button>
                        </td>
                        <td>灰色outline样式，清楚表示无内容状态</td>
                    </tr>
                    <tr>
                        <td><strong>调试按钮</strong></td>
                        <td class="old-style">
                            <button class="btn btn-secondary btn-sm">
                                <i class="fa fa-bug"></i> 调试附件
                            </button>
                        </td>
                        <td class="new-style">
                            <button class="btn btn-outline-secondary btn-sm">
                                <i class="fa fa-bug"></i> 调试附件
                            </button>
                        </td>
                        <td>使用outline样式，避免背景色遮挡文字</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">新按钮样式演示</div>
            
            <div class="button-row">
                <button class="btn btn-outline-primary btn-related-files has-files btn-action">
                    <i class="fa fa-folder"></i>
                    <span class="btn-text">相关文件 (有文件)</span>
                </button>
                <span>← 悬停查看效果</span>
            </div>
            
            <div class="button-row">
                <button class="btn btn-outline-primary btn-related-files no-files btn-action">
                    <i class="fa fa-folder-o"></i>
                    <span class="btn-text">相关文件 (无文件)</span>
                </button>
                <span>← 悬停查看效果</span>
            </div>
            
            <div class="button-row">
                <button class="btn btn-outline-secondary btn-action">
                    <i class="fa fa-bug"></i>
                    <span class="btn-text">调试附件</span>
                </button>
                <span>← 悬停查看效果</span>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">改进要点</div>
            <ul>
                <li><strong>可读性提升</strong>：使用outline样式替代实心背景，文字更清晰</li>
                <li><strong>状态区分</strong>：有文件时蓝色，无文件时灰色，状态一目了然</li>
                <li><strong>交互反馈</strong>：悬停时有颜色变化、阴影和轻微位移效果</li>
                <li><strong>视觉层次</strong>：通过颜色和图标变化表达不同状态</li>
                <li><strong>一致性</strong>：所有按钮使用统一的设计语言</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">技术实现</div>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-size: 12px;"><code>/* 相关文件按钮 - 有文件状态 */
.btn-related-files.has-files {
    border-color: #17a2b8 !important;
    color: #17a2b8 !important;
    background-color: rgba(23, 162, 184, 0.1) !important;
}

.btn-related-files.has-files:hover {
    background-color: #17a2b8 !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* 相关文件按钮 - 无文件状态 */
.btn-related-files.no-files {
    border-color: #6c757d !important;
    color: #6c757d !important;
    background-color: transparent !important;
}</code></pre>
        </div>
    </div>
</body>
</html>
