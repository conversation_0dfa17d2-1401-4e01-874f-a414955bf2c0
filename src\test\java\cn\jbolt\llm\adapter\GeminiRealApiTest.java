package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Gemini真实API测试
 * 测试各种实际场景，包括文本翻译、图片分析等
 */
public class GeminiRealApiTest {
    
    // 请替换为你的实际API Key
    private static final String API_KEY = "YOUR_GEMINI_API_KEY_HERE";
    private static final String BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/";
    
    private final GeminiAdapter adapter;
    
    public GeminiRealApiTest() {
        this.adapter = new GeminiAdapter();
    }
    
    public static void main(String[] args) {
        if (API_KEY.equals("YOUR_GEMINI_API_KEY_HERE")) {
            System.out.println("请先设置API Key!");
            System.out.println("1. 访问 https://makersuite.google.com/app/apikey 获取API Key");
            System.out.println("2. 将API Key替换到代码中的API_KEY变量");
            return;
        }
        
        GeminiRealApiTest test = new GeminiRealApiTest();
        
        System.out.println("Gemini真实API测试");
        System.out.println("================\n");
        
        // 1. 测试英文翻译
        test.testEnglishTranslation();
        
        // 2. 测试日文翻译
        test.testJapaneseTranslation();
        
        // 3. 测试韩文翻译
        test.testKoreanTranslation();
        
        // 4. 测试多轮对话
        test.testMultiTurnConversation();
        
        // 5. 测试长文本处理
        test.testLongTextProcessing();
        
        // 6. 测试系统提示词
        test.testSystemPrompt();
        
        // 7. 测试图片分析（如果有图片的话）
        test.testImageAnalysis();
        
        // 8. 测试错误处理
        test.testErrorHandling();
        
        System.out.println("所有测试完成！");
    }
    
    /**
     * 测试1: 英文翻译
     */
    public void testEnglishTranslation() {
        System.out.println("=== 测试1: 英文翻译 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请将以下英文翻译成中文：'Good morning! How are you today? I hope you have a wonderful day!'"));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("翻译结果:");
            System.out.println(formatJson(result));
            
            // 提取翻译内容
            JSONObject response = JSON.parseObject(result);
            if (response.containsKey("choices")) {
                String translation = response.getJSONArray("choices")
                        .getJSONObject(0)
                        .getJSONObject("message")
                        .getString("content");
                System.out.println("翻译内容: " + translation);
            }
            
        } catch (Exception e) {
            System.err.println("英文翻译测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试2: 日文翻译
     */
    public void testJapaneseTranslation() {
        System.out.println("=== 测试2: 日文翻译 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请将以下日文翻译成中文：「おはようございます。今日はいい天気ですね。お元気ですか？」"));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("翻译结果:");
            System.out.println(formatJson(result));
            
        } catch (Exception e) {
            System.err.println("日文翻译测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试3: 韩文翻译
     */
    public void testKoreanTranslation() {
        System.out.println("=== 测试3: 韩文翻译 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请将以下韩文翻译成中文：\"안녕하세요! 오늘 날씨가 정말 좋네요. 어떻게 지내세요?\""));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("翻译结果:");
            System.out.println(formatJson(result));
            
        } catch (Exception e) {
            System.err.println("韩文翻译测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试4: 多轮对话
     */
    public void testMultiTurnConversation() {
        System.out.println("=== 测试4: 多轮对话 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", "你好"));
            messages.add(Kv.by("role", "assistant").set("content", "你好！很高兴见到你。"));
            messages.add(Kv.by("role", "user").set("content", "请教我几个常用的英语问候语"));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("对话结果:");
            System.out.println(formatJson(result));
            
        } catch (Exception e) {
            System.err.println("多轮对话测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试5: 长文本处理
     */
    public void testLongTextProcessing() {
        System.out.println("=== 测试5: 长文本处理 ===");
        
        try {
            String longText = "The quick brown fox jumps over the lazy dog. " +
                    "This is a sample text for testing long text processing capabilities. " +
                    "We want to see how well the Gemini API handles longer inputs and " +
                    "whether our adapter can properly convert both the request and response. " +
                    "This text contains multiple sentences and should be translated accurately " +
                    "while maintaining the original meaning and context.";
            
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请将以下长段英文翻译成中文，保持原意：" + longText));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("长文本翻译结果:");
            System.out.println(formatJson(result));
            
        } catch (Exception e) {
            System.err.println("长文本处理测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试6: 系统提示词
     */
    public void testSystemPrompt() {
        System.out.println("=== 测试6: 系统提示词 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "system")
                    .set("content", "你是一个专业的翻译助手，请准确翻译用户提供的文本，保持原文的语气和风格。"));
            messages.add(Kv.by("role", "user")
                    .set("content", "Please translate: 'I'm absolutely thrilled to meet you!'"));
            
            String result = callGeminiAndConvert("gemini-pro", messages);
            System.out.println("系统提示词测试结果:");
            System.out.println(formatJson(result));
            
        } catch (Exception e) {
            System.err.println("系统提示词测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试7: 图片分析（模拟）
     */
    public void testImageAnalysis() {
        System.out.println("=== 测试7: 图片分析（模拟） ===");
        
        try {
            // 模拟图片处理
            List<String> imagePaths = Arrays.asList("menu.jpg");
            String prompt = "请分析这张餐厅菜单图片，识别其中的英文菜名并翻译成中文";
            
            Object parts = adapter.processImages(imagePaths, prompt);
            System.out.println("图片处理parts结构:");
            System.out.println(formatJson(JSON.toJSONString(parts)));
            
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", parts));
            
            String request = adapter.convertRequest("gemini-pro-vision", messages);
            System.out.println("图片分析请求格式:");
            System.out.println(formatJson(request));
            
            System.out.println("注意: 实际图片分析需要真实的图片文件和gemini-pro-vision模型");
            
        } catch (Exception e) {
            System.err.println("图片分析测试失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试8: 错误处理
     */
    public void testErrorHandling() {
        System.out.println("=== 测试8: 错误处理 ===");
        
        try {
            // 使用无效的模型名称
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user").set("content", "测试错误处理"));
            
            String requestBody = adapter.convertRequest("invalid-model", messages);
            String response = callGeminiApi("invalid-model", requestBody);
            
            System.out.println("错误响应:");
            System.out.println(formatJson(response));
            
            String converted = adapter.convertResponse(response);
            System.out.println("转换后的错误响应:");
            System.out.println(formatJson(converted));
            
        } catch (Exception e) {
            System.out.println("预期的错误: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 调用Gemini API并转换响应
     */
    private String callGeminiAndConvert(String model, List<Kv> messages) throws Exception {
        String requestBody = adapter.convertRequest(model, messages);
        System.out.println("发送请求:");
        System.out.println(formatJson(requestBody));
        
        String response = callGeminiApi(model, requestBody);
        System.out.println("原始响应:");
        System.out.println(formatJson(response));
        
        String converted = adapter.convertResponse(response);
        return converted;
    }
    
    /**
     * 调用Gemini API
     */
    private String callGeminiApi(String model, String requestBody) throws Exception {
        String urlString = BASE_URL + model + ":generateContent?key=" + API_KEY;
        URL url = new URL(urlString);
        
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        int responseCode = connection.getResponseCode();
        BufferedReader reader;
        
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        
        return response.toString();
    }
    
    /**
     * 格式化JSON输出
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
}