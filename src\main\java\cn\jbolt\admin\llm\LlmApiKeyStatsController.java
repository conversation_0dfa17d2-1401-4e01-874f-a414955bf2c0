package cn.jbolt.admin.llm;

import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import cn.jbolt.llm.manager.LlmApiKeyManager;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LLM API密钥使用统计控制器
 */
@CheckPermission("admin.llm")
@UnCheckIfSystemAdmin
@Path(value = "/admin/llm/apikey/stats", viewPath = "/_view/admin/llm/apikey")
public class LlmApiKeyStatsController extends JBoltBaseController {

    /**
     * 统计页面
     */
    public void index() {
        render("stats.html");
    }

    /**
     * 获取API密钥使用统计数据
     */
    public void datas() {
        try {
            // 获取内存中的统计数据
            Map<String, LlmApiKeyManager.ApiKeyUsage> memoryStats = LlmApiKeyManager.me().getUsageStatistics();
            
            // 获取数据库中的统计数据
            String sql = "SELECT p.name as provider_name, u.api_key_hash, u.total_requests, " +
                        "u.success_requests, u.failed_requests, u.last_request_time, " +
                        "u.last_success_time, u.is_blocked, u.block_until, " +
                        "ROUND(u.success_requests * 100.0 / NULLIF(u.total_requests, 0), 2) as success_rate " +
                        "FROM llm_api_key_usage u " +
                        "LEFT JOIN llm_provider p ON u.provider_id = p.id " +
                        "ORDER BY p.name, u.total_requests DESC";
            
            List<Record> dbStats = Db.find(sql);
            
            // 合并内存和数据库统计数据
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (Record record : dbStats) {
                Map<String, Object> item = new HashMap<>();
                item.put("providerName", record.getStr("provider_name"));
                item.put("apiKeyHash", record.getStr("api_key_hash"));
                item.put("totalRequests", record.getInt("total_requests"));
                item.put("successRequests", record.getInt("success_requests"));
                item.put("failedRequests", record.getInt("failed_requests"));
                item.put("successRate", record.getBigDecimal("success_rate"));
                item.put("lastRequestTime", record.getDate("last_request_time"));
                item.put("lastSuccessTime", record.getDate("last_success_time"));
                item.put("isBlocked", record.getBoolean("is_blocked"));
                item.put("blockUntil", record.getDate("block_until"));
                
                // 添加内存中的实时数据
                String keyHash = record.getStr("api_key_hash");
                for (LlmApiKeyManager.ApiKeyUsage usage : memoryStats.values()) {
                    if (keyHash.equals(generateKeyHash(usage.getApiKey()))) {
                        item.put("currentMinuteRequests", usage.getCurrentMinuteRequests());
                        item.put("memoryTotalRequests", usage.getTotalRequests());
                        item.put("memoryLastRequestTime", usage.getLastRequestTime() > 0 ? 
                               new java.util.Date(usage.getLastRequestTime()) : null);
                        item.put("memoryIsBlocked", usage.isBlocked());
                        break;
                    }
                }
                
                result.add(item);
            }
            
            renderJsonData(result);
            
        } catch (Exception e) {
            renderJsonFail("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取提供商概览统计
     */
    public void providerOverview() {
        try {
            String sql = "SELECT p.name as provider_name, " +
                        "COUNT(u.id) as key_count, " +
                        "SUM(u.total_requests) as total_requests, " +
                        "SUM(u.success_requests) as success_requests, " +
                        "SUM(u.failed_requests) as failed_requests, " +
                        "ROUND(SUM(u.success_requests) * 100.0 / NULLIF(SUM(u.total_requests), 0), 2) as success_rate, " +
                        "SUM(CASE WHEN u.is_blocked = 1 THEN 1 ELSE 0 END) as blocked_keys, " +
                        "MAX(u.last_request_time) as last_request_time " +
                        "FROM llm_provider p " +
                        "LEFT JOIN llm_api_key_usage u ON p.id = u.provider_id " +
                        "WHERE p.status = 1 " +
                        "GROUP BY p.id, p.name " +
                        "ORDER BY total_requests DESC";
            
            List<Record> records = Db.find(sql);
            renderJsonData(records);
            
        } catch (Exception e) {
            renderJsonFail("获取提供商概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时使用情况
     */
    public void realTimeStats() {
        try {
            Map<String, LlmApiKeyManager.ApiKeyUsage> stats = LlmApiKeyManager.me().getUsageStatistics();
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map.Entry<String, LlmApiKeyManager.ApiKeyUsage> entry : stats.entrySet()) {
                LlmApiKeyManager.ApiKeyUsage usage = entry.getValue();
                
                Map<String, Object> item = new HashMap<>();
                item.put("apiKey", maskApiKey(usage.getApiKey()));
                item.put("currentMinuteRequests", usage.getCurrentMinuteRequests());
                item.put("totalRequests", usage.getTotalRequests());
                item.put("lastRequestTime", usage.getLastRequestTime() > 0 ? 
                       new java.util.Date(usage.getLastRequestTime()) : null);
                item.put("isBlocked", usage.isBlocked());
                item.put("blockUntil", usage.getBlockUntil() > 0 ? 
                       new java.util.Date(usage.getBlockUntil()) : null);
                
                result.add(item);
            }
            
            renderJsonData(result);
            
        } catch (Exception e) {
            renderJsonFail("获取实时统计失败: " + e.getMessage());
        }
    }

    /**
     * 重置被阻塞的API密钥
     */
    public void resetBlockedKeys() {
        try {
            // 调用存储过程重置数据库中的阻塞状态
            Db.update("CALL ResetBlockedApiKeys()");
            
            // 重新加载内存中的配置
            LlmApiKeyManager.me().reload();
            
            renderJsonSuccess("成功重置被阻塞的API密钥");
            
        } catch (Exception e) {
            renderJsonFail("重置失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期日志
     */
    public void cleanupLogs() {
        int daysToKeep = getParaToInt("days", 30);
        
        try {
            Db.update("CALL CleanupApiRequestLogs(?)", daysToKeep);
            renderJsonSuccess("成功清理 " + daysToKeep + " 天前的日志");
            
        } catch (Exception e) {
            renderJsonFail("清理日志失败: " + e.getMessage());
        }
    }

    /**
     * 重新加载API密钥配置
     */
    public void reloadConfig() {
        try {
            LlmApiKeyManager.me().reload();
            renderJsonSuccess("成功重新加载API密钥配置");
            
        } catch (Exception e) {
            renderJsonFail("重新加载配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取API密钥配置信息
     */
    public void keyConfigs() {
        try {
            List<Map<String, Object>> result = new ArrayList<>();
            
            // 获取所有提供商的密钥配置
            String sql = "SELECT name, api_key, rate_limit_per_minute FROM llm_provider WHERE status = 1";
            List<Record> providers = Db.find(sql);
            
            for (Record provider : providers) {
                String providerName = provider.getStr("name");
                String apiKeys = provider.getStr("api_key");
                int rateLimit = provider.getInt("rate_limit_per_minute");
                
                if (apiKeys != null && !apiKeys.isEmpty()) {
                    String[] keys = apiKeys.split(",");
                    for (int i = 0; i < keys.length; i++) {
                        String key = keys[i].trim();
                        if (!key.isEmpty()) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("providerName", providerName);
                            item.put("apiKey", maskApiKey(key));
                            item.put("keyIndex", i + 1);
                            item.put("rateLimitPerMinute", rateLimit);
                            
                            // 获取该密钥的可用状态
                            List<LlmApiKeyManager.ApiKeyInfo> keyInfos = 
                                LlmApiKeyManager.me().getProviderKeys(providerName);
                            
                            for (LlmApiKeyManager.ApiKeyInfo keyInfo : keyInfos) {
                                if (key.equals(keyInfo.getApiKey())) {
                                    item.put("enabled", keyInfo.isEnabled());
                                    item.put("priority", keyInfo.getPriority());
                                    break;
                                }
                            }
                            
                            result.add(item);
                        }
                    }
                }
            }
            
            renderJsonData(result);
            
        } catch (Exception e) {
            renderJsonFail("获取密钥配置失败: " + e.getMessage());
        }
    }

    /**
     * 生成API密钥的哈希值
     */
    private String generateKeyHash(String apiKey) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(apiKey.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            return apiKey.hashCode() + "";
        }
    }

    /**
     * 掩码显示API密钥
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
