-- 修复Gemini API密钥配置

-- 1. 检查当前gemini提供商配置
SELECT 
    id,
    name,
    api_key,
    api_secret,
    status,
    priority,
    rate_limit_per_minute
FROM llm_provider 
WHERE name = 'gemini';

-- 2. 更新gemini提供商的API密钥
-- 将你的逗号分隔的API密钥直接设置到llm_provider表的api_key字段
UPDATE llm_provider 
SET 
    api_key = 'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    status = 1,
    rate_limit_per_minute = 15
WHERE name = 'gemini';

-- 3. 如果gemini提供商不存在，创建它
INSERT IGNORE INTO llm_provider (
    name, 
    api_type, 
    api_base_url, 
    api_key,
    status, 
    priority, 
    default_model, 
    adapter_class,
    rate_limit_per_minute
) VALUES (
    'gemini', 
    'gemini', 
    'https://generativelanguage.googleapis.com/v1beta/models/', 
    'AIzaSyDSG7Vah868Id-G-6phmTLuQOxlmK_AG9Y,AIzaSyCsw5m_8UHYoKNf96sir-YKScHIBgO9bQo,AIzaSyBaYobHkG262lXJfbgGi_iKtuUBZ9UqDwQ,AIzaSyA7zhImhSLPVaO7AE10T14FKY3fmJA2mQ8,AIzaSyA8f5BVywlxvVOGZ7A__139JNJnmnR8dpU,AIzaSyAfhvaDxor7iRSgX2aH72xLvx3yJlEkpog',
    1, 
    1, 
    'gemini-2.5-pro', 
    'cn.jbolt.llm.adapter.GeminiAdapter',
    15
);

-- 4. 验证更新结果
SELECT 
    id,
    name,
    CASE 
        WHEN api_key IS NOT NULL AND api_key != '' THEN CONCAT(LEFT(api_key, 20), '...(', LENGTH(api_key) - LENGTH(REPLACE(api_key, ',', '')) + 1, '个密钥)')
        ELSE 'NULL或空'
    END as api_key_info,
    status,
    rate_limit_per_minute
FROM llm_provider 
WHERE name = 'gemini';

-- 5. 检查API密钥分割结果（模拟LlmApiKeyManager的逻辑）
SELECT 
    name as provider_name,
    SUBSTRING_INDEX(SUBSTRING_INDEX(api_key, ',', numbers.n), ',', -1) as individual_key,
    numbers.n as key_index
FROM llm_provider
JOIN (
    SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7
) numbers ON CHAR_LENGTH(api_key) - CHAR_LENGTH(REPLACE(api_key, ',', '')) >= numbers.n - 1
WHERE name = 'gemini' AND api_key IS NOT NULL AND api_key != ''
ORDER BY numbers.n;
