package cn.jbolt.llm.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.Kv;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Gemini API 真实调用测试
 * 用于测试实际的API响应格式
 */
public class GeminiApiRealTest {
    
    // 请替换为你的实际API Key
    private static final String API_KEY = "YOUR_GEMINI_API_KEY_HERE";
    private static final String BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/";
    
    private final GeminiAdapter adapter;
    
    public GeminiApiRealTest() {
        this.adapter = new GeminiAdapter();
    }
    
    /**
     * 测试简单文本翻译
     */
    public void testSimpleTranslation() {
        System.out.println("=== 测试简单文本翻译 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请将以下英文翻译成中文：Hello, how are you today?"));
            
            String requestBody = adapter.convertRequest("gemini-pro", messages);
            System.out.println("发送的请求体:");
            System.out.println(formatJson(requestBody));
            
            String response = callGeminiApi("gemini-pro", requestBody);
            System.out.println("\nGemini原始响应:");
            System.out.println(formatJson(response));
            
            String convertedResponse = adapter.convertResponse(response);
            System.out.println("\n转换后的OpenAI格式响应:");
            System.out.println(formatJson(convertedResponse));
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试日文翻译
     */
    public void testJapaneseTranslation() {
        System.out.println("\n=== 测试日文翻译 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "请翻译这段日文：「こんにちは、元気ですか？今日はいい天気ですね。」"));
            
            String requestBody = adapter.convertRequest("gemini-pro", messages);
            System.out.println("发送的请求体:");
            System.out.println(formatJson(requestBody));
            
            String response = callGeminiApi("gemini-pro", requestBody);
            System.out.println("\nGemini原始响应:");
            System.out.println(formatJson(response));
            
            String convertedResponse = adapter.convertResponse(response);
            System.out.println("\n转换后的OpenAI格式响应:");
            System.out.println(formatJson(convertedResponse));
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试多轮对话
     */
    public void testMultiTurnConversation() {
        System.out.println("\n=== 测试多轮对话 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "user")
                    .set("content", "你好，我想学习中文"));
            messages.add(Kv.by("role", "assistant")
                    .set("content", "你好！很高兴帮助你学习中文。你想从哪里开始学习呢？"));
            messages.add(Kv.by("role", "user")
                    .set("content", "请教我一些基本的问候语"));
            
            String requestBody = adapter.convertRequest("gemini-pro", messages);
            System.out.println("发送的请求体:");
            System.out.println(formatJson(requestBody));
            
            String response = callGeminiApi("gemini-pro", requestBody);
            System.out.println("\nGemini原始响应:");
            System.out.println(formatJson(response));
            
            String convertedResponse = adapter.convertResponse(response);
            System.out.println("\n转换后的OpenAI格式响应:");
            System.out.println(formatJson(convertedResponse));
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试系统提示词
     */
    public void testSystemPrompt() {
        System.out.println("\n=== 测试系统提示词 ===");
        
        try {
            List<Kv> messages = new ArrayList<>();
            messages.add(Kv.by("role", "system")
                    .set("content", "你是一个专业的翻译助手，请准确翻译用户提供的文本。"));
            messages.add(Kv.by("role", "user")
                    .set("content", "Please translate: 'The weather is beautiful today.'"));
            
            String requestBody = adapter.convertRequest("gemini-pro", messages);
            System.out.println("发送的请求体:");
            System.out.println(formatJson(requestBody));
            
            String response = callGeminiApi("gemini-pro", requestBody);
            System.out.println("\nGemini原始响应:");
            System.out.println(formatJson(response));
            
            String convertedResponse = adapter.convertResponse(response);
            System.out.println("\n转换后的OpenAI格式响应:");
            System.out.println(formatJson(convertedResponse));
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试错误处理
     */
    public void testErrorHandling() {
        System.out.println("\n=== 测试错误处理 ===");
        
        try {
            // 使用无效的模型名称来触发错误
            String invalidRequestBody = "{\"contents\":[{\"role\":\"user\",\"parts\":[{\"text\":\"test\"}]}]}";
            
            String response = callGeminiApi("invalid-model", invalidRequestBody);
            System.out.println("错误响应:");
            System.out.println(formatJson(response));
            
            String convertedResponse = adapter.convertResponse(response);
            System.out.println("\n转换后的错误响应:");
            System.out.println(formatJson(convertedResponse));
            
        } catch (Exception e) {
            System.err.println("错误测试: " + e.getMessage());
        }
    }
    
    /**
     * 实际调用Gemini API
     */
    private String callGeminiApi(String model, String requestBody) throws Exception {
        if (API_KEY.equals("YOUR_GEMINI_API_KEY_HERE")) {
            throw new IllegalStateException("请先设置有效的API Key");
        }
        
        String urlString = BASE_URL + model + ":generateContent?key=" + API_KEY;
        URL url = new URL(urlString);
        
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        int responseCode = connection.getResponseCode();
        BufferedReader reader;
        
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        
        return response.toString();
    }
    
    /**
     * 格式化JSON输出
     */
    private String formatJson(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return JSON.toJSONString(jsonObject, true);
        } catch (Exception e) {
            return json;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("Gemini API 真实调用测试");
        System.out.println("========================");
        
        if (API_KEY.equals("YOUR_GEMINI_API_KEY_HERE")) {
            System.err.println("错误: 请先在代码中设置有效的API Key");
            System.err.println("获取API Key: https://makersuite.google.com/app/apikey");
            return;
        }
        
        GeminiApiRealTest test = new GeminiApiRealTest();
        
        // 运行各种测试
        test.testSimpleTranslation();
        test.testJapaneseTranslation();
        test.testMultiTurnConversation();
        test.testSystemPrompt();
        test.testErrorHandling();
        
        System.out.println("\n测试完成！");
    }
}